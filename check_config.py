#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查AI配置状态
"""

import sqlite3
import os

def check_ai_config():
    """检查AI配置数据库状态"""
    db_path = 'db/ai_config.db'
    
    if not os.path.exists(db_path):
        print("❌ AI配置数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("=== AI提供商配置 ===")
        cursor.execute('SELECT * FROM ai_providers')
        providers = cursor.fetchall()
        
        if not providers:
            print("❌ 没有配置任何AI提供商")
            return False
        
        active_providers = []
        for provider in providers:
            status = "✅ 活跃" if provider["is_active"] else "❌ 未激活"
            api_key_status = "已设置" if provider["api_key"] and provider["api_key"] != "在此处填入您的真实API密钥" else "未设置"
            print(f"ID: {provider['id']}, 名称: {provider['name']}, 状态: {status}")
            print(f"  API密钥: {api_key_status}")
            print(f"  基础URL: {provider['base_url']}")
            
            if provider["is_active"]:
                active_providers.append(provider)
        
        if not active_providers:
            print("❌ 没有活跃的AI提供商")
            return False
        
        print("\n=== 模型配置 ===")
        cursor.execute('SELECT * FROM ai_models')
        models = cursor.fetchall()
        
        if not models:
            print("❌ 没有配置任何模型")
            return False
        
        enabled_models = []
        for model in models:
            status = "✅ 启用" if model["is_enabled"] else "❌ 未启用"
            print(f"类型: {model['model_type']}, 状态: {status}, 提供商ID: {model['provider_id']}")
            
            if model["is_enabled"]:
                enabled_models.append(model)
        
        if not enabled_models:
            print("❌ 没有启用的模型")
            return False
        
        # 检查text模型是否启用
        text_model_enabled = any(m["model_type"] == "text" and m["is_enabled"] for m in models)
        if not text_model_enabled:
            print("❌ 文本模型未启用")
            return False
        
        conn.close()
        print("\n✅ AI配置检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 检查AI配置时出错: {e}")
        return False

def check_task_status():
    """检查任务状态"""
    db_path = 'db/task_queue.db'
    
    if not os.path.exists(db_path):
        print("❌ 任务队列数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("\n=== 任务状态统计 ===")
        cursor.execute('SELECT status, COUNT(*) as count FROM task_queue GROUP BY status')
        status_counts = cursor.fetchall()
        
        for row in status_counts:
            print(f"{row['status']}: {row['count']} 个任务")
        
        # 检查最近的任务
        print("\n=== 最近5个任务 ===")
        cursor.execute('SELECT * FROM task_queue ORDER BY created_at DESC LIMIT 5')
        recent_tasks = cursor.fetchall()
        
        for task in recent_tasks:
            print(f"ID: {task['id'][:8]}..., 状态: {task['status']}, 跟踪号: {task['tracking_number']}")
            print(f"  创建时间: {task['created_at']}")
            if task['error_message']:
                print(f"  错误信息: {task['error_message'][:100]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查任务状态时出错: {e}")

if __name__ == "__main__":
    print("🔍 开始检查系统配置...")
    
    # 检查AI配置
    ai_config_ok = check_ai_config()
    
    # 检查任务状态
    check_task_status()
    
    if not ai_config_ok:
        print("\n❌ 系统配置有问题，这可能是导致数据获取失败的原因")
        print("建议：")
        print("1. 检查AI配置是否正确设置")
        print("2. 确保API密钥有效")
        print("3. 确保模型配置正确")
    else:
        print("\n✅ 基础配置检查通过，问题可能在其他地方")

#!/usr/bin/env python3
"""获取新令牌并测试任务创建"""

import requests
import json

def get_new_token():
    """获取新的认证令牌"""
    try:
        response = requests.post(
            'http://127.0.0.1:8080/api/v1/auth/login', 
            json={'invite_code': 'TEST001'}
        )
        print(f"登录响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"解析后的JSON: {result}")
            
            # 尝试不同的字段名
            token = result.get('access_token') or result.get('token') or result.get('data', {}).get('access_token')
            
            if token:
                print(f"获取到新令牌: {token[:50]}...")
                return token
            else:
                print("响应中没有找到令牌字段")
                return None
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"获取令牌出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_task_creation(token):
    """测试任务创建"""
    if not token:
        print("没有有效令牌，无法测试")
        return
    
    # 创建任务数据
    task_data = {
        "container_number": "MEDUJ0618622",
        "carrier_code": "MSC", 
        "priority": "normal"
    }
    
    print(f"创建任务: {task_data}")
    
    # 发送请求
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:8080/api/v1/tasks/quick-create",
            json=task_data,
            headers=headers
        )
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"任务创建成功!")
            return result
        else:
            print(f"任务创建失败")
            return None
    except Exception as e:
        print(f"请求出错: {e}")
        return None

def main():
    print("=== 获取新令牌并测试任务创建 ===")
    
    # 1. 获取新令牌
    token = get_new_token()
    
    # 2. 测试任务创建
    if token:
        result = test_task_creation(token)
        if result:
            print("\n任务创建测试成功!")
        else:
            print("\n任务创建测试失败!")
    else:
        print("\n无法获取令牌，测试中止")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实提单号测试Web端数据同步功能
"""

import requests
import json
import time
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def login_and_get_token():
    """登录并获取访问令牌"""
    login_data = {"invite_code": "TEST001"}
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('access_token'):
            print(f"✅ 登录成功")
            return result['access_token']
    
    print(f"❌ 登录失败: {response.text}")
    return None

def create_test_task(token, container_number):
    """创建测试任务"""
    headers = {"Authorization": f"Bearer {token}"}
    task_data = {
        "container_number": container_number,
        "priority": "normal"
    }
    
    response = requests.post(f"{API_BASE}/tasks/create", json=task_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 任务创建成功: {result.get('task_id')}")
        return result
    else:
        print(f"❌ 任务创建失败: {response.text}")
        return None

def get_my_tasks(token):
    """获取我的任务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/tasks/my-tasks", headers=headers)
    
    if response.status_code == 200:
        tasks = response.json()
        return tasks
    else:
        print(f"❌ 获取任务失败: {response.text}")
        return []

def display_detailed_task_info(task):
    """显示详细的任务信息"""
    print(f"\n📦 详细任务信息:")
    print(f"  🆔 任务ID: {task.get('task_id')}")
    print(f"  📋 集装箱号: {task.get('container_number')}")
    print(f"  🚢 承运人: {task.get('carrier_code')} - {task.get('carrier_name', 'N/A')}")
    print(f"  📊 主状态: {task.get('status')}")
    print(f"  🚚 货运状态: {task.get('shipment_status', 'N/A')}")
    print(f"  🌐 网页抓取: {task.get('scraping_status', 'N/A')}")
    print(f"  🤖 AI分析: {task.get('ai_status', 'N/A')}")
    print(f"  🗓️  预计到港: {task.get('estimated_arrival_time', 'N/A')}")
    print(f"  📸 截图: {'有' if task.get('evidence_screenshot') else '无'}")
    
    shipment_dates = task.get('shipment_dates', [])
    print(f"  🛣️  物流节点: {len(shipment_dates)}个")
    
    if shipment_dates:
        print(f"     节点详情:")
        for i, date_info in enumerate(shipment_dates, 1):
            date_type = date_info.get('date_type', 'N/A')
            status = date_info.get('status', 'N/A')
            location = date_info.get('location', 'N/A')
            actual_date = date_info.get('actual_date') or date_info.get('planned_date', 'N/A')
            print(f"       {i}. {date_type} | {status} | {location} | {actual_date}")
    
    if task.get('ai_analysis_result'):
        print(f"  🧠 AI分析结果: 已完成")
    else:
        print(f"  🧠 AI分析结果: 未完成")

def main():
    """主测试流程"""
    print("🚀 使用真实提单号测试Web端数据同步")
    print("=" * 60)
    
    # 登录
    token = login_and_get_token()
    if not token:
        return
    
    # 获取当前任务列表
    print(f"\n📋 当前任务列表:")
    current_tasks = get_my_tasks(token)
    
    if current_tasks:
        print(f"发现 {len(current_tasks)} 个现有任务:")
        for i, task in enumerate(current_tasks, 1):
            print(f"  {i}. {task['container_number']} - {task['status']} - 抓取:{task.get('scraping_status', 'N/A')} - AI:{task.get('ai_status', 'N/A')}")
            
        # 查看最新任务的详细信息
        latest_task = current_tasks[0]
        print(f"\n🔍 最新任务详情:")
        display_detailed_task_info(latest_task)
        
        # 如果有已完成的任务，展示其完整信息
        completed_tasks = [t for t in current_tasks if t.get('status') in ['completed', 'partially_completed']]
        if completed_tasks:
            print(f"\n🎉 已完成任务示例:")
            display_detailed_task_info(completed_tasks[0])
    else:
        print("当前没有任务")
    
    # 创建一个新的真实提单号任务
    print(f"\n🆕 创建新的真实提单号任务...")
    
    # 使用用户指定的真实提单号
    real_container_number = "MEDUJ0616089"  # MSC真实提单号
    
    print(f"\n尝试创建任务: {real_container_number}")
    new_task = create_test_task(token, real_container_number)
    if new_task:
        print(f"✅ 创建成功，等待处理...")
        time.sleep(2)  # 短暂等待
        
        # 重新获取任务列表查看新任务
        updated_tasks = get_my_tasks(token)
        new_created_task = None
        for task in updated_tasks:
            if task.get('container_number') == real_container_number and task.get('task_id') == new_task.get('task_id'):
                new_created_task = task
                break
        
        if new_created_task:
            print(f"✅ 找到新创建的任务:")
            display_detailed_task_info(new_created_task)
        else:
            print(f"⚠️ 未找到新创建的任务")
    else:
        print(f"❌ 任务创建失败")
    
    print(f"\n🎯 测试完成!")
    print(f"💡 提示: 运行 python monitor_task_progress.py 可以实时监控任务进度")

if __name__ == "__main__":
    main()

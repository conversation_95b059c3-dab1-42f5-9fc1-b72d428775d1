#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务队列管理器
用于创建、更新和管理提单号/箱号查询任务
"""

import sqlite3
import uuid
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Tuple
import json
from db.connection_manager import get_connection_manager

# 定义东八区时区
BEIJING_TZ = timezone(timedelta(hours=8))

def get_beijing_time() -> datetime:
    """获取东八区（北京时间）当前时间"""
    return datetime.now(BEIJING_TZ)

def get_beijing_time_str() -> str:
    """获取东八区时间的ISO格式字符串"""
    return get_beijing_time().isoformat()

class TaskManager:
    """
    任务队列管理器类
    """
    
    def __init__(self, db_path: str = "db/task_queue.db"):
        self.db_path = db_path
    
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        print(f"[DB_LOG] 正在连接数据库: {self.db_path}")
        # 使用连接复用器，返回包装连接；调用 close() 将归还到池中
        conn = get_connection_manager(self.db_path).acquire()
        print(f"[DB_LOG] 数据库连接成功: {self.db_path}")
        return conn
    
    def create_task(self, 
                   tracking_number: str,
                   task_type: str,
                   creator_id: str,
                   creator_name: str = None,
                   carrier: str = None,
                   priority: int = 0,
                   remarks: str = None,
                   task_stage: str = 'scraping',
                   parent_task_id: str = None,
                   raw_data_path: str = None) -> str:
        """
        创建新的查询任务
        
        Args:
            tracking_number: 跟踪号码（提单号或箱号）
            task_type: 任务类型（'bill_of_lading' 或 'container'）
            creator_id: 任务发起人ID
            creator_name: 任务发起人姓名
            carrier: 承运人/船公司
            priority: 任务优先级（数字越大优先级越高）
            remarks: 备注信息
            task_stage: 任务阶段（'scraping' 或 'ai_analysis'）
            parent_task_id: 父任务ID（AI分析任务关联的抓取任务ID）
            raw_data_path: 原始数据存储路径
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        
        # 生成任务名称
        type_name = "提单号" if task_type == "bill_of_lading" else "箱号"
        stage_name = "网页抓取" if task_stage == "scraping" else "AI分析"
        task_name = f"{tracking_number}{type_name}{stage_name}任务"
        if carrier:
            task_name = f"{carrier}-{task_name}"
        
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                INSERT INTO task_queue (
                    id, task_name, task_type, tracking_number, carrier,
                    creator_id, creator_name, priority, remarks, task_stage, parent_task_id, raw_data_path
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                task_id, task_name, task_type, tracking_number, carrier,
                creator_id, creator_name, priority, remarks, task_stage, parent_task_id, raw_data_path
            )
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果: 影响行数 {affected_rows}")
            
            conn.commit()
            print(f"[DB_LOG] 事务提交成功 - 库:task_queue.db 表:task_queue")
            print(f"[SUCCESS] 任务创建成功: {task_name} (ID: {task_id})")
            return task_id
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 创建任务失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:task_queue.db")
            raise
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def update_task_status(self, task_id: str, status: str,
                          error_message: str = None,
                          result_summary: str = None,
                          result_data: dict = None) -> bool:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态（pending, processing, completed, cancelled）
            error_message: 错误信息（可选）
            result_summary: 结果摘要（可选）
            result_data: 结果数据（可选，字典格式，将转换为JSON存储）

        Returns:
            bool: 更新是否成功
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 根据状态设置相应的时间字段
            update_fields = ["status = ?"]
            params = [status]
            
            if status == "processing":
                update_fields.append("started_at = ?")
                params.append(get_beijing_time_str())
            elif status == "completed":
                update_fields.append("completed_at = ?")
                params.append(get_beijing_time_str())
            elif status == "cancelled":
                update_fields.append("cancelled_at = ?")
                params.append(get_beijing_time_str())
            
            if error_message:
                update_fields.append("error_message = ?")
                params.append(error_message)
            
            if result_summary:
                update_fields.append("result_summary = ?")
                params.append(result_summary)

            if result_data:
                import json
                update_fields.append("result = ?")
                params.append(json.dumps(result_data, ensure_ascii=False))

            params.append(task_id)
            
            sql = f"UPDATE task_queue SET {', '.join(update_fields)} WHERE id = ?"
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果: 影响行数 {affected_rows}")
            
            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:task_queue.db 表:task_queue")
                print(f"[SUCCESS] 任务状态更新成功: {task_id} -> {status}")
                return True
            else:
                print(f"[DB_LOG] 更新失败：未找到匹配的记录 - 库:task_queue.db 表:task_queue")
                print(f"[WARNING] 未找到任务: {task_id}")
                return False
                
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 更新任务状态失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:task_queue.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_pending_tasks(self, limit: int = 10) -> List[Dict]:
        """
        获取待处理的任务（按优先级和创建时间排序）
        
        Args:
            limit: 返回任务数量限制
            
        Returns:
            List[Dict]: 待处理任务列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                SELECT * FROM task_queue 
                WHERE status = 'pending' 
                ORDER BY priority DESC, created_at ASC 
                LIMIT ?
            """
            params = (limit,)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(rows)} 行数据")
            
            tasks = [dict(row) for row in rows]
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return tasks
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取待处理任务失败: {e}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_task_by_id(self, task_id: str) -> Optional[Dict]:
        """
        根据ID获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务详情，如果不存在返回None
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = "SELECT * FROM task_queue WHERE id = ?"
            params = (task_id,)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            row = cursor.fetchone()
            
            if row:
                print(f"[DB_LOG] 查询结果: 找到 1 行数据")
                result = dict(row)
            else:
                print(f"[DB_LOG] 查询结果: 未找到匹配数据")
                result = None
                
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return result
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取任务详情失败: {e}")
            return None
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_tasks_by_status(self, status: str, limit: int = 50) -> List[Dict]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            limit: 返回任务数量限制
            
        Returns:
            List[Dict]: 任务列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                SELECT * FROM task_queue 
                WHERE status = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            """
            params = (status, limit)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(rows)} 行数据")
            
            tasks = [dict(row) for row in rows]
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return tasks
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取任务列表失败: {e}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def cancel_task(self, task_id: str, cancelled_by: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            cancelled_by: 取消人ID
            
        Returns:
            bool: 取消是否成功
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                UPDATE task_queue 
                SET status = 'cancelled', cancelled_by = ?, cancelled_at = ?
                WHERE id = ? AND status IN ('pending', 'processing')
            """
            params = (cancelled_by, get_beijing_time_str(), task_id)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果: 影响行数 {affected_rows}")
            
            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:task_queue.db 表:task_queue")
                print(f"[SUCCESS] 任务取消成功: {task_id}")
                return True
            else:
                print(f"[DB_LOG] 取消失败：未找到匹配的记录或任务已完成 - 库:task_queue.db 表:task_queue")
                print(f"[WARNING] 无法取消任务（任务不存在或已完成）: {task_id}")
                return False
                
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 取消任务失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:task_queue.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def retry_task(self, task_id: str, reason: str = None, reset_attempts: bool = False) -> bool:
        """
        重试任务
        
        Args:
            task_id: 任务ID
            reason: 重试原因
            reset_attempts: 是否重置重试次数
            
        Returns:
            bool: 重试是否成功
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 检查任务是否存在且可重试
            check_sql = "SELECT status FROM task_queue WHERE id = ?"
            cursor.execute(check_sql, (task_id,))
            result = cursor.fetchone()
            
            if not result:
                print(f"[WARNING] 任务不存在: {task_id}")
                return False
            
            current_status = result['status']
            if current_status not in ('failed', 'cancelled', 'completed'):
                print(f"[WARNING] 任务当前状态不允许重试: {task_id} - {current_status}")
                return False
            
            # 重置任务状态为pending
            update_fields = ["status = ?", "started_at = NULL", "completed_at = NULL", 
                           "cancelled_at = NULL", "error_message = NULL"]
            params = ["pending"]
            
            if reason:
                update_fields.append("remarks = ?")
                params.append(f"重试原因: {reason}")
            
            if reset_attempts:
                update_fields.append("retry_count = 0")
            
            params.append(task_id)
            
            sql = f"UPDATE task_queue SET {', '.join(update_fields)} WHERE id = ?"
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果: 影响行数 {affected_rows}")
            
            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:task_queue.db 表:task_queue")
                print(f"[SUCCESS] 任务重试成功: {task_id}")
                return True
            else:
                print(f"[DB_LOG] 重试失败：未找到匹配的记录 - 库:task_queue.db 表:task_queue")
                return False
                
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 重试任务失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:task_queue.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_task_logs(self, task_id: str, limit: int = 100) -> Optional[Dict]:
        """
        获取任务日志
        
        Args:
            task_id: 任务ID
            limit: 日志条数限制
            
        Returns:
            Optional[Dict]: 日志信息，如果任务不存在返回None
        """
        # 检查任务是否存在
        task = self.get_task_by_id(task_id)
        if not task:
            return None
        
        # 简化版日志，基于任务状态和时间生成
        logs = []
        
        # 添加创建日志
        if task.get('created_at'):
            logs.append({
                'timestamp': task['created_at'],
                'level': 'INFO',
                'message': f"任务创建: {task.get('task_name', '')}",
                'details': {
                    'tracking_number': task.get('tracking_number'),
                    'carrier': task.get('carrier'),
                    'creator': task.get('creator_name')
                }
            })
        
        # 添加开始日志
        if task.get('started_at'):
            logs.append({
                'timestamp': task['started_at'],
                'level': 'INFO',
                'message': '任务开始处理',
                'details': {'status': 'processing'}
            })
        
        # 添加完成/取消/错误日志
        if task.get('completed_at'):
            logs.append({
                'timestamp': task['completed_at'],
                'level': 'INFO',
                'message': '任务处理完成',
                'details': {
                    'status': 'completed',
                    'result_summary': task.get('result_summary')
                }
            })
        elif task.get('cancelled_at'):
            logs.append({
                'timestamp': task['cancelled_at'],
                'level': 'WARNING',
                'message': '任务已取消',
                'details': {'status': 'cancelled'}
            })
        elif task.get('error_message'):
            logs.append({
                'timestamp': task.get('updated_at', task.get('created_at')),
                'level': 'ERROR',
                'message': '任务处理失败',
                'details': {
                    'status': 'failed',
                    'error_message': task.get('error_message')
                }
            })
        
        # 按时间降序排序并限制数量
        logs.sort(key=lambda x: x['timestamp'], reverse=True)
        logs = logs[:limit]
        
        return {
            'task_id': task_id,
            'logs': logs,
            'total': len(logs)
        }
    
    def get_statistics(self) -> Dict:
        """
        获取详细统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            # 获取基础统计
            basic_stats = self.get_task_statistics()
            
            # 扩展统计信息
            return {
                'today': {
                    'total_queries': basic_stats.get('today_created', 0),
                    'successful_queries': 0,  # 简化版，可以后续完善
                    'failed_queries': 0,
                    'pending_queries': basic_stats.get('status_distribution', {}).get('pending', 0),
                    'average_duration': 0.0,
                    'success_rate': 0.0
                },
                'this_week': {
                    'total_queries': basic_stats.get('total_tasks', 0),
                    'successful_queries': basic_stats.get('status_distribution', {}).get('completed', 0),
                    'failed_queries': basic_stats.get('status_distribution', {}).get('failed', 0),
                    'pending_queries': basic_stats.get('status_distribution', {}).get('pending', 0),
                    'average_duration': 0.0,
                    'success_rate': 0.0
                },
                'this_month': {
                    'total_queries': basic_stats.get('total_tasks', 0),
                    'successful_queries': basic_stats.get('status_distribution', {}).get('completed', 0),
                    'failed_queries': basic_stats.get('status_distribution', {}).get('failed', 0),
                    'pending_queries': basic_stats.get('status_distribution', {}).get('pending', 0),
                    'average_duration': 0.0,
                    'success_rate': 0.0
                },
                'all_time': {
                    'total_queries': basic_stats.get('total_tasks', 0),
                    'successful_queries': basic_stats.get('status_distribution', {}).get('completed', 0),
                    'failed_queries': basic_stats.get('status_distribution', {}).get('failed', 0),
                    'pending_queries': basic_stats.get('status_distribution', {}).get('pending', 0),
                    'average_duration': 0.0,
                    'success_rate': 0.0
                }
            }
            
        except Exception as e:
            print(f"[ERROR] 获取统计信息失败: {e}")
            return {}

    def get_task_statistics(self) -> Dict:
        """
        获取任务统计信息
        
        Returns:
            Dict: 统计信息
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 总体统计
            sql1 = """
                SELECT 
                    status,
                    COUNT(*) as count
                FROM task_queue 
                GROUP BY status
            """
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句(状态统计): {sql1.strip()}")
            
            cursor.execute(sql1)
            status_rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果(状态统计): 返回 {len(status_rows)} 行数据")
            status_stats = {row['status']: row['count'] for row in status_rows}
            
            # 今日统计
            sql2 = """
                SELECT 
                    COUNT(*) as today_created
                FROM task_queue 
                WHERE DATE(created_at) = DATE('now')
            """
            print(f"[DB_LOG] SQL语句(今日创建): {sql2.strip()}")
            cursor.execute(sql2)
            today_created_row = cursor.fetchone()
            today_created = today_created_row['today_created']
            print(f"[DB_LOG] 查询结果(今日创建): {today_created}")
            
            # 今日完成
            sql3 = """
                SELECT 
                    COUNT(*) as today_completed
                FROM task_queue 
                WHERE DATE(completed_at) = DATE('now')
            """
            print(f"[DB_LOG] SQL语句(今日完成): {sql3.strip()}")
            cursor.execute(sql3)
            today_completed_row = cursor.fetchone()
            today_completed = today_completed_row['today_completed']
            print(f"[DB_LOG] 查询结果(今日完成): {today_completed}")
            
            print(f"[DB_LOG] 统计查询成功 - 库:task_queue.db 表:task_queue")
            
            return {
                'status_distribution': status_stats,
                'today_created': today_created,
                'today_completed': today_completed,
                'total_tasks': sum(status_stats.values())
            }
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取统计信息失败: {e}")
            return {}
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def batch_create_tasks(self, tasks_data: List[Dict]) -> List[str]:
        """
        批量创建任务
        
        Args:
            tasks_data: 任务数据列表，每个元素包含创建任务所需的参数
            
        Returns:
            List[str]: 创建的任务ID列表
        """
        task_ids = []
        
        for task_data in tasks_data:
            try:
                task_id = self.create_task(**task_data)
                task_ids.append(task_id)
            except Exception as e:
                print(f"[ERROR] 批量创建任务失败（跳过）: {task_data.get('tracking_number', 'Unknown')} - {e}")
                continue
        
        print(f"[SUCCESS] 批量创建任务完成: {len(task_ids)}/{len(tasks_data)} 个任务创建成功")
        return task_ids
    
    # 两阶段任务支持方法
    def get_pending_tasks_by_stage(self, task_stage: str, limit: int = 10) -> List[Dict]:
        """
        根据任务阶段获取待处理的任务
        
        Args:
            task_stage: 任务阶段（'scraping' 或 'ai_analysis'）
            limit: 返回任务数量限制
            
        Returns:
            List[Dict]: 待处理任务列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                SELECT * FROM task_queue 
                WHERE status = 'pending' AND task_stage = ?
                ORDER BY priority DESC, created_at ASC 
                LIMIT ?
            """
            params = (task_stage, limit)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(rows)} 行数据")
            
            tasks = [dict(row) for row in rows]
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return tasks
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取{task_stage}阶段待处理任务失败: {e}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def create_ai_analysis_task(self, parent_task_id: str, raw_data_path: str, 
                               priority: int = 0, remarks: str = None) -> str:
        """
        基于抓取任务创建AI分析任务
        
        Args:
            parent_task_id: 父任务（抓取任务）ID
            raw_data_path: 原始数据存储路径
            priority: 任务优先级
            remarks: 备注信息
            
        Returns:
            str: AI分析任务ID，如果失败返回None
        """
        # 获取父任务信息
        parent_task = self.get_task_by_id(parent_task_id)
        if not parent_task:
            print(f"[ERROR] 父任务不存在: {parent_task_id}")
            return None
        
        if parent_task.get('task_stage') != 'scraping':
            print(f"[ERROR] 父任务不是抓取任务: {parent_task_id}")
            return None
        
        # 创建AI分析任务
        try:
            ai_task_id = self.create_task(
                tracking_number=parent_task['tracking_number'],
                task_type=parent_task['task_type'],
                creator_id=parent_task['creator_id'],
                creator_name=parent_task.get('creator_name'),
                carrier=parent_task.get('carrier'),
                priority=priority,
                remarks=remarks,
                task_stage='ai_analysis',
                parent_task_id=parent_task_id,
                raw_data_path=raw_data_path
            )
            print(f"[SUCCESS] AI分析任务创建成功: {ai_task_id} (父任务: {parent_task_id})")
            return ai_task_id
        except Exception as e:
            print(f"[ERROR] 创建AI分析任务失败: {e}")
            return None
    
    def update_raw_data_path(self, task_id: str, raw_data_path: str) -> bool:
        """
        更新任务的原始数据路径
        
        Args:
            task_id: 任务ID
            raw_data_path: 原始数据存储路径
            
        Returns:
            bool: 更新是否成功
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = "UPDATE task_queue SET raw_data_path = ? WHERE id = ?"
            params = (raw_data_path, task_id)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            affected_rows = cursor.rowcount
            print(f"[DB_LOG] 执行结果: 影响行数 {affected_rows}")
            
            if affected_rows > 0:
                conn.commit()
                print(f"[DB_LOG] 事务提交成功 - 库:task_queue.db 表:task_queue")
                print(f"[SUCCESS] 原始数据路径更新成功: {task_id}")
                return True
            else:
                print(f"[WARNING] 未找到任务: {task_id}")
                return False
                
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 更新原始数据路径失败: {e}")
            conn.rollback()
            print(f"[DB_LOG] 事务回滚完成 - 库:task_queue.db")
            return False
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_tasks_by_stage_and_status(self, task_stage: str, status: str, limit: int = 50) -> List[Dict]:
        """
        根据任务阶段和状态获取任务列表
        
        Args:
            task_stage: 任务阶段（'scraping' 或 'ai_analysis'）
            status: 任务状态
            limit: 返回任务数量限制
            
        Returns:
            List[Dict]: 任务列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                SELECT * FROM task_queue 
                WHERE task_stage = ? AND status = ?
                ORDER BY created_at DESC 
                LIMIT ?
            """
            params = (task_stage, status, limit)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(rows)} 行数据")
            
            tasks = [dict(row) for row in rows]
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return tasks
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取{task_stage}阶段{status}状态任务失败: {e}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_child_tasks(self, parent_task_id: str) -> List[Dict]:
        """
        获取父任务的所有子任务（AI分析任务）
        
        Args:
            parent_task_id: 父任务ID
            
        Returns:
            List[Dict]: 子任务列表
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            sql = """
                SELECT * FROM task_queue 
                WHERE parent_task_id = ?
                ORDER BY created_at ASC
            """
            params = (parent_task_id,)
            
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句: {sql.strip()}")
            print(f"[DB_LOG] 参数: {params}")
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果: 返回 {len(rows)} 行数据")
            
            tasks = [dict(row) for row in rows]
            print(f"[DB_LOG] 查询成功 - 库:task_queue.db 表:task_queue")
            return tasks
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取子任务失败: {e}")
            return []
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")
    
    def get_stage_statistics(self) -> Dict:
        """
        获取分阶段的任务统计信息
        
        Returns:
            Dict: 分阶段统计信息
        """
        conn = self._get_connection()
        try:
            cursor = conn.cursor()
            
            # 按阶段和状态统计
            sql = """
                SELECT 
                    task_stage,
                    status,
                    COUNT(*) as count
                FROM task_queue 
                GROUP BY task_stage, status
            """
            print(f"[DB_LOG] 执行SQL - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] SQL语句(阶段统计): {sql.strip()}")
            
            cursor.execute(sql)
            rows = cursor.fetchall()
            print(f"[DB_LOG] 查询结果(阶段统计): 返回 {len(rows)} 行数据")
            
            # 组织统计数据
            stats = {
                'scraping': {},
                'ai_analysis': {},
                'total': {}
            }
            
            for row in rows:
                stage = row['task_stage']
                status = row['status']
                count = row['count']
                
                if stage not in stats:
                    stats[stage] = {}
                stats[stage][status] = count
                
                # 累计到总计
                if status not in stats['total']:
                    stats['total'][status] = 0
                stats['total'][status] += count
            
            print(f"[DB_LOG] 阶段统计查询成功 - 库:task_queue.db 表:task_queue")
            return stats
            
        except Exception as e:
            print(f"[DB_LOG] 数据库操作失败 - 库:task_queue.db 表:task_queue")
            print(f"[DB_LOG] 错误详情: {str(e)}")
            print(f"[ERROR] 获取阶段统计信息失败: {e}")
            return {}
        finally:
            conn.close()
            print(f"[DB_LOG] 数据库连接已关闭: {self.db_path}")

# 便捷函数
def create_bill_of_lading_task(bl_number: str, creator_id: str, 
                               creator_name: str = None, carrier: str = None,
                               priority: int = 0, remarks: str = None,
                               task_stage: str = 'scraping') -> str:
    """
    创建提单号查询任务的便捷函数
    """
    manager = TaskManager()
    return manager.create_task(
        tracking_number=bl_number,
        task_type="bill_of_lading",
        creator_id=creator_id,
        creator_name=creator_name,
        carrier=carrier,
        priority=priority,
        remarks=remarks,
        task_stage=task_stage
    )

def create_container_task(container_number: str, creator_id: str,
                         creator_name: str = None, carrier: str = None,
                         priority: int = 0, remarks: str = None,
                         task_stage: str = 'scraping') -> str:
    """
    创建箱号查询任务的便捷函数
    """
    manager = TaskManager()
    return manager.create_task(
        tracking_number=container_number,
        task_type="container",
        creator_id=creator_id,
        creator_name=creator_name,
        carrier=carrier,
        priority=priority,
        remarks=remarks,
        task_stage=task_stage
    )

def create_scraping_task(tracking_number: str, task_type: str, creator_id: str,
                        creator_name: str = None, carrier: str = None,
                        priority: int = 0, remarks: str = None) -> str:
    """
    创建网页抓取任务的便捷函数
    """
    manager = TaskManager()
    return manager.create_task(
        tracking_number=tracking_number,
        task_type=task_type,
        creator_id=creator_id,
        creator_name=creator_name,
        carrier=carrier,
        priority=priority,
        remarks=remarks,
        task_stage='scraping'
    )

def create_ai_analysis_task_from_scraping(parent_task_id: str, raw_data_path: str,
                                         priority: int = 0, remarks: str = None) -> str:
    """
    基于抓取任务创建AI分析任务的便捷函数
    """
    manager = TaskManager()
    return manager.create_ai_analysis_task(
        parent_task_id=parent_task_id,
        raw_data_path=raw_data_path,
        priority=priority,
        remarks=remarks
    )

if __name__ == "__main__":
    # 测试代码
    print("🧪 测试任务管理器...")
    
    manager = TaskManager()
    
    # 创建测试任务
    task_id = create_bill_of_lading_task(
        bl_number="MEDUJ0618622",
        creator_id="test_user_001",
        creator_name="测试用户",
        carrier="MSC",
        priority=1,
        remarks="测试任务"
    )
    
    # 获取任务详情
    task = manager.get_task_by_id(task_id)
    print(f"📋 任务详情: {json.dumps(task, indent=2, ensure_ascii=False)}")
    
    # 获取统计信息
    stats = manager.get_task_statistics()
    print(f"[INFO] 统计信息: {json.dumps(stats, indent=2, ensure_ascii=False)}")
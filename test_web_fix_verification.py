#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Web数据显示修复是否有效
"""

import sqlite3
import json
from datetime import datetime

def test_database_structure():
    """测试数据库结构是否正确"""
    print("🔍 测试数据库结构...")
    
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 检查result字段是否存在
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'result' in column_names:
            print("✅ result字段存在")
        else:
            print("❌ result字段不存在")
            return False
        
        # 检查是否有result数据
        cursor.execute("SELECT COUNT(*) FROM task_queue WHERE result IS NOT NULL AND result != ''")
        result_count = cursor.fetchone()[0]
        print(f"📊 有result数据的任务数量: {result_count}")
        
        # 显示最近的几个任务
        cursor.execute("""
            SELECT id, tracking_number, task_stage, status, result, created_at 
            FROM task_queue 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        print(f"\n📋 最近5个任务:")
        for i, task in enumerate(tasks):
            task_id, tracking_number, task_stage, status, result, created_at = task
            result_preview = "有数据" if result else "无数据"
            print(f"  {i+1}. {tracking_number} ({task_stage}) - {status} - Result: {result_preview}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_api_data_structure():
    """测试API数据结构修复"""
    print("\n🔍 测试API数据结构...")
    
    try:
        # 模拟API服务的数据处理逻辑
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 使用修复后的SQL查询逻辑
        sql = """
            SELECT t1.* FROM task_queue t1
            INNER JOIN (
                SELECT tracking_number, 
                       MAX(CASE WHEN task_stage = 'ai_analysis' THEN created_at ELSE NULL END) as max_ai_time,
                       MAX(CASE WHEN task_stage = 'scraping' THEN created_at ELSE NULL END) as max_scraping_time
                FROM task_queue 
                WHERE creator_id = 'test_user'
                GROUP BY tracking_number
            ) t2 ON t1.tracking_number = t2.tracking_number
            WHERE t1.creator_id = 'test_user'
            AND (
                (t2.max_ai_time IS NOT NULL AND t1.task_stage = 'ai_analysis' AND t1.created_at = t2.max_ai_time)
                OR 
                (t2.max_ai_time IS NULL AND t1.task_stage = 'scraping' AND t1.created_at = t2.max_scraping_time)
            )
            ORDER BY t1.updated_at DESC
            LIMIT 3
        """
        
        cursor.execute(sql)
        tasks = cursor.fetchall()
        
        print(f"📊 查询到 {len(tasks)} 个任务（使用修复后的逻辑）")
        
        if tasks:
            print("✅ API查询逻辑正常")
            for i, task in enumerate(tasks):
                # 模拟task是一个字典
                task_dict = dict(zip([col[0] for col in cursor.description], task))
                tracking_number = task_dict['tracking_number']
                task_stage = task_dict['task_stage']
                status = task_dict['status']
                result = task_dict.get('result')
                
                print(f"  {i+1}. {tracking_number} ({task_stage}) - {status}")
                
                if result:
                    try:
                        result_data = json.loads(result)
                        print(f"      Result数据: {list(result_data.keys())}")
                    except:
                        print(f"      Result数据: 非JSON格式")
                else:
                    print(f"      Result数据: 无")
        else:
            print("⚠️  没有找到测试用户的任务")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ API数据结构测试失败: {e}")
        return False

def test_processor_status():
    """测试处理器状态"""
    print("\n🔍 测试处理器状态...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8080/api/v1/system/processor-status', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 处理器状态API正常")
            print(f"  运行状态: {'运行中' if data.get('running') else '停止'}")
            print(f"  状态: {data.get('status')}")
            
            active = data.get('active', {})
            print(f"  活跃任务:")
            print(f"    - 抓取任务: {active.get('scraping_tasks', 0)}")
            print(f"    - AI任务: {active.get('ai_tasks', 0)}")
            
            return True
        else:
            print(f"❌ 处理器状态API返回错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 处理器状态测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 验证Web数据显示修复...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    results = []
    
    # 测试数据库结构
    results.append(("数据库结构", test_database_structure()))
    
    # 测试API数据结构
    results.append(("API数据结构", test_api_data_structure()))
    
    # 测试处理器状态
    results.append(("处理器状态", test_processor_status()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！修复验证成功！")
        print("\n📝 下一步:")
        print("1. 确保API服务器在运行")
        print("2. 在Web页面中登录或创建用户")
        print("3. 创建新的查询任务测试数据显示")
        print("4. 刷新页面查看任务状态和结果数据")
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")

if __name__ == "__main__":
    main()

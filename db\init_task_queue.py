#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务队列数据库初始化脚本
创建任务队列表，用于管理提单号/箱号查询任务
"""

import sqlite3
import os
from datetime import datetime

def init_task_queue_database():
    """
    初始化任务队列数据库，创建任务表
    """
    db_path = "task_queue.db"
    
    # 连接数据库（如果不存在会自动创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建任务队列表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS task_queue (
            id TEXT PRIMARY KEY,                    -- 唯一任务ID（UUID格式）
            task_name TEXT NOT NULL,                -- 任务名称（如：MEDUJ0618622提单号查询任务）
            task_type TEXT NOT NULL,                -- 任务类型（bill_of_lading: 提单号, container: 箱号）
            tracking_number TEXT NOT NULL,          -- 跟踪号码（提单号或箱号）
            carrier TEXT,                           -- 承运人/船公司
            status TEXT NOT NULL DEFAULT 'pending', -- 任务状态（pending: 待处理, processing: 处理中, completed: 已完成, cancelled: 已取消）
            creator_id TEXT NOT NULL,               -- 任务发起人ID
            creator_name TEXT,                      -- 任务发起人姓名
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 任务发起时间
            started_at DATETIME,                    -- 任务开始处理时间
            completed_at DATETIME,                  -- 任务完成时间
            cancelled_by TEXT,                      -- 任务取消人ID
            cancelled_at DATETIME,                  -- 任务取消时间
            result_summary TEXT,                    -- 任务结果摘要
            error_message TEXT,                     -- 错误信息（如果任务失败）
            priority INTEGER DEFAULT 0,            -- 任务优先级（数字越大优先级越高）
            retry_count INTEGER DEFAULT 0,         -- 重试次数
            max_retries INTEGER DEFAULT 3,         -- 最大重试次数
            remarks TEXT,                           -- 备注信息
            result TEXT,                            -- 任务结果数据（JSON格式）
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP   -- 记录更新时间
        )
        """)
        
        # 创建索引以提高查询性能
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_task_status ON task_queue(status)",
            "CREATE INDEX IF NOT EXISTS idx_task_creator ON task_queue(creator_id)",
            "CREATE INDEX IF NOT EXISTS idx_task_created_at ON task_queue(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_task_type ON task_queue(task_type)",
            "CREATE INDEX IF NOT EXISTS idx_tracking_number ON task_queue(tracking_number)",
            "CREATE INDEX IF NOT EXISTS idx_task_priority ON task_queue(priority DESC)",
            "CREATE INDEX IF NOT EXISTS idx_task_updated_at ON task_queue(updated_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # 创建触发器，自动更新 updated_at 字段
        cursor.execute("""
        CREATE TRIGGER IF NOT EXISTS update_task_queue_updated_at
        AFTER UPDATE ON task_queue
        FOR EACH ROW
        BEGIN
            UPDATE task_queue SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        """)
        
        # 提交事务
        conn.commit()
        print(f"✅ 任务队列数据库初始化成功！")
        print(f"📁 数据库文件路径: {os.path.abspath(db_path)}")
        
        # 显示表结构
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        print("\n📋 任务队列表结构:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        # 显示索引信息
        cursor.execute("PRAGMA index_list(task_queue)")
        indexes = cursor.fetchall()
        print("\n🔍 创建的索引:")
        for idx in indexes:
            print(f"  - {idx[1]}")
        
        # 显示触发器信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='trigger' AND tbl_name='task_queue'")
        triggers = cursor.fetchall()
        print("\n⚡ 创建的触发器:")
        for trigger in triggers:
            print(f"  - {trigger[0]}")
            
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 开始初始化任务队列数据库...")
    init_task_queue_database()
    print("✨ 任务队列数据库初始化完成！")
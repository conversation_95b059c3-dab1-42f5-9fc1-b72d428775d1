#!/usr/bin/env python3
import requests
import json

# 测试API
try:
    response = requests.get('http://127.0.0.1:8000/tasks/my-tasks?limit=5')
    print(f'状态码: {response.status_code}')
    if response.status_code == 200:
        tasks = response.json()
        print(f'返回任务数量: {len(tasks)}')
        for i, task in enumerate(tasks[:3]):
            task_id = task.get('task_id', 'N/A')[:8] + '...'
            container = task.get('container_number', 'N/A')
            status = task.get('status', 'N/A')
            print(f'{i+1}. {task_id} {container} {status}')
    else:
        print(f'错误: {response.text}')
except Exception as e:
    print(f'请求失败: {e}')

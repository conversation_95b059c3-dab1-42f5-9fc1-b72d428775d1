#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web端数据同步功能
验证任务创建后，前端能够获取到完整的任务信息包括物流节点、截图等
"""

import requests
import json
import time
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def login_and_get_token():
    """登录并获取访问令牌"""
    login_data = {"invite_code": "TEST001"}
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('access_token'):
            print(f"✅ 登录成功，获得令牌")
            return result['access_token']
    
    print(f"❌ 登录失败: {response.text}")
    return None

def create_test_task(token, container_number):
    """创建测试任务"""
    headers = {"Authorization": f"Bearer {token}"}
    task_data = {
        "container_number": container_number,
        "priority": "normal"
    }
    
    response = requests.post(f"{API_BASE}/tasks/create", json=task_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 任务创建成功: {result.get('task_id')}")
        return result
    else:
        print(f"❌ 任务创建失败: {response.text}")
        return None

def get_my_tasks(token):
    """获取我的任务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/tasks/my-tasks", headers=headers)
    
    if response.status_code == 200:
        tasks = response.json()
        print(f"✅ 获取到 {len(tasks)} 个任务")
        return tasks
    else:
        print(f"❌ 获取任务失败: {response.text}")
        return []

def display_task_details(task):
    """显示任务详细信息"""
    print(f"\n📦 任务详情:")
    print(f"  任务ID: {task.get('task_id')}")
    print(f"  集装箱号: {task.get('container_number')}")
    print(f"  承运人: {task.get('carrier_code')} - {task.get('carrier_name', 'N/A')}")
    print(f"  任务状态: {task.get('status')}")
    print(f"  货运状态: {task.get('shipment_status', 'N/A')}")
    print(f"  网页抓取: {task.get('scraping_status', 'N/A')}")
    print(f"  AI分析: {task.get('ai_status', 'N/A')}")
    print(f"  预计到港: {task.get('estimated_arrival_time', 'N/A')}")
    print(f"  截图: {'有' if task.get('evidence_screenshot') else '无'}")
    
    shipment_dates = task.get('shipment_dates', [])
    if shipment_dates:
        print(f"  物流节点: {len(shipment_dates)}个")
        for i, date_info in enumerate(shipment_dates[:3], 1):  # 只显示前3个
            print(f"    {i}. {date_info.get('date_type')} - {date_info.get('status', 'N/A')}")
        if len(shipment_dates) > 3:
            print(f"    ... 还有{len(shipment_dates) - 3}个节点")
    else:
        print(f"  物流节点: 无")
    
    ai_result = task.get('ai_analysis_result')
    if ai_result:
        print(f"  AI分析: 已完成")
    else:
        print(f"  AI分析: 未完成")

def check_processor_status():
    """检查处理器状态"""
    response = requests.get(f"{API_BASE}/system/processor-status")
    
    if response.status_code == 200:
        status = response.json()
        print(f"🤖 处理器状态: {'运行中' if status.get('running') else '停止'}")
        if status.get('last_heartbeat'):
            print(f"   最后心跳: {status['last_heartbeat']}")
        return status.get('running', False)
    else:
        print(f"❌ 获取处理器状态失败: {response.text}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始测试Web端数据同步功能")
    print("=" * 60)
    
    # 1. 登录
    token = login_and_get_token()
    if not token:
        return
    
    # 2. 检查处理器状态
    processor_running = check_processor_status()
    if not processor_running:
        print("⚠️  处理器未运行，任务可能不会被处理")
    
    # 3. 获取当前任务列表
    print(f"\n📋 获取当前任务列表...")
    current_tasks = get_my_tasks(token)
    print(f"当前任务数量: {len(current_tasks)}")
    
    # 4. 创建一个新任务
    test_container = f"TEST{int(time.time())}"  # 使用时间戳确保唯一性
    print(f"\n🆕 创建新任务: {test_container}")
    new_task = create_test_task(token, test_container)
    
    if not new_task:
        return
    
    # 5. 等待一段时间让任务被处理
    print(f"\n⏳ 等待5秒让任务开始处理...")
    time.sleep(5)
    
    # 6. 重新获取任务列表，检查数据是否更新
    print(f"\n🔄 重新获取任务列表...")
    updated_tasks = get_my_tasks(token)
    
    # 7. 查找我们创建的任务
    our_task = None
    for task in updated_tasks:
        if task.get('container_number') == test_container:
            our_task = task
            break
    
    if our_task:
        print(f"\n✅ 找到我们的任务!")
        display_task_details(our_task)
        
        # 8. 检查数据完整性
        print(f"\n🔍 数据完整性检查:")
        checks = [
            ("任务ID", our_task.get('task_id')),
            ("承运人信息", our_task.get('carrier_code')),
            ("创建时间", our_task.get('created_at')),
            ("更新时间", our_task.get('updated_at')),
        ]
        
        for check_name, value in checks:
            status = "✅" if value else "❌"
            print(f"  {status} {check_name}: {value or 'N/A'}")
            
        # 检查高级功能
        advanced_checks = [
            ("货运状态", our_task.get('shipment_status')),
            ("网页抓取状态", our_task.get('scraping_status')),
            ("AI分析状态", our_task.get('ai_status')),
            ("预计到港时间", our_task.get('estimated_arrival_time')),
            ("截图", our_task.get('evidence_screenshot')),
            ("物流节点", our_task.get('shipment_dates')),
        ]
        
        print(f"\n🔬 高级功能检查:")
        for check_name, value in advanced_checks:
            if isinstance(value, list):
                status = "✅" if value else "⏳"
                detail = f"{len(value)}个" if value else "暂无"
            else:
                status = "✅" if value else "⏳"
                detail = str(value) if value else "暂无"
            print(f"  {status} {check_name}: {detail}")
            
    else:
        print(f"\n❌ 未找到我们创建的任务 {test_container}")
    
    # 9. 显示所有任务的概览
    if updated_tasks:
        print(f"\n📊 所有任务概览:")
        status_count = {}
        scraping_count = {}
        ai_count = {}
        
        for task in updated_tasks:
            # 统计任务状态
            status = task.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
            
            # 统计抓取状态
            scraping = task.get('scraping_status', 'unknown')
            scraping_count[scraping] = scraping_count.get(scraping, 0) + 1
            
            # 统计AI状态
            ai = task.get('ai_status', 'unknown')
            ai_count[ai] = ai_count.get(ai, 0) + 1
        
        print(f"  任务状态分布: {dict(status_count)}")
        print(f"  抓取状态分布: {dict(scraping_count)}")
        print(f"  AI状态分布: {dict(ai_count)}")
    
    print(f"\n🎯 测试完成!")

if __name__ == "__main__":
    main()

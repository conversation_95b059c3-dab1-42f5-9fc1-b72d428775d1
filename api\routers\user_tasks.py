#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户任务管理API路由
处理用户任务创建、查询等HTTP请求
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Body
from api.models.user_schemas import (
    TaskCreateRequest,
    BatchTaskCreateRequest, 
    TaskResponse,
    BatchTaskCreateResponse,
    UserInfo,
    APIResponse,
    TaskPriority
)
from api.services.user_task_service import UserTaskService, get_user_task_service
from api.middleware.auth import (
    get_current_user_required,
    require_permission
)
from api.utils.logger import api_logger

router = APIRouter()

@router.post("/tasks/create", response_model=TaskResponse)
async def create_task(
    request: TaskCreateRequest,
    current_user: UserInfo = Depends(get_current_user_required),
    task_service: UserTaskService = Depends(get_user_task_service),
    _: UserInfo = Depends(require_permission("query:create"))
):
    """
    创建单个查询任务
    
    - **container_number**: 集装箱号/提单号（必填）
    - **carrier_code**: 承运人代码（可选，自动识别）
    - **priority**: 任务优先级（可选，默认normal）
    - **callback_url**: 回调URL（可选）
    - **metadata**: 附加元数据（可选）
    
    需要认证和 'query:create' 权限
    """
    try:
        api_logger.info(f"用户 {current_user.name} 创建任务: {request.container_number}")
        
        task_response = await task_service.create_single_task(request, current_user)
        
        api_logger.info(f"任务创建成功: {task_response.task_id}")
        return task_response
        
    except Exception as e:
        api_logger.error(f"创建任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/create-batch", response_model=BatchTaskCreateResponse)
async def create_batch_tasks(
    request: BatchTaskCreateRequest,
    current_user: UserInfo = Depends(get_current_user_required),
    task_service: UserTaskService = Depends(get_user_task_service),
    _: UserInfo = Depends(require_permission("batch:create"))
):
    """
    批量创建查询任务
    
    - **tasks**: 任务列表（最多100个）
    
    需要认证和 'batch:create' 权限
    """
    try:
        api_logger.info(f"用户 {current_user.name} 批量创建任务: {len(request.tasks)}个")
        
        if len(request.tasks) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量创建任务数量不能超过100个"
            )
        
        batch_response = await task_service.create_batch_tasks(request, current_user)
        
        api_logger.info(f"批量任务创建完成: 成功{batch_response.summary['success']}个, 失败{batch_response.summary['failed']}个")
        return batch_response
        
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"批量创建任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/create-from-text", response_model=BatchTaskCreateResponse)
async def create_tasks_from_text(
    text_input: str = Body(..., description="批量输入文本", embed=True),
    priority: TaskPriority = Body(TaskPriority.NORMAL, description="任务优先级", embed=True),
    deduplicate: bool = Body(True, description="是否去重", embed=True),
    current_user: UserInfo = Depends(get_current_user_required),
    task_service: UserTaskService = Depends(get_user_task_service),
    _: UserInfo = Depends(require_permission("batch:create"))
):
    """
    从文本创建任务（一步完成：解析+校验+创建）
    
    - **text_input**: 批量输入的文本（支持多种分隔符）
    - **priority**: 任务优先级（可选，默认normal）
    - **deduplicate**: 是否去重（可选，默认true）
    
    这是前端最常用的接口，将文本解析、校验和任务创建合并为一步
    需要认证和 'batch:create' 权限
    """
    try:
        api_logger.info(f"用户 {current_user.name} 从文本创建任务")
        
        batch_response = await task_service.validate_and_create_from_text(
            text_input=text_input,
            user_info=current_user,
            priority=priority.value,
            deduplicate=deduplicate
        )
        
        api_logger.info(f"文本任务创建完成: 成功{batch_response.summary['success']}个, 失败{batch_response.summary['failed']}个")
        return batch_response
        
    except Exception as e:
        api_logger.error(f"从文本创建任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/my-tasks", response_model=List[TaskResponse])
async def get_my_tasks(
    status_filter: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    current_user: UserInfo = Depends(get_current_user_required),
    task_service: UserTaskService = Depends(get_user_task_service),
    _: UserInfo = Depends(require_permission("query:view"))
):
    """
    获取我的任务列表
    
    - **status_filter**: 状态过滤（可选）
    - **limit**: 限制数量（默认20）
    - **offset**: 偏移量（默认0）
    
    需要认证和 'query:view' 权限
    """
    try:
        api_logger.info(f"用户 {current_user.name} 查询任务列表")
        
        tasks = await task_service.get_user_tasks(
            user_info=current_user,
            status_filter=status_filter,
            limit=limit,
            offset=offset
        )
        
        api_logger.info(f"查询到 {len(tasks)} 个任务")
        return tasks
        
    except Exception as e:
        api_logger.error(f"查询任务列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/quick-create", response_model=TaskResponse)
async def quick_create_task(
    container_number: str = Body(..., description="集装箱号/提单号", embed=True),
    current_user: UserInfo = Depends(get_current_user_required),
    task_service: UserTaskService = Depends(get_user_task_service),
    _: UserInfo = Depends(require_permission("query:create"))
):
    """
    快速创建任务（简化版）
    
    - **container_number**: 集装箱号/提单号
    
    自动识别承运人，使用默认优先级
    需要认证和 'query:create' 权限
    """
    # Write initial debug info
    with open('D:/container-helper/debug_api.log', 'a', encoding='utf-8') as f:
        f.write(f"[QUICK CREATE] 路由被调用，container_number: {container_number}\n")
        f.write(f"[QUICK CREATE] 当前用户: {current_user.user_id} - {current_user.name}\n")
        f.flush()
        
    try:
        # Write debug info to a file
        with open('D:/container-helper/debug_api.log', 'a', encoding='utf-8') as f:
            f.write(f"[ROUTER DEBUG] 用户 {current_user.name} 快速创建任务: {container_number}\n")
            f.flush()
        
        print(f"[ROUTER DEBUG] 用户 {current_user.name} 快速创建任务: {container_number}")
        api_logger.info(f"用户 {current_user.name} 快速创建任务: {container_number}")
        
        request = TaskCreateRequest(
            container_number=container_number,
            priority=TaskPriority.NORMAL
        )
        
        with open('D:/container-helper/debug_api.log', 'a', encoding='utf-8') as f:
            f.write(f"[ROUTER DEBUG] 准备调用 task_service.create_single_task\n")
            f.flush()
            
        print(f"[ROUTER DEBUG] 准备调用 task_service.create_single_task")
        print(f"[ROUTER DEBUG] task_service: {task_service}")
        task_response = await task_service.create_single_task(request, current_user)
        
        with open('D:/container-helper/debug_api.log', 'a', encoding='utf-8') as f:
            f.write(f"[ROUTER DEBUG] task_service.create_single_task 返回: {task_response}\n")
            f.flush()
            
        print(f"[ROUTER DEBUG] task_service.create_single_task 返回: {task_response}")
        api_logger.info(f"快速任务创建成功: {task_response.task_id}")
        return task_response
        
    except Exception as e:
        api_logger.error(f"快速创建任务异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
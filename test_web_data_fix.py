#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web数据显示修复
验证任务状态、结果数据、处理器状态等是否正确显示
"""

import requests
import json
import time
from datetime import datetime

# API配置
API_BASE = "http://127.0.0.1:8000"

def test_processor_status():
    """测试处理器状态API"""
    print("=" * 50)
    print("🤖 测试处理器状态...")
    
    try:
        response = requests.get(f"{API_BASE}/system/processor-status")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"运行状态: {'运行中' if data.get('running') else '停止'}")
            print(f"状态: {data.get('status')}")
            print(f"最后心跳: {data.get('last_heartbeat', '无')}")
            
            active = data.get('active', {})
            print(f"活跃任务:")
            print(f"  - 抓取任务: {active.get('scraping_tasks', 0)}")
            print(f"  - AI任务: {active.get('ai_tasks', 0)}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_user_tasks_api():
    """测试用户任务API"""
    print("=" * 50)
    print("📋 测试用户任务API...")
    
    try:
        # 这里需要实际的认证token，暂时跳过认证测试
        headers = {
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f"{API_BASE}/tasks/my-tasks?limit=5", headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"返回任务数量: {len(tasks)}")
            
            for i, task in enumerate(tasks[:3]):  # 只显示前3个
                print(f"\n任务 {i+1}:")
                print(f"  ID: {task.get('task_id', 'N/A')}")
                print(f"  容器号: {task.get('container_number', 'N/A')}")
                print(f"  状态: {task.get('status', 'N/A')}")
                print(f"  预计到港: {task.get('estimated_arrival_time', 'N/A')}")
                print(f"  物流节点数: {len(task.get('shipment_dates', []))}")
                print(f"  截图: {'有' if task.get('evidence_screenshot') else '无'}")
                
                # 检查result字段
                result = task.get('result', {})
                if result:
                    print(f"  Result字段:")
                    print(f"    - ETA: {result.get('estimated_arrival_time', 'N/A')}")
                    print(f"    - 节点数: {len(result.get('tracking_points', []))}")
                    print(f"    - 截图数: {len(result.get('screenshots', []))}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_task_detail_api():
    """测试任务详情API"""
    print("=" * 50)
    print("🔍 测试任务详情API...")
    
    try:
        # 先获取一个任务ID
        response = requests.get(f"{API_BASE}/tasks/my-tasks?limit=1")
        if response.status_code != 200:
            print("❌ 无法获取任务列表")
            return False
            
        tasks = response.json()
        if not tasks:
            print("❌ 没有可用的任务")
            return False
            
        task_id = tasks[0].get('task_id')
        if not task_id:
            print("❌ 任务ID为空")
            return False
            
        print(f"测试任务ID: {task_id}")
        
        # 测试任务详情API
        response = requests.get(f"{API_BASE}/task/{task_id}/shipment-details")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"任务ID: {data.get('task_id', 'N/A')}")
            print(f"跟踪号: {data.get('tracking_number', 'N/A')}")
            print(f"找到货运记录: {'是' if data.get('shipment_found') else '否'}")
            print(f"物流节点数: {len(data.get('tracking_points', []))}")
            print(f"预计到港: {data.get('estimated_arrival_time', 'N/A')}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Web数据显示修复...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试处理器状态
    results.append(("处理器状态", test_processor_status()))
    
    # 测试用户任务API
    results.append(("用户任务API", test_user_tasks_api()))
    
    # 测试任务详情API
    results.append(("任务详情API", test_task_detail_api()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Web数据显示修复成功！")
    else:
        print("⚠️  部分测试失败，请检查相关问题")

if __name__ == "__main__":
    main()

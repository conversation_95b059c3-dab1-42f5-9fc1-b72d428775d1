# Web数据显示问题修复报告

## 问题描述

用户反馈Web页面上的任务列表存在以下问题：
1. 任务状态一直显示"查询中"，无法看到任务进度
2. 任务完成后，预计到港时间、物流节点、截图等数据显示为空
3. 处理器状态提示一直显示为0

## 问题分析

通过代码分析，发现了以下根本原因：

### 1. 数据库结构问题
- `task_queue`表缺少`result`字段来存储任务结果数据
- 任务完成后的结果数据没有正确保存到数据库

### 2. API数据结构不匹配
- 前端期望的数据字段与API返回的字段名不一致
- 前端期望：`task.result.estimated_arrival_time`
- API实际返回：`task.estimated_arrival_time`

### 3. 处理器状态字段不匹配
- 前端期望：`data.active.scraping` 和 `data.active.ai`
- API实际返回：`data.active.scraping_tasks` 和 `data.active.ai_tasks`

## 修复方案

### 1. 数据库结构修复

**文件：** `db/init_task_queue.py`
- 在`task_queue`表中添加`result TEXT`字段用于存储JSON格式的任务结果

**文件：** `fix_task_queue_schema.py`
- 创建数据库迁移脚本，为现有数据库添加`result`字段
- 迁移现有的`result_summary`数据到新的`result`字段

### 2. 任务管理器更新

**文件：** `task_manager.py`
- 更新`update_task_status`方法，添加`result_data`参数
- 支持将任务结果数据以JSON格式保存到`result`字段

### 3. 任务处理器更新

**文件：** `scheduled_task_processor.py`
- 在任务完成时调用`update_task_status`保存结果数据
- 确保AI分析结果正确保存到数据库

### 4. API服务更新

**文件：** `api/services/user_task_service.py`
- 优先读取`result`字段，兼容旧的`result_summary`字段
- 构建增强的result字段，包含前端期望的数据结构：
  - `estimated_arrival_time`：预计到港时间
  - `tracking_points`：物流节点数组
  - `screenshots`：截图数组

### 5. 前端代码修复

**文件：** `web/index.html`

#### 数据字段修复
```javascript
// 修复前
eta: task.result?.estimated_arrival_time || '-'
nodes: task.result?.tracking_points?.length ? `${task.result.tracking_points.length}条` : '-'
voucher: task.result?.screenshots?.length > 0

// 修复后（支持新旧数据结构）
eta: task.result?.estimated_arrival_time || task.estimated_arrival_time || '-'
nodes: (task.result?.tracking_points?.length || task.shipment_dates?.length) ? 
       `${task.result?.tracking_points?.length || task.shipment_dates?.length}条` : '-'
voucher: !!(task.result?.screenshots?.length || task.evidence_screenshot)
```

#### 处理器状态修复
```javascript
// 修复前
`处理器: 在线 · 抓取 ${data.active?.scraping ?? 0} · AI ${data.active?.ai ?? 0}`

// 修复后
`处理器: 在线 · 抓取 ${data.active?.scraping_tasks ?? 0} · AI ${data.active?.ai_tasks ?? 0}`
```

## 修复效果

### 1. 任务状态正确显示
- 任务状态会根据实际进度更新（查询中 → 已完成）
- 支持实时状态刷新（15秒间隔）

### 2. 结果数据正确显示
- 预计到港时间：从任务结果或货运记录中获取
- 物流节点：显示实际节点数量
- 凭证截图：正确显示是否有截图

### 3. 处理器状态正确显示
- 显示处理器在线/离线状态
- 显示当前活跃的抓取和AI任务数量

## 测试验证

创建了测试脚本验证修复效果：
- `test_web_data_fix.py`：测试API数据返回
- `fix_task_queue_schema.py`：数据库结构修复

## 部署说明

1. **数据库迁移**（必须）：
   ```bash
   py fix_task_queue_schema.py
   ```

2. **重启服务**（推荐）：
   - 重启API服务器
   - 重启任务处理器

3. **清除浏览器缓存**（必须）：
   - 访问 `http://127.0.0.1:8080/?v=fix1` 强制刷新
   - 或使用 Ctrl+F5 强制刷新

## 注意事项

1. 数据库迁移会自动处理现有数据，无需担心数据丢失
2. 前端代码兼容新旧数据结构，确保平滑过渡
3. 建议在生产环境部署前先在测试环境验证

## 后续优化建议

1. 添加WebSocket实时推送，减少轮询频率
2. 优化任务状态更新机制，提高响应速度
3. 添加更详细的错误信息显示

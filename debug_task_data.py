#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务数据，检查物流节点和截图数据
"""

import requests
import json
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def debug_task_data():
    """调试任务数据"""
    print("🔍 调试任务数据...")
    
    # 使用之前的token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiVEVTVDAwMSIsIm5hbWUiOiJcdTZkNGJcdThiZDVcdTc1MjhcdTYyMzciLCJyb2xlIjoiXHU2NjZmXHU5MDFhXHU3NTI4XHU2MjM3IiwiZXhwIjoxNzU3MTcyOTc4LCJpYXQiOjE3NTY1NjgxNzgsInR5cGUiOiJhY2Nlc3NfdG9rZW4ifQ.T0JN4V5ii6sKybtmZf2k0lp9WbR-gpLi18Nr9k7wtKc"
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{API_BASE}/tasks/my-tasks?limit=5", headers=headers, timeout=10)
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 获取到 {len(tasks)} 个任务")
            
            for i, task in enumerate(tasks):
                print(f"\n📋 任务 {i+1}: {task.get('container_number', 'N/A')}")
                print(f"   状态: {task.get('status', 'N/A')}")
                print(f"   预计到港: {task.get('estimated_arrival_time', 'N/A')}")
                print(f"   evidence_screenshot: {task.get('evidence_screenshot', 'N/A')}")
                print(f"   shipment_dates 长度: {len(task.get('shipment_dates', []))}")
                
                # 详细检查result字段
                result = task.get('result', {})
                print(f"   result字段存在: {'是' if result else '否'}")
                
                if result:
                    print(f"   result.keys(): {list(result.keys())}")
                    print(f"   result.estimated_arrival_time: {result.get('estimated_arrival_time', 'N/A')}")
                    print(f"   result.tracking_points: {result.get('tracking_points', 'N/A')}")
                    print(f"   result.screenshots: {result.get('screenshots', 'N/A')}")
                    
                    # 检查tracking_points的详细内容
                    tracking_points = result.get('tracking_points', [])
                    if tracking_points:
                        print(f"   tracking_points 长度: {len(tracking_points)}")
                        if len(tracking_points) > 0:
                            print(f"   第一个节点: {tracking_points[0]}")
                    else:
                        print(f"   tracking_points: 空或不存在")
                    
                    # 检查screenshots的详细内容
                    screenshots = result.get('screenshots', [])
                    if screenshots:
                        print(f"   screenshots 长度: {len(screenshots)}")
                        print(f"   screenshots 内容: {screenshots}")
                    else:
                        print(f"   screenshots: 空或不存在")
                
                # 检查shipment_dates
                shipment_dates = task.get('shipment_dates', [])
                if shipment_dates:
                    print(f"   shipment_dates 长度: {len(shipment_dates)}")
                    if len(shipment_dates) > 0:
                        print(f"   第一个日期: {shipment_dates[0]}")
                else:
                    print(f"   shipment_dates: 空")
                
                print(f"   完整task数据: {json.dumps(task, ensure_ascii=False, indent=2)}")
                print("-" * 50)
            
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 调试任务数据...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    debug_task_data()

if __name__ == "__main__":
    main()

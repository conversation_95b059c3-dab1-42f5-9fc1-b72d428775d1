#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量创建任务接口（模拟Web端调用）
"""

import requests
import json
from datetime import datetime

def test_batch_create_from_text():
    """测试从文本批量创建任务的接口"""
    print('🧪 测试批量创建任务接口（模拟Web端）')
    print('=' * 80)
    
    # 1. 登录
    print('步骤 1: 登录')
    login_url = 'http://127.0.0.1:8080/api/v1/auth/login'
    login_data = {'invite_code': 'TEST001'}
    
    response = requests.post(login_url, json=login_data)
    if response.status_code != 200:
        print(f'❌ 登录失败: {response.status_code}')
        return
    
    result = response.json()
    if not result.get('success'):
        print(f'❌ 登录失败: {result.get("message")}')
        return
    
    token = result['access_token']
    user_info = result['user_info']
    print(f'✅ 登录成功，用户ID: {user_info["user_id"]}，用户名: {user_info["name"]}')
    
    # 2. 获取创建前的任务数量
    print('\n步骤 2: 获取创建前的任务数量')
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    tasks_url = 'http://127.0.0.1:8080/api/v1/tasks/my-tasks'
    
    response = requests.get(tasks_url, headers=headers)
    if response.status_code == 200:
        before_tasks = response.json()
        print(f'✅ 创建前任务数量: {len(before_tasks)}')
    else:
        print(f'❌ 获取任务列表失败: {response.status_code}')
        return
    
    # 3. 使用批量创建接口（模拟Web端调用）
    print('\n步骤 3: 使用批量创建接口')
    timestamp = datetime.now().strftime("%m%d%H%M%S")
    test_text = f"""
BATCH{timestamp}A
BATCH{timestamp}B
BATCH{timestamp}C
    """.strip()
    
    batch_url = 'http://127.0.0.1:8080/api/v1/tasks/create-from-text'
    batch_data = {
        'text_input': test_text,
        'priority': 'normal',
        'deduplicate': True
    }
    
    print(f'批量创建数据: {batch_data}')
    
    response = requests.post(batch_url, json=batch_data, headers=headers)
    if response.status_code == 200:
        batch_result = response.json()
        print(f'✅ 批量创建响应: {json.dumps(batch_result, indent=2, ensure_ascii=False)}')
        
        if batch_result.get('success') and batch_result.get('created_tasks'):
            created_task_ids = [task['task_id'] for task in batch_result['created_tasks']]
            print(f'✅ 批量创建成功，创建了 {len(created_task_ids)} 个任务')
            for i, task in enumerate(batch_result['created_tasks']):
                print(f'  {i+1}. ID: {task["task_id"]}, 号码: {task["container_number"]}, 用户: {task["user_id"]}')
        else:
            print(f'❌ 批量创建失败')
            return
    else:
        print(f'❌ 批量创建失败: {response.status_code} - {response.text}')
        return
    
    # 4. 获取创建后的任务数量
    print('\n步骤 4: 获取创建后的任务数量')
    response = requests.get(tasks_url, headers=headers)
    if response.status_code == 200:
        after_tasks = response.json()
        print(f'✅ 创建后任务数量: {len(after_tasks)}')
        
        # 5. 检查新任务是否在列表中
        print('\n步骤 5: 检查新任务是否在列表中')
        found_count = 0
        for created_task in batch_result['created_tasks']:
            created_task_id = created_task['task_id']
            found = False
            for task in after_tasks:
                if task['task_id'] == created_task_id:
                    found = True
                    found_count += 1
                    print(f'  ✅ 找到任务: {task["container_number"]} (ID: {created_task_id})')
                    break
            if not found:
                print(f'  ❌ 未找到任务: {created_task["container_number"]} (ID: {created_task_id})')
        
        print(f'\n总结: 批量创建了 {len(created_task_ids)} 个任务，在列表中找到 {found_count} 个')
        
        # 6. 比较任务数量变化
        print(f'\n步骤 6: 比较任务数量变化')
        expected_increase = len(created_task_ids)
        actual_increase = len(after_tasks) - len(before_tasks)
        
        if actual_increase == expected_increase:
            print(f'✅ 任务数量正确增加了{expected_increase}个')
            print(f'\n🎉 批量创建功能正常！')
        else:
            print(f'❌ 任务数量变化异常: 预期+{expected_increase}，实际+{actual_increase}')
            
    else:
        print(f'❌ 获取创建后任务列表失败: {response.status_code}')

if __name__ == "__main__":
    test_batch_create_from_text()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def check_api_tasks():
    """检查API相关的任务数据"""
    print("🔍 检查API任务数据...")
    
    # 连接数据库
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    
    try:
        print("\n=== 检查TEST001用户的所有任务（最近10个）===")
        cursor.execute('''
            SELECT id, tracking_number, creator_id, task_stage, status, created_at 
            FROM task_queue 
            WHERE creator_id = 'TEST001' 
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        tasks = cursor.fetchall()
        
        if tasks:
            for i, task in enumerate(tasks, 1):
                print(f"  {i}. 任务ID: {task[0][:8]}..., 集装箱号: {task[1]}, 邀请码: {task[2]}")
                print(f"     阶段: {task[3]}, 状态: {task[4]}, 创建时间: {task[5]}")
        else:
            print("  没有找到TEST001用户的任务")
        
        print("\n=== 检查MEDUJ0616089是否有邀请码 ===")
        cursor.execute('''
            SELECT id, tracking_number, creator_id, task_stage, status, created_at 
            FROM task_queue 
            WHERE tracking_number = 'MEDUJ0616089'
            ORDER BY created_at DESC
        ''')
        meduj_tasks = cursor.fetchall()
        
        if meduj_tasks:
            for i, task in enumerate(meduj_tasks, 1):
                print(f"  {i}. 任务ID: {task[0][:8]}..., 集装箱号: {task[1]}, 邀请码: {task[2] or 'None'}")
                print(f"     阶段: {task[3]}, 状态: {task[4]}, 创建时间: {task[5]}")
        else:
            print("  没有找到MEDUJ0616089任务")
            
        print("\n=== 检查所有用户的邀请码分布 ===")
        cursor.execute('''
            SELECT creator_id, COUNT(*) as task_count,
                   COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count
            FROM task_queue 
            GROUP BY creator_id 
            ORDER BY task_count DESC
        ''')
        invite_stats = cursor.fetchall()
        
        for stat in invite_stats:
            print(f"  邀请码: {stat[0] or 'None'}, 总任务数: {stat[1]}, 完成数: {stat[2]}")
            
    except Exception as e:
        print(f"❌ 检查任务时出错: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_api_tasks()

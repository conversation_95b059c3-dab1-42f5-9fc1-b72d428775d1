#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动任务处理器
"""

import sys
import os
import time
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_processor():
    try:
        print("[STARTUP] 正在启动任务处理器...")
        
        from scheduled_task_processor import ScheduledTaskProcessor
        
        # 创建处理器实例
        processor = ScheduledTaskProcessor(
            scraping_interval=10,
            ai_interval=5,
            status_update_interval=30,
            max_scraping_tasks=2,
            max_ai_tasks=3
        )
        
        # 启动处理器
        processor.start()
        
        print("[SUCCESS] 任务处理器已启动，按Ctrl+C停止...")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n[STOP] 接收到停止信号，正在关闭处理器...")
            processor.stop()
            print("[SUCCESS] 任务处理器已停止")
            
    except Exception as e:
        print(f"[ERROR] 启动任务处理器失败: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    start_processor()

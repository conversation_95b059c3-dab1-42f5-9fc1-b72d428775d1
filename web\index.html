<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E船期查询 (数据显示修复版本)</title>

    <!-- PWA and Icon Configuration -->
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzYzNjZmMSIvPgo8c3ZnIHg9IjgiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMyIvPgo8cGF0aCBkPSJtMyA5IDktOSA5IDl2OS4yNWEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY5WiIvPgo8L3N2Zz4KPC9zdmc+">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDE4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iMTgwIiByeD0iMjAiIGZpbGw9IiM2MzY2ZjEiLz4KPHN2ZyB4PSI0NSIgeT0iNDUiIHdpZHRoPSI5MCIgaGVpZ2h0PSI5MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiLz4KPHBhdGggZD0ibTMgOSA5LTkgOSA5djkuMjVhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOVoiLz4KPC9zdmc+Cjwvc3ZnPg==">
    <meta name="theme-color" content="#6366f1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="E船期查询">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

        <script>
            // 登录守卫：未登录则跳转 login.html
            (function(){
                try{
                    var hasAuth = false;
                    try {
                        var authData = localStorage.getItem('auth_data');
                        if (authData) {
                            var parsed = JSON.parse(authData);
                            hasAuth = !!(parsed && parsed.access_token);
                        }
                    } catch {}
                    var inLogin = /login\.html$/i.test(location.pathname);
                    if(!hasAuth && !inLogin){
                        var url = new URL('login.html', location.href);
                        var qs = new URLSearchParams(location.search);
                        if(qs.get('debug') === '1') url.searchParams.set('debug','1');
                        if(!url.searchParams.get('v')) url.searchParams.set('v', Date.now().toString().slice(-8));
                        location.replace(url.toString());
                        return; // 阻止后续脚本执行
                    }
                }catch(e){
                    console.warn('登录守卫异常:', e);
                }
            })();
        </script>

    <style>
        html.dark {
            color-scheme: dark;
        }
        html { font-size: 12px; }
        body {
            font-family: 'Inter', 'Helvetica Neue', 'Arial', 'sans-serif';
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .lucide {
            width: 1.125rem; /* 18px at 16px base; scales to 13.5px at 12px base */
            height: 1.125rem;
        }

        /* 粒子背景Canvas样式 */
        #particle-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            pointer-events: none;
        }

        /* 确保内容在粒子背景之上 */
        main > * {
            position: relative;
            z-index: 1;
        }

        /* 卡片辉光效果 */
        .card-glow {
            position: relative;
            transition: all 0.3s ease;
        }
        .card-glow:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 1rem; /* 16px */
            border: 1px solid transparent;
            background: linear-gradient(120deg, #818cf8, #3b82f6) border-box;
            -webkit-mask:
                linear-gradient(#fff 0 0) padding-box,
                linear-gradient(#fff 0 0);
            mask:
                linear-gradient(#fff 0 0) padding-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .card-glow:hover:before {
            opacity: 0.6;
        }

        /* 海浪涟漪效果 */
        .wave-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .wave {
            position: absolute;
            bottom: -50px;
            left: -50%;
            width: 200%;
            height: 150px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
            border-radius: 50%;
            animation: wave-flow 12s ease-in-out infinite;
        }

        .wave1 {
            animation-delay: 0s;
            opacity: 0.6;
            animation-duration: 12s;
        }

        .wave2 {
            animation-delay: -4s;
            opacity: 0.4;
            background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.2), transparent);
            animation-duration: 15s;
        }

        .wave3 {
            animation-delay: -8s;
            opacity: 0.3;
            background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.15), transparent);
            animation-duration: 18s;
        }

        @keyframes wave-flow {
            0% {
                transform: translateX(-100%) translateY(0px) scaleY(1);
            }
            25% {
                transform: translateX(-25%) translateY(-10px) scaleY(1.1);
            }
            50% {
                transform: translateX(50%) translateY(0px) scaleY(1);
            }
            75% {
                transform: translateX(125%) translateY(-5px) scaleY(0.9);
            }
            100% {
                transform: translateX(200%) translateY(0px) scaleY(1);
            }
        }

        /* 涟漪效果 */
        .wave-container::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 30%;
            width: 100px;
            height: 100px;
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            animation: ripple 4s ease-out infinite;
        }

        .wave-container::after {
            content: '';
            position: absolute;
            top: 60%;
            right: 25%;
            width: 80px;
            height: 80px;
            border: 2px solid rgba(6, 182, 212, 0.2);
            border-radius: 50%;
            animation: ripple 6s ease-out infinite 2s;
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* 雪花效果 */
        .snowflakes {
            pointer-events: none;
        }

        .snowflake {
            position: absolute;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            animation: snowfall 8s linear infinite;
        }

        .snowflake:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 8s; }
        .snowflake:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 9s; }
        .snowflake:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 7s; }
        .snowflake:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 10s; }
        .snowflake:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 8s; }
        .snowflake:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 9s; }
        .snowflake:nth-child(7) { left: 70%; animation-delay: 6s; animation-duration: 7s; }
        .snowflake:nth-child(8) { left: 80%; animation-delay: 7s; animation-duration: 8s; }
        .snowflake:nth-child(9) { left: 90%; animation-delay: 8s; animation-duration: 9s; }
        .snowflake:nth-child(10) { left: 15%; animation-delay: 9s; animation-duration: 10s; }

        @keyframes snowfall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* 船只环绕动画 */
        .ship-orbit {
            pointer-events: none;
        }

        .ship {
            position: absolute;
            font-size: 1.5rem;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .ship1 {
            animation: orbit1 20s linear infinite;
        }

        .ship2 {
            animation: orbit2 25s linear infinite;
        }

        .ship3 {
            animation: orbit3 30s linear infinite;
        }

        @keyframes orbit1 {
            0% {
                transform: translate(50vw, 20vh) rotate(0deg) translateX(200px) rotate(0deg);
            }
            100% {
                transform: translate(50vw, 20vh) rotate(360deg) translateX(200px) rotate(-360deg);
            }
        }

        @keyframes orbit2 {
            0% {
                transform: translate(50vw, 80vh) rotate(0deg) translateX(250px) rotate(0deg);
            }
            100% {
                transform: translate(50vw, 80vh) rotate(-360deg) translateX(250px) rotate(360deg);
            }
        }

        @keyframes orbit3 {
            0% {
                transform: translate(50vw, 50vh) rotate(0deg) translateX(300px) rotate(0deg);
            }
            100% {
                transform: translate(50vw, 50vh) rotate(360deg) translateX(300px) rotate(-360deg);
            }
        }

        /* 毛玻璃效果 */
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .glass-morphism::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
            border-radius: inherit;
            pointer-events: none;
        }

        /* 闪光效果 */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        @keyframes shimmer-slow {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .animate-shimmer {
            animation: shimmer 2s ease-in-out infinite;
        }

        .animate-shimmer-slow {
            animation: shimmer-slow 3s ease-in-out infinite;
        }

        /* 用户弹窗样式 */
        .user-modal {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        /* 分页控件样式 */
        .pagination-button {
            transition: all 0.2s ease;
        }
        .pagination-button:hover {
            transform: translateY(-1px);
        }

        /* 物流时间轴样式 */
        .logistics-timeline {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .route-overview {
            border-radius: 12px 12px 0 0;
        }

        .timeline-item {
            transition: all 0.2s ease;
        }

        .timeline-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .dark .timeline-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* 状态徽章 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.2px;
        }
        .status-badge .en { opacity: 0.75; font-weight: 500; }
        .status-green { background: linear-gradient(90deg,#22c55e33,#16a34a33); color:#166534; border:1px solid #86efac; }
        .status-blue { background: linear-gradient(90deg,#60a5fa33,#3b82f633); color:#1e40af; border:1px solid #93c5fd; }
        .status-yellow { background: linear-gradient(90deg,#facc1533,#f59e0b33); color:#92400e; border:1px solid #fde68a; }
        .status-red { background: linear-gradient(90deg,#fca5a533,#ef444433); color:#7f1d1d; border:1px solid #fecaca; }
        .status-gray { background: linear-gradient(90deg,#cbd5e133,#94a3b833); color:#334155; border:1px solid #cbd5e1; }

        /* 右侧时间标签 */
        .time-chip {
            background: #f1f5f9;
            color: #475569;
            padding: 2px 10px;
            border-radius: 9999px;
            font-size: 12px;
            border: 1px solid #e2e8f0;
        }
        .dark .time-chip { background:#1f2937; color:#cbd5e1; border-color:#334155; }

        /* 阅读指引和图例 */
        .legend-chip {
            background: #fff;
            border:1px dashed #cbd5e1;
            color:#475569;
            padding: 4px 10px;
            border-radius: 9999px;
            font-size: 12px;
        }
        .dark .legend-chip { background:#0f172a; border-color:#334155; color:#cbd5e1; }

        /* 表格骨架屏加载动画 */
        .skeleton { position: relative; overflow: hidden; background: #e5e7eb; }
        .dark .skeleton { background: #374151; }
        .skeleton::after { content: ""; position: absolute; inset: 0; transform: translateX(-100%); background: linear-gradient(90deg, transparent, rgba(255,255,255,.35), transparent); animation: shimmer 1.2s infinite; }
        @keyframes shimmer { 100% { transform: translateX(100%); } }

        /* 简洁时间轴（参照12306） */
        .tl-col { width: 2rem; }
        .tl-dot { width: 10px; height: 10px; border-radius: 9999px; background: #10b981; border: 2px solid #fff; box-shadow: 0 0 0 2px #a7f3d0; }
        .dark .tl-dot { box-shadow: 0 0 0 2px #064e3b; }
        .tl-line { flex: 1; width: 2px; background: #e5e7eb; }
        .dark .tl-line { background: #334155; }
        .tl-dot-gray { background:#9ca3af; box-shadow:0 0 0 2px #e5e7eb; }
        .tl-dot-blue { background:#3b82f6; box-shadow:0 0 0 2px #bfdbfe; }

        /* 响应式设计 */
        @media (max-width: 640px) {
            .route-overview { padding: 1rem; }
            .timeline-body { padding: 1rem; }
            .timeline-item { padding: 0.75rem; }
        /* 自定义下拉的外观增强（避免系统样式导致右边框丢失） */
        select { background-image: linear-gradient(45deg, transparent 50%, #64748b 50%), linear-gradient(135deg, #64748b 50%, transparent 50%); background-position: right 10px top 50%, right 5px top 50%; background-size: 5px 5px, 5px 5px; background-repeat: no-repeat; }
        .dark select { background-image: linear-gradient(45deg, transparent 50%, #cbd5e1 50%), linear-gradient(135deg, #cbd5e1 50%, transparent 50%); }
        select:focus { outline: none; }
        /* 强制右边框可见 */
        select { border-right-width: 1px !important; }

        }
    </style>
</head>
<body class="bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-300 transition-colors duration-300">

    <!-- 登录已迁移至 login.html，移除旧遮罩 -->

    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm border-r border-slate-200/80 dark:border-slate-700/60 flex flex-col transition-all duration-300">
            <div class="pl-6 pr-0 py-4 border-b border-slate-200/80 dark:border-slate-700/60 flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i data-lucide="ship-wheel" class="text-blue-500 h-7 w-7"></i>
                    <h1 class="text-xl font-bold text-slate-900 dark:text-slate-100">
                        <button id="system-title-btn" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 cursor-pointer" title="关于系统">E船期查询</button>
                    </h1>
                </div>
                <!-- 通知按钮与新建查询按钮右边缘对齐 -->
                <div class="pr-4">
                    <button id="notification-btn" class="relative p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700 transition-all duration-200 group" title="通知">
                        <i data-lucide="bell" class="h-5 w-5 group-hover:animate-bounce"></i>
                        <!-- 通知红点 -->
                        <div class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <button id="new-query-btn" class="group w-full bg-gradient-to-r from-blue-600 to-blue-500 text-white font-bold py-2.5 px-4 rounded-lg flex items-center justify-center transition-all duration-300 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 hover:-translate-y-0.5 transform">
                    <i data-lucide="plus" class="mr-2 h-5 w-5 group-hover:rotate-90 transition-transform duration-300"></i>
                    <span>新建查询</span>
                </button>
            </div>
            <nav class="flex-1 px-4 space-y-6">
                <!-- 查询管理 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">查询管理</h3>
                    <ul class="space-y-1">
                        <li>
                            <a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-semibold text-blue-600 dark:text-blue-400 border-l-4 border-blue-600 dark:border-blue-400 bg-blue-100 dark:bg-blue-500/10 transition-colors duration-200" data-page="dashboard">
                                <i data-lucide="inbox" class="mr-3"></i>
                                <span>全部</span>
                                <span class="ml-auto bg-blue-200 dark:bg-blue-500/20 text-blue-800 dark:text-blue-300 text-xs font-medium px-2 py-0.5 rounded-full" id="total-count">8</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="in-progress">
                                <i data-lucide="loader-2" class="mr-3"></i>
                                <span>进行中</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full" id="progress-count">2</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="completed">
                                <i data-lucide="check-circle" class="mr-3"></i>
                                <span>已完成</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full" id="completed-count">5</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="failed">
                                <i data-lucide="x-circle" class="mr-3"></i>
                                <span>查询失败</span>
                                <span class="ml-auto bg-slate-200 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs font-medium px-2 py-0.5 rounded-full" id="failed-count">1</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <!-- 数据分析 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">数据分析</h3>
                    <ul class="space-y-1">
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="statistics"><i data-lucide="bar-chart-3" class="mr-3"></i><span>统计报表</span></a></li>
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="trends"><i data-lucide="trending-up" class="mr-3"></i><span>趋势分析</span></a></li>
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="map"><i data-lucide="map" class="mr-3"></i><span>航线地图</span></a></li>
                    </ul>
                </div>
                <!-- 工具 -->
                <div>
                    <h3 class="mb-2 text-xs font-semibold text-slate-500 uppercase tracking-wider">工具</h3>
                     <ul class="space-y-1">
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="export"><i data-lucide="download" class="mr-3"></i><span>导出数据</span></a></li>
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="notifications"><i data-lucide="bell" class="mr-3"></i><span>通知设置</span></a></li>
                        <li><a href="#" class="nav-link flex items-center px-3 py-2 text-sm font-medium text-slate-500 dark:text-slate-400 border-l-4 border-transparent hover:bg-slate-100 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 rounded-r-lg transition-colors duration-200" data-page="settings"><i data-lucide="settings" class="mr-3"></i><span>系统设置</span></a></li>
                    </ul>
                </div>
            </nav>
            <!-- User Profile Section -->
            <div class="mt-auto p-2 border-t border-slate-200/80 dark:border-slate-700/60">
                <div class="flex items-center justify-between w-full">
                    <button id="user-profile-btn" class="flex-1 flex items-center p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-all duration-300 min-w-0 group relative">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-sm shadow-md flex-shrink-0 ring-2 ring-transparent group-hover:ring-purple-300 dark:group-hover:ring-purple-500 transition-all duration-300">
                            M
                        </div>
                        <div class="ml-3 min-w-0">
                            <p class="text-sm font-semibold text-slate-800 dark:text-slate-200 truncate group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors flex items-center">
                                <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"></span>
                                <span id="user-display-name">李乐</span>
                            </p>
                            <p class="text-xs text-slate-500 dark:text-slate-400 truncate">
                                <span id="user-display-role">高级用户</span>
                            </p>
                        </div>
                    </button>
                    <div class="flex items-center flex-shrink-0">
                        <button id="sidebar-logout-btn" class="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-red-100 dark:hover:bg-red-500/10 hover:text-red-600 dark:hover:text-red-400 transition-all duration-200 group" title="退出登录">
                            <i data-lucide="log-out" class="h-5 w-5 group-hover:-rotate-12 transition-transform"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col bg-slate-100 dark:bg-slate-900">
            <!-- Page content -->
            <main class="flex-1 p-6 lg:p-8 overflow-y-auto relative flex flex-col">
                <canvas id="particle-canvas"></canvas>
                <!-- Search Card -->
                <div class="card-glow bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm p-4 rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/50 border border-slate-200/80 dark:border-slate-700/60 transition-all duration-300 hover:-translate-y-1 flex-shrink-0">
                    <div class="flex flex-wrap items-center gap-6">
                        <div class="flex items-center gap-2 min-w-[240px] max-w-[280px]">
                            <label for="search-input" class="text-sm font-medium text-slate-700 dark:text-slate-300 whitespace-nowrap">提单号/箱号：</label>
                            <input type="text" id="search-input" placeholder="请输入提单号或箱号" class="flex-1 px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                        </div>
                        <div class="flex items-center gap-2 min-w-[160px] max-w-[200px]">
                             <label for="status-filter" class="text-sm font-medium text-slate-700 dark:text-slate-300 whitespace-nowrap">状态：</label>
                             <select id="status-filter" class="flex-1 px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 appearance-none">
                                 <option value="all">全部状态</option>
                                 <option value="已完成">已完成</option>
                                 <option value="查询中">查询中</option>
                                 <option value="查询失败">查询失败</option>
                             </select>
                        </div>
                        <div class="flex items-center gap-2 min-w-[180px] max-w-[220px]">
                             <label for="eta-filter" class="text-sm font-medium text-slate-700 dark:text-slate-300 whitespace-nowrap">预计到港时间：</label>
                             <input type="date" id="eta-filter" class="flex-1 px-4 py-2 bg-white/50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-lg text-slate-900 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                        </div>
                        <div class="flex items-center gap-2 ml-auto">
                            <button id="search-button" class="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 transform hover:-translate-y-0.5 active:scale-95 flex items-center justify-center">
                                <i data-lucide="search" class="mr-2 h-4 w-4"></i>
                                <span>搜索</span>
                            </button>
                            <button id="reset-button" class="bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 font-bold py-2 px-6 rounded-lg transition-all duration-300 transform active:scale-95 flex items-center justify-center">
                                <i data-lucide="rotate-ccw" class="mr-2 h-4 w-4"></i>
                                <span>重置</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Query Tasks List -->
                <div class="card-glow mt-8 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm rounded-2xl shadow-2xl shadow-black/10 dark:shadow-black/50 border border-slate-200/80 dark:border-slate-700/60 transition-all duration-300 hover:-translate-y-1 flex flex-col flex-grow min-h-0">
                    <!-- Header: 表格标题+统计 -->
                    <div class="px-6 py-4 flex-shrink-0 border-b border-slate-200/80 dark:border-slate-700/60">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
                                查询任务列表
                                <span id="stats-total" class="ml-3 text-xs text-slate-500 dark:text-slate-400">
                                    总计: 7
                                </span>
                                <span class="mx-1 text-xs text-slate-300 dark:text-slate-600">|</span>
                                <span id="stats-running" class="text-xs text-amber-600 dark:text-amber-400">
                                    运行中: 2
                                </span>
                                <span class="mx-1 text-xs text-slate-300 dark:text-slate-600">|</span>
                                <span id="stats-success" class="text-xs text-emerald-600 dark:text-emerald-400">
                                    成功: 4
                                </span>
                                <span class="mx-1 text-xs text-slate-300 dark:text-slate-600">|</span>
                                <span id="stats-failed" class="text-xs text-rose-600 dark:text-rose-400">
                                    失败: 1
                                </span>
                            </h3>
                            <!-- 右侧工具按钮 -->
                            <div class="flex items-center space-x-2">
                                <span id="processor-indicator" class="hidden items-center text-xs font-medium px-2 py-1 rounded-full bg-slate-200 text-slate-700 dark:bg-slate-700 dark:text-slate-200" title="后台处理器状态">处理器: 未知</span>
                                <button id="refresh-btn" class="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 px-3 py-1.5 rounded-md transition-all duration-200 group">
                                    <i data-lucide="refresh-cw" class="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500"></i>
                                    刷新
                                </button>
                                <button id="export-btn" class="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 px-3 py-1.5 rounded-md transition-colors">
                                    <i data-lucide="download" class="mr-2 h-4 w-4"></i>
                                    导出
                                </button>
                                <button id="density-btn" class="flex items-center text-sm font-medium text-slate-600 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700/50 hover:text-slate-900 dark:hover:text-slate-100 px-3 py-1.5 rounded-md transition-colors">
                                    <i data-lucide="list" class="mr-2 h-4 w-4"></i>
                                    密度
                                </button>
                            </div>
                        </div>
                        <p class="text-xs text-slate-500 dark:text-slate-400">实时统计：根据当前筛选条件动态更新</p>
                    </div>

                    <!-- 表格内容 -->
                    <div class="overflow-auto flex-1">
                        <table class="w-full text-sm text-left text-slate-600 dark:text-slate-400">
                            <thead class="sticky top-0 bg-white/80 dark:bg-slate-800/50 backdrop-blur-sm z-10">
                                <tr>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">提单号/箱号</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">船公司</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">预计到港时间</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">物流节点</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">凭证截图</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">创建时间</th>
                                    <th scope="col" class="px-6 py-3 font-medium tracking-wider">更新时间</th>
                                </tr>
                            </thead>
                            <tbody id="table-body" class="divide-y divide-slate-200/80 dark:divide-slate-700/60">
                                <!-- Table rows will be generated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="px-6 py-4 border-t border-slate-200/80 dark:border-slate-700/60 flex items-center justify-between">
                        <div class="flex items-center space-x-3 text-sm text-slate-500 dark:text-slate-400">
                            <span>显示 <span id="show-start">1</span> 到 <span id="show-end">7</span> 条，共 <span id="total-records">7</span> 条记录</span>
                            <div class="flex items-center space-x-2">
                                <label for="page-size-select" class="text-xs">每页显示:</label>
                                <select id="page-size-select" class="px-2 py-1 text-xs bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded appearance-none">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center space-x-1">
                            <button id="prev-page" class="pagination-button px-3 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i data-lucide="chevron-left" class="h-4 w-4"></i>
                            </button>
                            <div id="page-numbers" class="flex items-center space-x-1">
                                <button class="pagination-button px-3 py-1 text-sm bg-blue-500 text-white rounded">1</button>
                            </div>
                            <button id="next-page" class="pagination-button px-3 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 rounded hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i data-lucide="chevron-right" class="h-4 w-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 用户个人信息弹窗 -->
    <div id="user-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm user-modal hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="user-modal-content">
            <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-xl shadow-lg relative">
                        M
                        <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white dark:border-slate-800 rounded-full"></div>
                    </div>
                    <div>
                        <h3 id="modal-user-name" class="text-xl font-bold text-slate-900 dark:text-slate-100">李乐</h3>
                        <p id="modal-user-info" class="text-sm text-slate-500 dark:text-slate-400">高级用户 · ID: 001</p>
                        <div class="flex items-center mt-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" title="在线"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-slate-900 dark:text-slate-100">128</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">查询总数</div>
                    </div>
                    <div class="bg-slate-50 dark:bg-slate-700/50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">95%</div>
                        <div class="text-xs text-slate-500 dark:text-slate-400">成功率</div>
                    </div>
                </div>

                <div class="space-y-3">
                    <h4 class="font-semibold text-slate-900 dark:text-slate-100 flex items-center">
                        <i data-lucide="bell" class="w-4 h-4 mr-2"></i>
                        消息通知
                        <span class="ml-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">3</span>
                    </h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-slate-900 dark:text-slate-100">查询任务完成</p>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">MEDUX0610822 已完成</p>
                                </div>
                            </div>
                            <span class="text-xs text-slate-400">5分钟前</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-amber-500 rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-slate-900 dark:text-slate-100">系统维护通知</p>
                                    <p class="text-xs text-slate-500 dark:text-slate-400">今晚23:00-01:00维护</p>
                                </div>
                            </div>
                            <span class="text-xs text-slate-400">1小时前</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 border-t border-slate-200 dark:border-slate-700 flex justify-between">
                <button id="logout-btn" class="px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 flex items-center">
                    <i data-lucide="log-out" class="h-4 w-4 mr-2"></i>
                    退出登录
                </button>
                <div class="flex space-x-3">
                    <button id="close-user-modal" class="px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 transition-colors">
                        关闭
                    </button>
                    <button class="px-4 py-2 text-sm font-medium bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                        个人设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm user-modal hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="notification-modal-content">
            <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 text-white rounded-full flex items-center justify-center shadow-lg">
                            <i data-lucide="bell" class="h-5 w-5"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-slate-100">系统通知</h3>
                            <p class="text-sm text-slate-500 dark:text-slate-400">查看最新的系统消息和任务通知</p>
                        </div>
                    </div>
                    <button id="close-notification-modal" class="p-2 rounded-full text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 transition-all duration-200">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
            </div>

            <div class="p-6 max-h-96 overflow-y-auto">
                <div id="notification-list" class="space-y-4">
                    <!-- 系统通知 -->
                    <div class="notification-item p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-blue-900 dark:text-blue-100">系统更新通知</h4>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">系统已更新到最新版本，新增了批量查询功能。</p>
                                <p class="text-xs text-blue-500 dark:text-blue-400 mt-2">2小时前</p>
                            </div>
                        </div>
                    </div>

                    <!-- 任务完成通知 -->
                    <div class="notification-item p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-green-900 dark:text-green-100">查询任务完成</h4>
                                <p class="text-sm text-green-700 dark:text-green-300 mt-1">您的船期查询任务已完成，共找到5条匹配记录。</p>
                                <p class="text-xs text-green-500 dark:text-green-400 mt-2">1天前</p>
                            </div>
                        </div>
                    </div>

                    <!-- 维护通知 -->
                    <div class="notification-item p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                        <div class="flex items-start space-x-3">
                            <div class="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-amber-900 dark:text-amber-100">系统维护通知</h4>
                                <p class="text-sm text-amber-700 dark:text-amber-300 mt-1">系统将于今晚23:00-01:00进行维护，期间可能无法访问。</p>
                                <p class="text-xs text-amber-500 dark:text-amber-400 mt-2">3天前</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 border-t border-slate-200 dark:border-slate-700 flex justify-between">
                <button id="mark-all-read" class="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 flex items-center">
                    <i data-lucide="check-circle" class="h-4 w-4 mr-2"></i>
                    全部已读
                </button>
                <button id="close-notification-modal-btn" class="px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 transition-colors">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 新建查询弹窗 -->
    <div id="new-query-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm user-modal hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl w-full max-w-2xl transform transition-all duration-300 scale-95 opacity-0" id="new-query-modal-content">
            <div class="p-6 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full flex items-center justify-center shadow-lg">
                            <i data-lucide="plus" class="h-5 w-5"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-slate-900 dark:text-slate-100">新建查询任务</h3>
                            <p class="text-sm text-slate-500 dark:text-slate-400">选择单个添加或批量添加模式</p>
                        </div>
                    </div>
                    <button id="close-new-query-modal" class="p-2 rounded-full text-slate-400 hover:text-slate-600 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 transition-all duration-200">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>

                <!-- 标签页切换 -->
                <div class="flex mt-4 bg-slate-100 dark:bg-slate-700 p-1 rounded-lg">
                    <button id="single-mode-tab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 bg-white dark:bg-slate-600 text-blue-600 dark:text-blue-400 shadow-sm">
                        <i data-lucide="file-text" class="h-4 w-4 inline mr-2"></i>
                        单个添加
                    </button>
                    <button id="batch-mode-tab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200">
                        <i data-lucide="layers" class="h-4 w-4 inline mr-2"></i>
                        批量添加
                    </button>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 单个模式容器 -->
                <form id="single-mode" class="space-y-5">
                    <!-- 输入框 -->
                    <div class="space-y-2">
                        <label for="tracking-input" class="block text-sm font-semibold text-slate-700 dark:text-slate-300">
                            提单号/集装箱号
                        </label>
                        <div class="relative">
                            <input
                                type="text"
                                id="tracking-input"
                                placeholder="例如：MEDUJ0618622, MSKU1234567, TCLU1234567"
                                class="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-xl text-slate-900 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-center font-mono text-lg tracking-wider"
                                maxlength="20"
                                autocomplete="off"
                            >
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <div id="input-status" class="hidden">
                                    <!-- 状态图标将在这里显示 -->
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-slate-500 dark:text-slate-400 text-center">
                            支持MSC、COSCO、马士基、CMA CGM等主要船公司
                        </p>
                    </div>

                    <!-- 承运人信息 -->
                    <div id="carrier-info" class="hidden space-y-4 p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center">
                                <i data-lucide="ship" class="h-4 w-4 mr-2 text-blue-500"></i>
                                承运人信息
                            </h4>
                            <span id="detection-badge" class="px-2 py-1 bg-green-100 dark:bg-green-500/10 text-green-800 dark:text-green-300 text-xs font-medium rounded-full">
                                自动识别
                            </span>
                        </div>

                        <div class="grid grid-cols-1 gap-3">
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-600">
                                <div class="flex items-center space-x-3">
                                    <div id="carrier-logo" class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                        <!-- 船公司logo首字母 -->
                                    </div>
                                    <div>
                                        <p id="carrier-name" class="text-sm font-medium text-slate-900 dark:text-slate-100">
                                            <!-- 船公司名称 -->
                                        </p>
                                        <p id="carrier-code" class="text-xs text-slate-500 dark:text-slate-400">
                                            <!-- 船公司代码 -->
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p id="carrier-website" class="text-xs text-blue-600 dark:text-blue-400 hover:underline cursor-pointer">
                                        <!-- 官网链接 -->
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- 输入验证状态 -->
                        <div id="tracking-validation" class="flex items-center space-x-2 text-sm">
                            <div class="flex items-center text-green-600 dark:text-green-400">
                                <i data-lucide="check-circle" class="h-4 w-4 mr-2"></i>
                                <span>提单号格式正确</span>
                            </div>
                        </div>
                    </div>

                    <!-- 错误提示 -->
                    <div id="error-message" class="hidden p-4 bg-red-50 dark:bg-red-500/10 border border-red-200 dark:border-red-500/20 rounded-xl">
                        <div class="flex items-center text-red-800 dark:text-red-300">
                            <i data-lucide="alert-circle" class="h-4 w-4 mr-2 flex-shrink-0"></i>
                            <span id="error-text">请输入有效的提单号或集装箱号</span>
                        </div>
                    </div>
                </form>

                <!-- 批量模式容器 -->
                <div id="batch-mode" class="space-y-4 hidden">
                    <div>
                        <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-2">批量输入</label>
                        <textarea id="batch-input" rows="8" placeholder="每行一个：可粘贴Excel列或手动换行\n例如：\nMEDUJ0618622\nMSKU1234567\nTCLU7654321"
                            class="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700/50 border border-slate-300 dark:border-slate-600 rounded-xl text-slate-900 dark:text-slate-200 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-mono"></textarea>
                        <p class="mt-2 text-xs text-slate-500 dark:text-slate-400">自动去重、自动识别承运人；最多一次添加200条。</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center">
                                    <i data-lucide="list-checks" class="h-4 w-4 mr-2 text-blue-500"></i>
                                    待添加清单
                                </h4>
                                <span class="text-xs text-slate-500" id="batch-stats">0 条有效 · 0 条重复/无效</span>
                            </div>
                            <div id="batch-preview" class="max-h-56 overflow-auto pr-2 space-y-2">
                                <!-- 动态生成 -->
                            </div>
                        </div>
                        <div class="p-4 bg-slate-50 dark:bg-slate-700/30 rounded-xl border border-slate-200 dark:border-slate-600">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center">
                                    <i data-lucide="info" class="h-4 w-4 mr-2 text-blue-500"></i>
                                    说明
                                </h4>
                            </div>
                            <ul class="text-sm text-slate-600 dark:text-slate-300 list-disc pl-5 space-y-1">
                                <li>支持混合粘贴提单号与箱号，自动识别类型与承运人</li>
                                <li>重复项会自动过滤；无效项会被忽略</li>
                                <li>最多一次添加200条，过多会被截断</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-6 border-t border-slate-200 dark:border-slate-700 flex justify-end space-x-3">
                <button id="cancel-query" class="px-6 py-2.5 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all duration-200">
                    取消
                </button>
                <button id="confirm-query" class="px-6 py-2.5 text-sm font-medium bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-lg transition-all duration-300 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/40 transform hover:-translate-y-0.5 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none flex items-center" disabled>
                    <i data-lucide="search" class="h-4 w-4 mr-2"></i>
                    <span>开始查询</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 自定义退出登录确认弹窗 -->
    <div id="logout-modal" class="fixed inset-0 z-[10000] hidden flex items-center justify-center">
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
        <div class="relative bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-6 mx-4 max-w-sm w-full transform transition-all duration-300 scale-95 opacity-0" id="logout-modal-content">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="log-out" class="h-8 w-8 text-white"></i>
                </div>
                <h3 class="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-2">退出登录</h3>
                <p class="text-slate-600 dark:text-slate-400 mb-6">确定要退出当前账户吗？</p>
                <div class="flex space-x-3">
                    <button id="logout-cancel" class="flex-1 px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-all duration-200">
                        取消
                    </button>
                    <button id="logout-confirm" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-lg transition-all duration-200 transform hover:scale-105">
                        确定退出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统信息弹窗 -->
    <div id="system-info-modal" class="fixed inset-0 z-50 hidden">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity"></div>
        <div class="fixed inset-0 flex items-center justify-center p-4">
            <div id="system-info-content" class="relative bg-white dark:bg-slate-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden transform scale-95 opacity-0 transition-all duration-300">
                <!-- 头部 -->
                <div class="relative bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 px-8 py-6 text-white overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-purple-500/20 animate-pulse"></div>
                    <div class="relative z-10 flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                                <i data-lucide="anchor" class="h-8 w-8 text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold">E船期查询系统</h3>
                                <p class="text-blue-100 text-sm">Enterprise Shipping Schedule Query Platform</p>
                            </div>
                        </div>
                        <button id="close-system-info" class="p-2 rounded-full hover:bg-white/20 transition-colors">
                            <i data-lucide="x" class="h-6 w-6"></i>
                        </button>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="p-8 max-h-[calc(90vh-120px)] overflow-y-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- 系统信息 -->
                        <div class="space-y-6">
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200/50 dark:border-blue-700/50">
                                <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                                    <i data-lucide="info" class="h-5 w-5 mr-2 text-blue-600"></i>
                                    系统信息
                                </h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">版本号</span>
                                        <span id="modal-version" class="font-mono text-slate-900 dark:text-slate-100">v2025.08.28-01</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">构建时间</span>
                                        <span class="font-mono text-slate-900 dark:text-slate-100">2025-08-28 15:30</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">环境</span>
                                        <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-xs font-medium">Production</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">API版本</span>
                                        <span class="font-mono text-slate-900 dark:text-slate-100">v1.0</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl p-6 border border-emerald-200/50 dark:border-emerald-700/50">
                                <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                                    <i data-lucide="activity" class="h-5 w-5 mr-2 text-emerald-600"></i>
                                    系统状态
                                </h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between items-center">
                                        <span class="text-slate-600 dark:text-slate-400">服务状态</span>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                            <span class="text-green-600 dark:text-green-400 font-medium">正常运行</span>
                                        </div>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">响应时间</span>
                                        <span class="text-slate-900 dark:text-slate-100">< 200ms</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-slate-600 dark:text-slate-400">可用性</span>
                                        <span class="text-slate-900 dark:text-slate-100">99.9%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 技术栈与功能 -->
                        <div class="space-y-6">
                            <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50">
                                <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                                    <i data-lucide="code" class="h-5 w-5 mr-2 text-purple-600"></i>
                                    技术栈
                                </h4>
                                <div class="grid grid-cols-2 gap-3 text-sm">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">HTML5 / CSS3</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">JavaScript ES6+</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-cyan-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">Tailwind CSS</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-indigo-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">Lucide Icons</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">RESTful API</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                        <span class="text-slate-700 dark:text-slate-300">PWA Support</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200/50 dark:border-orange-700/50">
                                <h4 class="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center">
                                    <i data-lucide="ship" class="h-5 w-5 mr-2 text-orange-600"></i>
                                    核心功能
                                </h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="check-circle" class="h-4 w-4 text-green-500"></i>
                                        <span class="text-slate-700 dark:text-slate-300">多承运人船期查询</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="check-circle" class="h-4 w-4 text-green-500"></i>
                                        <span class="text-slate-700 dark:text-slate-300">实时物流轨迹追踪</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="check-circle" class="h-4 w-4 text-green-500"></i>
                                        <span class="text-slate-700 dark:text-slate-300">智能数据分析</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="check-circle" class="h-4 w-4 text-green-500"></i>
                                        <span class="text-slate-700 dark:text-slate-300">批量查询处理</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <i data-lucide="check-circle" class="h-4 w-4 text-green-500"></i>
                                        <span class="text-slate-700 dark:text-slate-300">数据导出功能</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部信息 -->
                    <div class="mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
                        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                            <div class="text-center md:text-left">
                                <p class="text-sm text-slate-600 dark:text-slate-400">
                                    © 2025 E船期查询系统. 专业的企业级物流查询平台
                                </p>
                                <p class="text-xs text-slate-500 dark:text-slate-500 mt-1">
                                    为全球贸易提供高效、准确的船期信息服务
                                </p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-2 px-4 py-2 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-colors text-sm">
                                    <i data-lucide="github" class="h-4 w-4"></i>
                                    <span>GitHub</span>
                                </button>
                                <button class="flex items-center space-x-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/40 text-blue-700 dark:text-blue-300 rounded-lg transition-colors text-sm">
                                    <i data-lucide="book-open" class="h-4 w-4"></i>
                                    <span>文档</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API配置（使用相对当前站点的根，避免 localhost/127.0.0.1 不一致）
        // 应用版本号（用于你核对是否最新）。每次我更新会同步递增。
        const APP_VERSION = '2025.08.28-01';

        const API_BASE_URL = `${location.origin}/api/v1`;

        // 认证token和用户信息
        let authToken = null;
        let currentUser = null;

        // --- 登录链路调试开关与日志器（默认关闭，仅debug=1或本地开关打开时启用） ---
        const DEBUG_LOGIN = (new URLSearchParams(location.search).get('debug') === '1') || (localStorage.getItem('debug_login') === '1');
        const loginDebug = {
            enabled: DEBUG_LOGIN,
            logs: [],
            log(step, msg, data) {
                if (!this.enabled) return;
                const entry = { t: new Date().toISOString(), step, msg, data };
                this.logs.push(entry);
                try { console.log('[LOGINDBG]', step, msg, data || ''); } catch {}
            },
            show() {
                if (!this.enabled) return;
                const html = `
                    <div class="p-4 h-full overflow-hidden flex flex-col">
                        <div class="mb-3 flex items-center gap-2">
                            <button id="ld-copy" class="px-3 py-2 text-xs font-medium bg-slate-200 dark:bg-slate-700 rounded">复制日志</button>
                            <button id="ld-clear" class="px-3 py-2 text-xs font-medium bg-slate-200 dark:bg-slate-700 rounded">清空</button>
                        </div>
                        <pre id="ld-pre" class="flex-1 overflow-auto bg-slate-900 text-slate-100 text-xs p-3 rounded">${this.logs.map(l=>JSON.stringify(l)).join('\n')}</pre>
                    </div>`;
                if (typeof openModal === 'function') {
                    openModal('登录链路调试日志', html);
                    setTimeout(() => {
                        const pre = document.getElementById('ld-pre');
                        const copyBtn = document.getElementById('ld-copy');
                        const clrBtn = document.getElementById('ld-clear');
                        copyBtn && (copyBtn.onclick = async ()=>{
                            try { await navigator.clipboard.writeText(this.logs.map(l=>JSON.stringify(l)).join('\n')); showNotification && showNotification('已复制','日志已复制','success'); } catch {}
                        });
                        clrBtn && (clrBtn.onclick = ()=>{ this.logs = []; pre.textContent = ''; });
                    }, 0);
                } else {
                    alert(this.logs.map(l=>JSON.stringify(l)).join('\n'));
                }
            }
        };


        // 检查认证状态
        async function checkAuth() {
            loginDebug.log('checkAuth.start', { hasAuthData: !!localStorage.getItem('auth_data') });
            const authData = localStorage.getItem('auth_data');
            if (authData) {
                try {
                    const userData = JSON.parse(authData);
                    authToken = userData.access_token;

                    // 验证token是否还有效
                    const isValid = await verifyAuthToken();
                    if (isValid) {
                        hideAuthOverlay();
                        updateUserInfo(currentUser);
                        return true;
                    } else {
                        // token过期，清除本地存储
                        localStorage.removeItem('auth_data');
                        authToken = null;
                        currentUser = null;
                    }
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                    localStorage.removeItem('auth_data');
                    authToken = null;
                    currentUser = null;
                }
            }
            redirectToLogin();
            return false;
        }

        // 验证认证token
        async function verifyAuthToken() {
            if (!authToken) return false;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/profile`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) { loginDebug.log('verifyAuthToken.ok', { status: response.status });
                    currentUser = await response.json();
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                console.error('验证token失败:', error);
                return false;
            }
        }

        // 登录重定向（替代旧遮罩）
        function redirectToLogin() {
            try {
                const url = new URL('login.html', location.href);
                const qs = new URLSearchParams(location.search);
                if (qs.get('debug') === '1') url.searchParams.set('debug', '1');
                if (!url.searchParams.get('v')) {
                    const v = (typeof APP_VERSION !== 'undefined' ? APP_VERSION : document.lastModified.replace(/\D/g,'').slice(0,12));
                    url.searchParams.set('v', v);
                }
                location.href = url.toString();
            } catch (e) {}
        }

        // 兼容旧调用：显示遮罩 => 重定向；隐藏遮罩 => 空操作
        function showAuthOverlay() { redirectToLogin(); }
        function hideAuthOverlay() { /* overlay removed */ }

        // 更新用户信息显示
        function updateUserInfo(userInfo) {
            if (userInfo) {
                // 更新侧边栏用户信息
                document.getElementById('user-display-name').textContent = userInfo.name;
                document.getElementById('user-display-role').textContent = userInfo.role;

                // 更新用户弹窗信息
                document.getElementById('modal-user-name').textContent = userInfo.name;
                document.getElementById('modal-user-info').textContent = `${userInfo.role} · ID: ${userInfo.user_id}`;

                // 更新头像
                const avatarElements = document.querySelectorAll('.w-10.h-10, .w-16.h-16');
                avatarElements.forEach(el => {
                    if (el.textContent.trim().length === 1) {
                        el.textContent = userInfo.avatar;
                    }
                });
            }
        }

        // 登录表单逻辑已迁移至 login.html

        // 自定义退出登录弹窗函数
        function showLogoutModal() {
            const modal = document.getElementById('logout-modal');
            const content = document.getElementById('logout-modal-content');
            modal.classList.remove('hidden');

            // 动画效果
            setTimeout(() => {
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        function hideLogoutModal() {
            const modal = document.getElementById('logout-modal');
            const content = document.getElementById('logout-modal-content');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        async function performLogout() {
            // 先调用API登出
            if (authToken) {
                try {
                    await fetch(`${API_BASE_URL}/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    console.log('API登出成功');
                } catch (error) {
                    console.error('API登出失败:', error);
                    // 即使API失败也继续本地登出
                }
            }

            // 停止自动刷新
            if (autoRefreshInterval) {
                clearTimeout(autoRefreshInterval);
                autoRefreshInterval = null;
                console.log('已停止自动刷新');
            }

            // 清除本地数据
            localStorage.removeItem('auth_data');
            authToken = null;
            currentUser = null;

            // 关闭用户弹窗
            const closeUserModal = document.getElementById('close-user-modal');
            if (closeUserModal) {
                closeUserModal.click();
            }

            hideLogoutModal();
            redirectToLogin();
        }

        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', async function() {
            const loggedIn = await checkAuth();
            if (loggedIn) {
                try {
                    showTableSkeleton();
                    await fetchTasksFromAPI();
                } catch (e) {
                    console.error('初始化加载任务失败:', e);
                } finally {
                    renderTable();
                }
                
                // 登录成功后启动自动刷新
                startAutoRefresh();
            }

            // 绑定退出登录弹窗事件
            const logoutModal = document.getElementById('logout-modal');
            const logoutCancel = document.getElementById('logout-cancel');
            const logoutConfirm = document.getElementById('logout-confirm');

            // 点击背景关闭弹窗
            logoutModal.addEventListener('click', (e) => {
                if (e.target === logoutModal) {
                    hideLogoutModal();
                }
            });

            // 取消按钮
            logoutCancel.addEventListener('click', hideLogoutModal);

            // 确认退出按钮
            logoutConfirm.addEventListener('click', performLogout);

            // 绑定退出登录事件 - 用户弹窗中的退出按钮
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', showLogoutModal);
            }

            // 绑定退出登录事件 - 侧边栏中的退出按钮
            const sidebarLogoutBtn = document.getElementById('sidebar-logout-btn');
            if (sidebarLogoutBtn) {
                sidebarLogoutBtn.addEventListener('click', showLogoutModal);
            }
        });


            // 渲染登录页和侧边栏上的版本信息
            (function renderVersionBadges(){
                try {
                    const v = (typeof APP_VERSION !== 'undefined' ? APP_VERSION : (window.__APP_VERSION__ || '-'));

                    // 更新弹窗中的版本信息
                    const modalVersion = document.getElementById('modal-version');
                    if (modalVersion) modalVersion.textContent = `v${v}`;
                } catch (e) { console.warn('版本信息渲染失败', e); }
            })();

        // 系统信息弹窗功能
        function showSystemInfoModal() {
            const modal = document.getElementById('system-info-modal');
            const content = document.getElementById('system-info-content');
            modal.classList.remove('hidden');

            // 动画效果
            setTimeout(() => {
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        function hideSystemInfoModal() {
            const modal = document.getElementById('system-info-modal');
            const content = document.getElementById('system-info-content');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }

        // 绑定系统信息弹窗事件
        document.getElementById('system-title-btn')?.addEventListener('click', showSystemInfoModal);
        document.getElementById('close-system-info')?.addEventListener('click', hideSystemInfoModal);

        // 点击背景关闭弹窗
        document.getElementById('system-info-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                hideSystemInfoModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('system-info-modal');
                if (modal && !modal.classList.contains('hidden')) {
                    hideSystemInfoModal();
                }
            }
        });

        // 初始化Lucide图标
        lucide.createIcons();

        // --- Elements ---
        const tableBody = document.getElementById('table-body');
        const searchInput = document.getElementById('search-input');
        const statusFilter = document.getElementById('status-filter');
        const etaFilter = document.getElementById('eta-filter');
        const searchButton = document.getElementById('search-button');
        const resetButton = document.getElementById('reset-button');
        const userProfileBtn = document.getElementById('user-profile-btn');
        const userModal = document.getElementById('user-modal');
        const userModalContent = document.getElementById('user-modal-content');
        const closeUserModal = document.getElementById('close-user-modal');

        // 通知弹窗相关元素
        const notificationBtn = document.getElementById('notification-btn');
        const notificationModal = document.getElementById('notification-modal');
        const notificationModalContent = document.getElementById('notification-modal-content');
        const closeNotificationModal = document.getElementById('close-notification-modal');
        const closeNotificationModalBtn = document.getElementById('close-notification-modal-btn');
        const markAllReadBtn = document.getElementById('mark-all-read');

        // 新建查询弹窗相关元素
        const newQueryBtn = document.getElementById('new-query-btn');
        const newQueryModal = document.getElementById('new-query-modal');
        const newQueryModalContent = document.getElementById('new-query-modal-content');
        const closeNewQueryModal = document.getElementById('close-new-query-modal');
        const trackingInput = document.getElementById('tracking-input');
        const carrierInfo = document.getElementById('carrier-info');
        const errorMessage = document.getElementById('error-message');
        const confirmQueryBtn = document.getElementById('confirm-query');
        const cancelQueryBtn = document.getElementById('cancel-query');

        // 标签页和批量模式元素
        const singleModeTab = document.getElementById('single-mode-tab');
        const batchModeTab = document.getElementById('batch-mode-tab');
        const singleMode = document.getElementById('single-mode');
        const batchMode = document.getElementById('batch-mode');
        const batchInput = document.getElementById('batch-input');
        const batchPreview = document.getElementById('batch-preview');
        const batchStats = document.getElementById('batch-stats');

        // 分页相关元素
        let currentPage = 1;
        let itemsPerPage = 20;
        let filteredData = [];
        let tableDensity = 'normal'; // 'compact', 'normal', 'comfortable'

        // --- Table Data ---
        let tableData = [];


            // 全局骨架屏，供首次加载 / 搜索 / 刷新调用
            function showTableSkeleton(rows = itemsPerPage) {
                if (!tableBody) return;
                tableBody.innerHTML = '';
                for (let i = 0; i < rows; i++) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td class="px-6 py-4"><div class="h-4 w-8 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-40 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-56 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-16 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-24 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-10 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-28 rounded skeleton"></div></td>
                        <td class="px-6 py-4"><div class="h-4 w-28 rounded skeleton"></div></td>
                    `;
                    tableBody.appendChild(tr);
                }
            }

            // 隐藏表格骨架屏
            function hideTableSkeleton() {
                if (!tableBody) return;
                // 清空骨架行，等待后续 renderTable() 重新渲染
                tableBody.innerHTML = '';
            }


        // Fetch tasks from API
    async function fetchTasksFromAPI() {
            try {
                // 显示表格骨架屏
                showTableSkeleton();

                // 检查是否有认证token
                if (!authToken || !currentUser) {
                    console.log('未登录，清空任务数据');
                    tableData = [];
                    hideTableSkeleton();
                    return;
                }

                // 调用用户任务API（根据状态筛选拼接查询参数）
                const statusEl = document.getElementById('status-filter');
                const statusTerm = statusEl ? statusEl.value : 'all';
                const statusMapToApi = {
                    '已完成': 'completed',
                    '查询失败': 'failed,cancelled',
                    '查询中': 'pending,running'
                };
                const qs = new URLSearchParams({ limit: '100', offset: '0' });
                const statusParam = statusMapToApi[statusTerm];
                if (statusParam) qs.set('status_filter', statusParam);
                const response = await apiCall(`/tasks/my-tasks?${qs.toString()}`);

                // Store latest tasks data for modal functions
                window.__latestTasks = response || [];

                // Map API response to table format
                tableData = (response || []).map(task => {
                    // Map status from API to display format
                    let statusDisplay = '已完成';
                    switch(task.status) {
                        case 'pending':
                            statusDisplay = '查询中';
                            break;
                        case 'running':
                            statusDisplay = '查询中';
                            break;
                        case 'completed':
                            statusDisplay = '已完成';
                            break;
                        case 'failed':
                            statusDisplay = '查询失败';
                            break;
                        case 'cancelled':
                            statusDisplay = '查询失败';
                            break;
                        default:
                            statusDisplay = '查询中';
                    }

                    // Extract carrier name
                    let companyName = '未知承运人';
                    if (task.carrier_code) {
                        // Try to find exact match in CARRIER_RULES
                        const rule = CARRIER_RULES.find(r => r.code === task.carrier_code.toUpperCase());
                        if (rule) {
                            companyName = rule.fullName;
                        } else {
                            // Fallback mapping for known codes
                            switch(task.carrier_code.toUpperCase()) {
                                case 'MSC':
                                    companyName = 'MSC (Mediterranean Shipping Company / 地中海航运)';
                                    break;
                                case 'COSCO':
                                    companyName = 'COSCO (中远海运)';
                                    break;
                                case 'MAERSK':
                                    companyName = 'Maersk (马士基)';
                                    break;
                                case 'CMA_CGM':
                                    companyName = 'CMA CGM (达飞海运)';
                                    break;
                                case 'EVERGREEN':
                                    companyName = 'Evergreen (长荣海运)';
                                    break;
                                case 'HAPAG_LLOYD':
                                    companyName = 'Hapag-Lloyd (赫伯罗特)';
                                    break;
                                default:
                                    companyName = `${task.carrier_code} (承运人)`;
                            }
                        }
                    }

                    // Format dates
                    const formatDate = (dateStr) => {
                        if (!dateStr) return new Date().toISOString().split('T')[0];
                        try {
                            return new Date(dateStr).toISOString().split('T')[0];
                        } catch {
                            return new Date().toISOString().split('T')[0];
                        }
                    };

                    return {
                        taskId: task.task_id,
                        trackingNumber: task.container_number,
                        company: companyName,
                        status: statusDisplay,
                        eta: task.result?.estimated_arrival_time || task.estimated_arrival_time || '-',
                        nodes: (task.result?.tracking_points?.length || task.shipment_dates?.length) ?
                               `${task.result?.tracking_points?.length || task.shipment_dates?.length}条` : '-',
                        voucher: !!(task.result?.screenshots?.length || task.evidence_screenshot),
                        createdTime: formatDate(task.created_at),
                        updatedTime: formatDate(task.updated_at)
                    };
                });

                console.log('任务数据加载完成:', tableData.length);
                hideTableSkeleton();

            } catch (error) {
                console.error('获取任务数据失败:', error);
                hideTableSkeleton();

                // 使用空数据而不是mock数据
                tableData = [];

                // 显示错误提示
                if (error.message.includes('401') || error.message.includes('403')) {
                    showNotification('认证失败', '请重新登录', 'error');
                    // 清除认证信息，触发重新登录
                    localStorage.removeItem('auth_data');
                    authToken = null;
                    currentUser = null;
                    redirectToLogin();
                } else {
                    showNotification('获取任务失败', error.message || '网络错误', 'error');
                }
            }
        }

        // --- Functions ---
        function getStatusBadge(status) {
            const isDark = document.documentElement.classList.contains('dark');
            switch (status) {
                case '已完成':
                    return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/10 text-green-300"><svg class="w-2 h-2 mr-1.5 text-green-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>已完成</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><svg class="w-2 h-2 mr-1.5 text-green-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>已完成</span>`;
                case '查询中':
                     return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-300"><svg class="w-2 h-2 mr-1.5 text-yellow-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询中...</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><svg class="w-2 h-2 mr-1.5 text-yellow-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询中...</span>`;
                case '查询失败':
                    return isDark
                        ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-500/10 text-red-300"><svg class="w-2 h-2 mr-1.5 text-red-400" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询失败</span>`
                        : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"><svg class="w-2 h-2 mr-1.5 text-red-500" fill="currentColor" viewBox="0 0 8 8"><circle cx="4" cy="4" r="3"></circle></svg>查询失败</span>`;
            }
        }

        function updateStats() {
            const total = filteredData.length;
            const running = filteredData.filter(row => row.status === '查询中').length;
            const success = filteredData.filter(row => row.status === '已完成').length;
            const failed = filteredData.filter(row => row.status === '查询失败').length;

            document.getElementById('stats-total').textContent = `总计: ${total}`;
            document.getElementById('stats-running').textContent = `运行中: ${running}`;
            document.getElementById('stats-success').textContent = `成功: ${success}`;
            document.getElementById('stats-failed').textContent = `失败: ${failed}`;
        }

        function renderPagination() {
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            const pageNumbers = document.getElementById('page-numbers');
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');

            // 更新分页信息
            const start = Math.min((currentPage - 1) * itemsPerPage + 1, filteredData.length);
            const end = Math.min(currentPage * itemsPerPage, filteredData.length);
            document.getElementById('show-start').textContent = start;
            document.getElementById('show-end').textContent = end;
            document.getElementById('total-records').textContent = filteredData.length;

            // 更新按钮状态
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages || totalPages === 0;

            // 生成页码按钮
            pageNumbers.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                if (totalPages <= 7 || i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const btn = document.createElement('button');
                    btn.className = `pagination-button px-3 py-1 text-sm rounded ${i === currentPage ? 'bg-blue-500 text-white' : 'bg-white dark:bg-slate-700 border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600'}`;
                    btn.textContent = i;
                    btn.onclick = () => {
                        currentPage = i;
                        renderTable();
                    };
                    pageNumbers.appendChild(btn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 text-slate-400';
                    ellipsis.textContent = '...';
                    pageNumbers.appendChild(ellipsis);
                }
            }
        }

        function renderTable() {
            const isDark = document.documentElement.classList.contains('dark');
            const searchTerm = searchInput.value.trim().toLowerCase();
            const statusTerm = statusFilter.value;
            const etaTerm = etaFilter.value;

            // 筛选数据
            filteredData = tableData.filter(row => {
                const matchSearch = !searchTerm || row.trackingNumber.toLowerCase().includes(searchTerm);
                const matchStatus = !statusTerm || statusTerm === 'all' || row.status === statusTerm;
                const matchEta = !etaTerm || row.eta === etaTerm;
                return matchSearch && matchStatus && matchEta;
            });

            // 分页数据
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const paginatedData = filteredData.slice(startIndex, endIndex);

            tableBody.innerHTML = '';

            const rowClass = isDark ? 'hover:bg-slate-700/50' : 'bg-white hover:bg-slate-50';
            const mainTextColor = isDark ? 'text-slate-300' : 'text-slate-700';
            const subTextColor = isDark ? 'text-slate-500' : 'text-slate-500';
            const monoTextColor = isDark ? 'text-slate-200' : 'text-slate-900';
            const linkColor = isDark ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800';
            const pyClass = tableDensity === 'compact' ? 'py-2' : (tableDensity === 'comfortable' ? 'py-6' : 'py-4');

            paginatedData.forEach((row, index) => {
                const globalIndex = startIndex + index + 1;
                const tr = document.createElement('tr');
                tr.className = `${rowClass} transition-colors duration-200`;

                tr.innerHTML = `
                    <td class="px-6 ${pyClass} ${mainTextColor}">${globalIndex}</td>
                    <td class="px-6 ${pyClass} font-mono ${monoTextColor}">${row.trackingNumber}</td>
                    <td class="px-6 ${pyClass} ${mainTextColor}">${row.company}</td>
                    <td class="px-6 ${pyClass}">
                        ${getStatusBadge(row.status)}
                        ${row.needsRetry ? `<button onclick="retryTask('${row.taskId}')" class="ml-2 px-2 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600 transition-colors">重试</button>` : ''}
                    </td>
                    <td class="px-6 ${pyClass} ${mainTextColor}">${row.eta}</td>
                    <td class="px-6 ${pyClass}"><a href="#" onclick="showTaskDetail('${row.taskId || ''}', '${row.trackingNumber || ''}')" class="${linkColor} font-medium hover:underline">${row.nodes}</a></td>
                    <td class="px-6 ${pyClass}"><a href="#" onclick="showImagePreview('${row.taskId || ''}', '${row.trackingNumber || ''}')" class="${linkColor} font-medium hover:underline">查看截图</a></td>
                    <td class="px-6 ${pyClass} ${subTextColor}">${row.createdTime}</td>
                    <td class="px-6 ${pyClass} ${subTextColor}">${row.updatedTime}</td>
                `;
                tableBody.appendChild(tr);
            });

            updateStats();
            renderPagination();
        }

        // 重试失败的任务创建
        async function retryTask(taskId) {
            console.log('重试任务:', taskId);

            const taskIndex = tableData.findIndex(t => t.taskId === taskId);
            if (taskIndex === -1) {
                showNotification('重试失败', '找不到要重试的任务', 'error');
                return;
            }

            const task = tableData[taskIndex];
            const originalTracking = task.originalTracking || task.trackingNumber;

            try {
                // 更新状态为重试中
                task.status = '重试中';
                task.needsRetry = false;
                renderTable();

                showNotification('重试中', `正在重新创建任务 ${originalTracking}...`, 'info');

                // 重新调用API
                const result = await apiCall('/tasks/quick-create', {
                    method: 'POST',
                    body: JSON.stringify({
                        container_number: originalTracking
                    })
                });

                console.log('重试成功:', result);

                // 更新任务为成功状态
                const info = getCarrierByTracking(originalTracking);
                tableData[taskIndex] = {
                    taskId: result.task_id || taskId,
                    trackingNumber: result.container_number || originalTracking,
                    company: info ? info.name : task.company,
                    status: '查询中',
                    eta: '-',
                    nodes: '-',
                    voucher: false,
                    createdTime: result.created_at ? result.created_at.split('T')[0] : task.createdTime,
                    updatedTime: result.updated_at ? result.updated_at.split('T')[0] : new Date().toISOString().split('T')[0],
                    isOptimistic: false
                };

                renderTable();
                showNotification('重试成功', `任务 ${originalTracking} 创建成功`, 'success');

            } catch (error) {
                console.error('重试失败:', error);

                // 恢复失败状态和重试按钮
                task.status = '创建失败';
                task.needsRetry = true;
                renderTable();

                if (error.message.includes('401') || error.message.includes('403')) {
                    showNotification('认证失败', '请重新登录', 'error');
                    localStorage.removeItem('auth_data');
                    authToken = null;
                    currentUser = null;
                    redirectToLogin();
                } else {
                    showNotification('重试失败', `${error.message}，请稍后再试`, 'error');
                }
            }
        }

        function setTheme(isDark) {
            if (isDark) {
                document.documentElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            }
            renderTable();
        }

        // --- Event Listeners ---
        searchButton.addEventListener('click', () => {
            currentPage = 1;
            showTableSkeleton();
            setTimeout(() => renderTable(), 120);
        });

        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                currentPage = 1;
                showTableSkeleton();
                setTimeout(() => renderTable(), 120);
            }
        });

        resetButton.addEventListener('click', () => {
            searchInput.value = '';
            statusFilter.value = 'all';
            etaFilter.value = '';
            currentPage = 1;
            showTableSkeleton();
            setTimeout(() => renderTable(), 120);
        });

        // 用户弹窗事件
        userProfileBtn.addEventListener('click', () => {
            userModal.classList.remove('hidden');
            setTimeout(() => {
                userModalContent.classList.remove('scale-95', 'opacity-0');
                userModalContent.classList.add('scale-100', 'opacity-100');
            }, 50);
        });

        closeUserModal.addEventListener('click', () => {
            userModalContent.classList.remove('scale-100', 'opacity-100');
            userModalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                userModal.classList.add('hidden');
            }, 300);
        });

        userModal.addEventListener('click', (e) => {
            if (e.target === userModal) {
                closeUserModal.click();
            }
        });

        // 通知弹窗事件
        notificationBtn.addEventListener('click', () => {
            notificationModal.classList.remove('hidden');
            setTimeout(() => {
                notificationModalContent.classList.remove('scale-95', 'opacity-0');
                notificationModalContent.classList.add('scale-100', 'opacity-100');
            }, 50);
        });

        closeNotificationModal.addEventListener('click', () => {
            notificationModalContent.classList.remove('scale-100', 'opacity-100');
            notificationModalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                notificationModal.classList.add('hidden');
            }, 300);
        });

        closeNotificationModalBtn.addEventListener('click', () => {
            closeNotificationModal.click();
        });

        notificationModal.addEventListener('click', (e) => {
            if (e.target === notificationModal) {
                closeNotificationModal.click();
            }
        });

        // 标记全部已读功能
        markAllReadBtn.addEventListener('click', () => {
            const notificationItems = document.querySelectorAll('.notification-item');
            notificationItems.forEach(item => {
                item.style.opacity = '0.6';
                const dot = item.querySelector('.w-2.h-2');
                if (dot) {
                    dot.classList.remove('bg-blue-500', 'bg-green-500', 'bg-amber-500');
                    dot.classList.add('bg-gray-400');
                }
            });

            // 隐藏通知红点
            const notificationDot = document.querySelector('#notification-btn .bg-red-500');
            if (notificationDot) {
                notificationDot.style.display = 'none';
            }

            markAllReadBtn.textContent = '已全部标记';
            markAllReadBtn.disabled = true;
            markAllReadBtn.classList.add('opacity-50', 'cursor-not-allowed');
        });

        // 分页事件
        document.getElementById('prev-page').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                renderTable();
            }
        });

        document.getElementById('next-page').addEventListener('click', () => {
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            if (currentPage < totalPages) {
                currentPage++;
                renderTable();
            }
        });

        // 每页显示数量选择事件
        document.getElementById('page-size-select').addEventListener('change', (e) => {
            itemsPerPage = parseInt(e.target.value);
            currentPage = 1; // 重置到第一页
            renderTable();
        });

        // 导出功能：把当前筛选后的数据导出为 CSV
        document.getElementById('export-btn').addEventListener('click', () => {
            try {
                // 复用 renderTable 的筛选逻辑
                const searchTerm = searchInput.value.trim().toLowerCase();
                const statusTerm = statusFilter.value;
                const etaTerm = etaFilter.value;

                const rows = tableData.filter(row => {
                    const matchSearch = !searchTerm || row.trackingNumber.toLowerCase().includes(searchTerm);
                    const matchStatus = !statusTerm || statusTerm === 'all' || row.status === statusTerm;
                    const matchEta = !etaTerm || row.eta === etaTerm;
                    return matchSearch && matchStatus && matchEta;
                });

                const headers = ['序号','提单号/箱号','船公司','状态','预计到港时间','物流节点','凭证','创建时间','更新时间'];
                const csvLines = [headers.join(',')];
                rows.forEach((r, i) => {
                    const line = [
                        i + 1,
                        r.trackingNumber,
                        r.company,
                        r.status,
                        r.eta,
                        r.nodes,
                        r.voucher ? '有' : '无',
                        r.createdTime,
                        r.updatedTime
                    ].map(v => typeof v === 'string' && v.includes(',') ? `"${v.replace(/"/g,'""')}"` : v).join(',');
                    csvLines.push(line);
                });

                const blob = new Blob(["\uFEFF" + csvLines.join('\n')], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                const now = new Date();
                a.download = `任务列表_${now.getFullYear()}${String(now.getMonth()+1).padStart(2,'0')}${String(now.getDate()).padStart(2,'0')}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } catch (e) {
                console.error('导出失败:', e);
                alert('导出失败，请稍后再试');
            }
        });

        // 密度按钮事件
        document.getElementById('density-btn').addEventListener('click', () => {
            const densityOptions = ['compact', 'normal', 'comfortable'];
            const currentIndex = densityOptions.indexOf(tableDensity);
            tableDensity = densityOptions[(currentIndex + 1) % densityOptions.length];
            renderTable();
        });

        // 刷新按钮事件
        document.getElementById('refresh-btn').addEventListener('click', async () => {
            showTableSkeleton();
            try {
                await fetchTasksFromAPI();
            } catch (e) {
                console.error('刷新数据失败:', e);
            } finally {
                renderTable();
            }
        });

        // --- 新建查询弹窗逻辑 ---
        const CARRIER_RULES = [
            { code: 'MSC', name: 'MSC (地中海航运)', fullName: 'MSC (Mediterranean Shipping Company / 地中海航运)', detect: v => /^MEDU|^MSCU|^MEDU|^MSMU|^MSC/.test(v), url: 'https://www.msc.com/en/track-a-shipment' },
            { code: 'COSCO', name: 'COSCO (中远海运)', fullName: 'COSCO (中远海运)', detect: v => /^CSNU|^COSU|^OOLU|^GLDU|^SEGU|^CNS/.test(v), url: 'https://elines.coscoshipping.com/ebusiness/cargoTracking' },
            { code: 'MAERSK', name: 'Maersk (马士基)', fullName: 'Maersk (马士基)', detect: v => /^MSKU|^MAEU|^APLU|^OOLU/.test(v), url: 'https://www.maersk.com/tracking/' },
            { code: 'CMA', name: 'CMA CGM (达飞海运)', fullName: 'CMA CGM (达飞海运)', detect: v => /^CMDU|^CMAU/.test(v), url: 'https://www.cma-cgm.com/ebusiness/tracking' },
            { code: 'EVERGREEN', name: 'Evergreen (长荣海运)', fullName: 'Evergreen (长荣海运)', detect: v => /^EGLV|^EMCU|^EVER/.test(v), url: 'https://www.shipmentlink.com/servlet/TDB1_CargoTracking.do' },
            { code: 'HAPAG', name: 'Hapag-Lloyd (赫伯罗特)', fullName: 'Hapag-Lloyd (赫伯罗特)', detect: v => /^HLCU|^HAPU/.test(v), url: 'https://www.hapag-lloyd.com/en/online-business/tracing/tracing-by-booking.html' },
        ];

        function getCarrierByTracking(value) {
            const v = (value || '').toUpperCase().replace(/\s+/g, '');
            if (!/^([A-Z]{4}\d{7}|[A-Z]{3,5}[A-Z0-9]{5,})$/.test(v)) {
                return null;
            }
            for (const rule of CARRIER_RULES) {
                if (rule.detect(v)) return { code: rule.code, name: rule.fullName, url: rule.url }; // 使用fullName保持一致
            }
            return { code: 'UNKNOWN', name: '未知承运人', url: '' };
        }

        function updateCarrierUI(info, tracking) {
            const logo = document.getElementById('carrier-logo');
            const name = document.getElementById('carrier-name');
            const code = document.getElementById('carrier-code');
            const website = document.getElementById('carrier-website');
            const badge = document.getElementById('detection-badge');
            const validation = document.getElementById('tracking-validation');
            const inputStatus = document.getElementById('input-status');

            if (!info) {
                carrierInfo.classList.add('hidden');
                errorMessage.classList.remove('hidden');
                confirmQueryBtn.disabled = true;
                inputStatus.innerHTML = '<i data-lucide="x-circle" class="h-5 w-5 text-red-500"></i>';
                inputStatus.classList.remove('hidden');
                lucide.createIcons();
                return;
            }

            errorMessage.classList.add('hidden');
            carrierInfo.classList.remove('hidden');
            confirmQueryBtn.disabled = false;

            logo.textContent = info.code.substring(0, 1);
            name.textContent = info.name;
            code.textContent = `${info.code} · ${tracking.toUpperCase()}`;
            website.textContent = info.url ? '查询网站' : '—';
            website.onclick = () => { if (info.url) window.open(info.url, '_blank'); };
            badge.textContent = info.code === 'UNKNOWN' ? '未识别' : '自动识别';
            validation.innerHTML = '<div class="flex items-center text-emerald-600 dark:text-emerald-400"><i data-lucide="check-circle" class="h-4 w-4 mr-2"></i><span>格式校验通过</span></div>';
            inputStatus.innerHTML = '<i data-lucide="check-circle" class="h-5 w-5 text-emerald-500"></i>';
            inputStatus.classList.remove('hidden');
            lucide.createIcons();
        }

        function openNewQueryModal() {
            newQueryModal.classList.remove('hidden');
            setTimeout(() => {
                newQueryModalContent.classList.remove('scale-95', 'opacity-0');
                newQueryModalContent.classList.add('scale-100', 'opacity-100');
            }, 50);
            // 重置状态
            setMode('single');
            trackingInput.value = '';
            batchInput.value = '';
            batchPreview.innerHTML = '';
            batchStats.textContent = '0 条有效 · 0 条重复/无效';
            carrierInfo.classList.add('hidden');
            errorMessage.classList.add('hidden');
            confirmQueryBtn.disabled = true;
            updateConfirmText();
        }

        function closeNewQuery() {
            newQueryModalContent.classList.remove('scale-100', 'opacity-100');
            newQueryModalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => newQueryModal.classList.add('hidden'), 300);
        }

        function setMode(mode) {
            const isSingle = mode === 'single';
            singleMode.classList.toggle('hidden', !isSingle);
            batchMode.classList.toggle('hidden', isSingle);
            // 切换Tab样式
            if (isSingle) {
                singleModeTab.classList.add('bg-white','dark:bg-slate-600','text-blue-600','dark:text-blue-400','shadow-sm');
                batchModeTab.classList.remove('bg-white','dark:bg-slate-600','text-blue-600','dark:text-blue-400','shadow-sm');
                batchModeTab.classList.add('text-slate-600','dark:text-slate-400');
            } else {
                batchModeTab.classList.add('bg-white','dark:bg-slate-600','text-blue-600','dark:text-blue-400','shadow-sm');
                singleModeTab.classList.remove('bg-white','dark:bg-slate-600','text-blue-600','dark:text-blue-400','shadow-sm');
                singleModeTab.classList.add('text-slate-600','dark:text-slate-400');
            }
            updateConfirmText();
        }

        function updateConfirmText(validCount = 0) {
            const span = confirmQueryBtn.querySelector('span');
            if (!batchMode.classList.contains('hidden')) {
                span.textContent = validCount > 0 ? `批量添加（${validCount}）` : '批量添加';
                confirmQueryBtn.disabled = validCount === 0;
            } else {
                span.textContent = '开始查询';
            }
        }

        newQueryBtn.addEventListener('click', openNewQueryModal);
        closeNewQueryModal.addEventListener('click', closeNewQuery);
        cancelQueryBtn.addEventListener('click', closeNewQuery);
        newQueryModal.addEventListener('click', (e) => { if (e.target === newQueryModal) closeNewQuery(); });

        // Tab 切换
        singleModeTab.addEventListener('click', () => setMode('single'));
        batchModeTab.addEventListener('click', () => setMode('batch'));

        // 单个输入
        trackingInput.addEventListener('input', (e) => {
            const val = e.target.value.trim();
            if (!val) {
                carrierInfo.classList.add('hidden');
                errorMessage.classList.add('hidden');
                confirmQueryBtn.disabled = true;
                return;
            }
            const info = getCarrierByTracking(val);
            updateCarrierUI(info, val);
        });

        // 批量输入解析
        function parseBatchInput(text) {
            const lines = text.split(/\r?\n|\t|,|;|\s+/).map(s => s.trim()).filter(Boolean);
            const unique = Array.from(new Set(lines.map(s => s.toUpperCase()))).slice(0, 200);
            const valid = [];
            const invalid = [];
            unique.forEach(item => {
                const info = getCarrierByTracking(item);
                if (info) valid.push({ tracking: item, info }); else invalid.push(item);
            });
            return { valid, invalid };
        }

        function renderBatchPreview(result) {
            batchPreview.innerHTML = '';
            result.valid.forEach(({tracking, info}) => {
                const row = document.createElement('div');
                row.className = 'flex items-center justify-between p-2 bg-white dark:bg-slate-800 rounded-md border border-slate-200 dark:border-slate-600';
                row.innerHTML = `<div class="flex items-center space-x-3"><div class="w-7 h-7 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center text-xs font-bold">${info.code.slice(0,1)}</div><div><p class="text-sm font-medium">${tracking}</p><p class="text-xs text-slate-500">${info.name}</p></div></div>`;
                batchPreview.appendChild(row);
            });
            const invalidCount = result.invalid.length;
            batchStats.textContent = `${result.valid.length} 条有效 · ${invalidCount} 条重复/无效`;
            updateConfirmText(result.valid.length);
        }

        batchInput && batchInput.addEventListener('input', (e) => {
            const text = e.target.value;
            const result = parseBatchInput(text);
            renderBatchPreview(result);
        });

        // API调用辅助函数
        async function apiCall(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken ? { 'Authorization': `Bearer ${authToken}` } : {})
                }
            };

            const response = await fetch(`${API_BASE_URL}${url}`, {
                ...defaultOptions,
                ...options,
                headers: { ...defaultOptions.headers, ...options.headers }
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `请求失败: ${response.status}`);
            }

            return response.json();
        }

        confirmQueryBtn.addEventListener('click', async () => {
            // 检查认证状态
            if (!authToken || !currentUser) {
                showNotification('创建任务失败', '请先登录', 'error');
                redirectToLogin();
                return;
            }

            // 显示loading状态
            const originalBtnText = confirmQueryBtn.innerHTML;
            confirmQueryBtn.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>处理中...';
            confirmQueryBtn.disabled = true;

            try {
                if (!batchMode.classList.contains('hidden')) {
                    // 批量添加 - 调用API
                    const text = batchInput.value || '';
                    if (!text.trim()) {
                        throw new Error('请输入要查询的提单号');
                    }

                    // 调用批量创建任务API
                    const result = await apiCall('/tasks/create-from-text', {
                        method: 'POST',
                        body: JSON.stringify({
                            text_input: text,
                            priority: 'normal',
                            deduplicate: true
                        })
                    });

                    if (result.success) {
                        console.log('批量任务创建成功:', result);
                        // 刷新任务列表
                        await fetchTasksFromAPI();
                        renderTable();
                        closeNewQuery();

                        // 显示成功消息
                        showNotification(
                            '批量任务创建成功',
                            `成功创建 ${result.summary.success} 个任务${result.summary.failed > 0 ? `，${result.summary.failed} 个失败` : ''}`,
                            'success'
                        );
                        
                        // 批量任务创建成功后，重新启动自动刷新以调整刷新间隔
                        startAutoRefresh();
                        
                        // 重新启动处理器状态轮询以更快更新处理器状态
                        startProcessorStatusPolling();
                    } else {
                        throw new Error(result.message || '批量任务创建失败');
                    }
                } else {
                    // 单个添加 - 实现乐观更新
                    const tracking = trackingInput.value.trim();
                    if (!tracking) {
                        throw new Error('请输入提单号');
                    }

                    const info = getCarrierByTracking(tracking);
                    if (!info) {
                        throw new Error('无效的提单号格式');
                    }

                    // 1. 立即显示任务（乐观更新）
                    const tempTaskId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    const optimisticTask = {
                        taskId: tempTaskId,
                        trackingNumber: tracking,
                        company: info.name, // 修复: 使用name而不是fullName
                        status: '创建中',
                        eta: '-',
                        nodes: '-',
                        voucher: false,
                        createdTime: new Date().toISOString().split('T')[0],
                        updatedTime: new Date().toISOString().split('T')[0],
                        isOptimistic: true
                    };

                    // 添加到任务列表并立即显示
                    tableData.unshift(optimisticTask);
                    renderTable();
                    closeNewQuery();

                    // 显示创建中消息
                    showNotification('任务创建中', `正在为提单号 ${tracking} 创建查询任务...`, 'info');

                    // 2. 后台调用API
                    try {
                        const result = await apiCall('/tasks/quick-create', {
                            method: 'POST',
                            body: JSON.stringify({
                                container_number: tracking
                            })
                        });

                        console.log('任务创建成功:', result);

                        // 3. 重新从API获取最新的任务列表，确保数据同步
                        await fetchTasksFromAPI();
                        renderTable();
                        
                        showNotification('任务创建成功', `提单号 ${tracking} 的查询任务已创建`, 'success');
                        
                        // 任务创建成功后，重新启动自动刷新以调整刷新间隔（因为现在有查询中的任务）
                        startAutoRefresh();
                        
                        // 重新启动处理器状态轮询以更快更新处理器状态
                        startProcessorStatusPolling();

                    } catch (apiError) {
                        console.error('API调用失败:', apiError);

                        // 4. API失败时，从任务列表中移除临时任务，重新获取真实数据
                        await fetchTasksFromAPI();
                        renderTable();

                        // 检查是否是认证或权限错误
                        if (apiError.message.includes('401')) {
                            showNotification('认证失败', '登录已过期，请重新登录', 'error');
                            localStorage.removeItem('auth_data');
                            authToken = null;
                            currentUser = null;
                            showAuthOverlay();
                        } else if (apiError.message.includes('403')) {
                            showNotification('权限不足', '您没有批量创建任务的权限', 'error');
                        } else {
                            showNotification('任务创建失败', `${apiError.message}`, 'error');
                        }
                    }
                }

            } catch (error) {
                console.error('创建任务失败:', error);

                // 检查是否是认证/权限错误
                if (error.message.includes('401')) {
                    showNotification('认证失败', '请重新登录', 'error');
                    // 清除认证信息，触发重新登录
                    localStorage.removeItem('auth_data');
                    authToken = null;
                    currentUser = null;
                    showAuthOverlay();
                } else if (error.message.includes('403')) {
                    // 保持登录状态，仅提示权限不足
                    showNotification('权限不足', '您没有批量创建任务的权限', 'error');
                } else {
                    showNotification('创建任务失败', error.message, 'error');
                }
            } finally {
                // 恢复按钮状态
                confirmQueryBtn.innerHTML = originalBtnText;
                confirmQueryBtn.disabled = false;
            }
        });

        // 处理器状态指示器 & 轮询
        const processorIndicator = document.getElementById('processor-indicator');
        let processorStatusInterval;
        
        async function refreshProcessorStatus() {
            try {
                const resp = await fetch(`${API_BASE_URL}/system/processor-status`);
                const data = await resp.json();
                const online = data && data.status === 'online' && data.running;
                processorIndicator.classList.remove('hidden');
                processorIndicator.textContent = online
                    ? `处理器: 在线 · 抓取 ${data.active?.scraping_tasks ?? 0} · AI ${data.active?.ai_tasks ?? 0}`
                    : '处理器: 离线';
                processorIndicator.className = `items-center text-xs font-medium px-2 py-1 rounded-full ${online ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/40 dark:text-emerald-300' : 'bg-rose-100 text-rose-700 dark:bg-rose-900/40 dark:text-rose-300'}`;
            } catch (e) {
                processorIndicator.classList.remove('hidden');
                processorIndicator.textContent = '处理器: 未知';
                processorIndicator.className = 'items-center text-xs font-medium px-2 py-1 rounded-full bg-slate-200 text-slate-700 dark:bg-slate-700 dark:text-slate-200';
            }
        }
        
        function startProcessorStatusPolling() {
            // 清除现有定时器
            if (processorStatusInterval) {
                clearInterval(processorStatusInterval);
            }
            
            // 立即刷新一次
            refreshProcessorStatus();
            
            // 动态刷新间隔：如果有查询中的任务，5秒刷新一次处理器状态；否则15秒
            function scheduleNextRefresh() {
                const hasPendingTasks = tableData.some(task => task.status === '查询中');
                const refreshInterval = hasPendingTasks ? 5000 : 15000; // 5秒或15秒
                
                processorStatusInterval = setTimeout(async () => {
                    await refreshProcessorStatus();
                    scheduleNextRefresh();
                }, refreshInterval);
            }
            
            scheduleNextRefresh();
        }
        
        // 启动处理器状态轮询
        startProcessorStatusPolling();

        // 定期自动刷新任务数据以同步后端状态更新
        let autoRefreshInterval;
        function startAutoRefresh() {
            // 如果已经有定时器，先清除
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            // 动态刷新间隔：如果有查询中的任务，15秒刷新一次；否则60秒
            function refreshWithDynamicInterval() {
                const hasPendingTasks = tableData.some(task => task.status === '查询中');
                const refreshInterval = hasPendingTasks ? 15000 : 60000; // 15秒或60秒
                
                autoRefreshInterval = setTimeout(async () => {
                    try {
                        // 只有在登录状态下才刷新
                        if (authToken && currentUser) {
                            console.log(`自动刷新任务数据... (间隔: ${refreshInterval/1000}秒, 有查询中任务: ${hasPendingTasks})`);
                            await fetchTasksFromAPI();
                            renderTable();
                        }
                    } catch (error) {
                        console.error('自动刷新失败:', error);
                    }
                    
                    // 递归调用以实现动态间隔
                    refreshWithDynamicInterval();
                }, refreshInterval);
            }
            
            // 开始第一次刷新循环
            refreshWithDynamicInterval();
        }

        // 启动自动刷新
        startAutoRefresh();

        // 状态筛选变化时，自动刷新任务列表
        const statusSelectEl = document.getElementById('status-filter');
        if (statusSelectEl) {
            statusSelectEl.addEventListener('change', () => {
                fetchTasksFromAPI().catch(console.error);
            });
        }

        // 通知显示函数
        function showNotification(title, message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                'bg-blue-500 text-white'
            }`;

            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-1">
                        <div class="font-semibold">${title}</div>
                        <div class="text-sm opacity-90">${message}</div>
                    </div>
                    <button class="ml-2 text-white/80 hover:text-white" onclick="this.parentElement.parentElement.remove()">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动消失
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }
// --- Initial Load ---
        const initialThemeIsDark = localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);
        setTheme(initialThemeIsDark);
        // Load data then render
        (async () => {
            await fetchTasksFromAPI();
            renderTable();
        })();

        // --- Particle Animation ---
        const canvas = document.getElementById('particle-canvas');
        const ctx = canvas.getContext('2d');
        let particles = [];
        const particleOptions = {
            light: {
                particleColor: "rgba(100, 116, 139, 0.5)",
                lineColor: "rgba(100, 116, 139, 0.2)",
            },
            dark: {
                particleColor: "rgba(148, 163, 184, 0.5)",
                lineColor: "rgba(148, 163, 184, 0.1)",
            },
            particleAmount: 80,
            defaultRadius: 2.5,
            variantRadius: 1.5,
            defaultSpeed: 0.3,
            linkRadius: 220,
        };

        function getCurrentThemeColors() {
            return document.documentElement.classList.contains('dark') ? particleOptions.dark : particleOptions.light;
        }

        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        class Particle {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.radius = particleOptions.defaultRadius + Math.random() * particleOptions.variantRadius;
                this.speedX = (Math.random() - 0.5) * particleOptions.defaultSpeed;
                this.speedY = (Math.random() - 0.5) * particleOptions.defaultSpeed;
            }

            draw() {
                const colors = getCurrentThemeColors();
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = colors.particleColor;
                ctx.fill();
                ctx.closePath();
            }

            update() {
                this.x += this.speedX;
                this.y += this.speedY;

                if (this.x < 0 || this.x > canvas.width) this.speedX *= -1;
                if (this.y < 0 || this.y > canvas.height) this.speedY *= -1;
            }
        }

        function createParticles() {
            particles = [];
            for (let i = 0; i < particleOptions.particleAmount; i++) {
                particles.push(new Particle());
            }
        }

        function linkParticles() {
            const colors = getCurrentThemeColors();
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    const dx = particles[i].x - particles[j].x;
                    const dy = particles[i].y - particles[j].y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance < particleOptions.linkRadius) {
                        const opacity = 1 - (distance / particleOptions.linkRadius);
                        ctx.strokeStyle = colors.lineColor.replace(/,\s*\d*\.?\d*\)/, `, ${opacity * 0.5})`);
                        ctx.lineWidth = 1;
                        ctx.beginPath();
                        ctx.moveTo(particles[i].x, particles[i].y);
                        ctx.lineTo(particles[j].x, particles[j].y);
                        ctx.stroke();
                        ctx.closePath();
                    }
                }
            }
        }

        function animateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            particles.forEach(particle => {
                particle.update();
                particle.draw();
            });
            linkParticles();
            requestAnimationFrame(animateParticles);
        }

        createParticles();
        animateParticles();

        // === 任务详情和截图查看功能 ===
        async function showTaskDetail(taskId, trackingNumber) {
            if (!taskId) return;

            const title = `物流节点 - ${trackingNumber || taskId}`;

            try {
                // 显示加载状态
                openModal(title, '<div class="p-4 text-center text-gray-500 dark:text-gray-400">正在加载物流节点...</div>');

                // 从API获取物流节点数据
                const response = await fetch(`${API_BASE_URL}/task/${taskId}/shipment-details`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('物流节点API返回数据:', data); // 调试日志
                const points = data.tracking_points || [];

                // 解析起运港和目的港
                const routeInfo = extractRouteInfo(points);

                const content = points.length ? `
                    <div class="logistics-timeline h-full flex flex-col">
                        <!-- 路线概览（固定头部） -->
                        <div class="route-overview flex-shrink-0 p-4 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl">
                            <div class="flex items-center justify-between">
                                <!-- 起运港 -->
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white text-lg font-bold mb-2">🏭</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">起运港</div>
                                    <div class="text-sm font-semibold text-gray-800 dark:text-gray-100">${routeInfo.origin}</div>
                                </div>

                                <!-- 中间箭头和耗时 -->
                                <div class="flex-1 flex flex-col items-center mx-6">
                                    <!-- 箭头 -->
                                    <div class="flex items-center w-full mb-2">
                                        <div class="flex-1 h-0.5 bg-gradient-to-r from-emerald-500 to-rose-500"></div>
                                        <div class="mx-2 text-rose-500 text-lg">→</div>
                                        <div class="flex-1 h-0.5 bg-gradient-to-r from-emerald-500 to-rose-500"></div>
                                    </div>
                                    <!-- 耗时信息 -->
                                    <div class="px-3 py-1 rounded-full bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 text-xs">
                                        <span class="font-medium">预计耗时：${calculateTransitTime(points)}</span>
                                    </div>
                                </div>

                                <!-- 目的港 -->
                                <div class="flex flex-col items-center text-center">
                                    <div class="w-12 h-12 bg-rose-500 rounded-full flex items-center justify-center text-white text-lg font-bold mb-2">🏁</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">目的港</div>
                                    <div class="text-sm font-semibold text-gray-800 dark:text-gray-100">${routeInfo.destination}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 时间信息（固定头部） -->
                        <div class="flex-shrink-0 text-center text-xs text-slate-500 dark:text-slate-400 mt-2 pb-2 border-b border-slate-200 dark:border-slate-700">
                            <span>起运时间：${formatLogisticsDate(getEarliestDate(points))}</span>
                            <span class="mx-2">|</span>
                            <span>预计到达：${formatLogisticsDate(getLatestDate(points))}</span>
                        </div>

                        <!-- 时间轴主体（可滚动） -->
                        <div class="timeline-body flex-1 overflow-y-auto p-6">
                            <div class="space-y-4">
                                ${points
                                      .slice()
                                      .sort((a,b)=> new Date(b.date||b.time||b.timestamp) - new Date(a.date||a.time||a.timestamp))
                                      .map((p, idx, arr) => generateSimpleTimelineNode(p, idx, arr.length))
                                      .join('')}
                            </div>
                        </div>
                    </div>
                ` : '<div class="p-8 text-center text-gray-500 dark:text-gray-400"><div class="text-4xl mb-2">🚢</div><div>暂无物流节点</div><div class="text-sm mt-1">货物信息正在更新中...</div></div>';

                // 更新模态框内容
                openModal(title, content);

            } catch (error) {
                console.error('获取物流节点失败:', error);
                openModal(title, '<div class="p-4 text-red-500 dark:text-red-400">获取物流节点失败，请稍后重试</div>');
            }
        }

        function getScreenshotUrl(taskId) {
            if (!taskId) return '';
            const t = window.__latestTasks?.find(x => x.task_id === taskId);
            const path = t?.result_files?.screenshot || t?.result?.evidence_screenshot || '';
            if (!path) return '';
            if (/^https?:\/\//i.test(path)) return path;
            // Use the API endpoint to serve files with the actual file path
            return `${API_BASE_URL}/files?path=${encodeURIComponent(path)}`;
        }

        // 其他相关函数...
        window.showTaskDetail = showTaskDetail;
        window.showImagePreview = showImagePreview;
    </script>

    <!-- PWA Service Worker 和剩余功能 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // 提取路线信息
        function extractRouteInfo(points) {
            if (!points || points.length === 0) {
                return { origin: '未知', destination: '未知' };
            }

            // 查找起运港（第一个有地点的节点）
            let origin = '未知';
            for (let point of points) {
                if (point.location && point.location.trim()) {
                    origin = point.location.split(',')[0].trim(); // 取第一部分作为港口名
                    break;
                }
            }

            // 查找目的港（最后一个有地点的节点）
            let destination = '未知';
            for (let i = points.length - 1; i >= 0; i--) {
                if (points[i].location && points[i].location.trim()) {
                    destination = points[i].location.split(',')[0].trim();
                    break;
                }
            }

            return { origin, destination };
        }

        // 生成简洁的时间轴节点
        function generateSimpleTimelineNode(point, index, total) {
            const isFirst = index === 0;
            const isLast = index === total - 1;

            // 解析状态和位置信息 - 保留原始数据，不添加中文翻译
            const status = point.status || point.event || point.event_type || 'Event';
            const location = point.location || '';
            const date = formatLogisticsDate(point.date || point.time || point.timestamp);
            const description = point.description || point.detail || point.vessel_info || '';

            // 确定节点类型和样式
            const nodeStyle = getSimpleNodeStyle(status, isFirst, isLast);

            return `
                <div class="timeline-item flex items-start gap-3 p-3 bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 ${date.includes('还有') ? 'ring-2 ring-rose-300' : ''}">
                    <!-- 左列时间轴列 -->
                    <div class="tl-col flex flex-col items-center pt-1">
                        <div class="tl-dot ${nodeStyle.dotClass}" aria-hidden="true"></div>
                        ${index < total - 1 ? '<div class="tl-line"></div>' : ''}
                    </div>

                    <!-- 右列内容 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <span class="text-sm font-semibold text-slate-900 dark:text-slate-100">${status}</span>
                            </div>
                            <span class="text-xs time-chip ${date.includes('还有') ? 'ring-2 ring-rose-400 text-rose-600' : ''}">${date}</span>
                        </div>

                        ${location ? `
                            <div class="mt-1 text-sm ${date.includes('还有') ? 'text-rose-600' : 'text-gray-600 dark:text-gray-400'} truncate">
                                <span class="mr-1">📍</span>${location}
                            </div>
                        ` : ''}

                        ${description ? `
                            <div class="mt-1 text-xs ${date.includes('还有') ? 'text-rose-500' : 'text-gray-500 dark:text-gray-400'}">${description}</div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function getSimpleNodeStyle(status, isFirst, isLast) {
            const s = (status || '').toLowerCase();

            // 起始或空箱
            if (isFirst || s.includes('empty')) {
                return { icon: '🏭', bgClass: 'bg-green-500', badgeClass: 'status-green', dotClass: '' };
            }
            // 终点或卸货
            if (isLast || s.includes('discharged') || s.includes('arrived')) {
                return { icon: '🏁', bgClass: 'bg-red-500', badgeClass: 'status-red', dotClass: 'tl-dot-blue' };
            }
            // 已装船
            if (s.includes('loaded') || s.includes('on board') || s.includes('onboard')) {
                return { icon: '📦', bgClass: 'bg-blue-500', badgeClass: 'status-blue', dotClass: '' };
            }
            // 在途/转运
            if (s.includes('transit') || s.includes('departed')) {
                return { icon: '🚢', bgClass: 'bg-yellow-500', badgeClass: 'status-yellow', dotClass: '' };
            }
            // 默认
            return { icon: '📍', bgClass: 'bg-gray-500', badgeClass: 'status-gray', dotClass: 'tl-dot-gray' };
        }

        // 物流节点时间轴辅助函数（保留旧版本以防需要）
        function generateTimelineNode(point, index, total) {
            const isFirst = index === 0;
            const isLast = index === total - 1;
            const isCurrent = point.status && (point.status.includes('运输中') || point.status.includes('在途'));

            // 解析状态和位置信息
            const status = point.status || point.event || point.event_type || '节点';
            const location = point.location || '';
            const date = formatLogisticsDate(point.date || point.time || point.timestamp);
            const description = point.description || point.detail || point.vessel_info || '';

            // 确定节点类型和图标
            const nodeInfo = getNodeTypeInfo(status, location, isFirst, isLast);

            return `
                <div class="timeline-node relative flex items-start mb-6 ${isCurrent ? 'current-node' : ''}">
                    <!-- 节点图标 -->
                    <div class="timeline-icon relative z-10 flex items-center justify-center w-16 h-16 rounded-full ${nodeInfo.bgClass} ${nodeInfo.borderClass} shadow-lg">
                        <div class="text-2xl">${nodeInfo.icon}</div>
                        ${isCurrent ? '<div class="absolute -inset-1 rounded-full bg-blue-400 animate-ping opacity-75"></div>' : ''}
                    </div>

                    <!-- 节点内容 -->
                    <div class="timeline-content ml-6 flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 ${isCurrent ? 'ring-2 ring-blue-400 ring-opacity-50' : ''}">
                        <!-- 状态和时间 -->
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-semibold text-gray-900 dark:text-gray-100 ${nodeInfo.statusClass}">${status}</h4>
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">${date}</span>
                        </div>

                        <!-- 地点信息 -->
                        ${location ? `
                            <div class="flex items-center mb-2">
                                <span class="text-sm text-gray-600 dark:text-gray-400">📍</span>
                                <span class="ml-1 text-sm font-medium text-gray-700 dark:text-gray-300">${location}</span>
                            </div>
                        ` : ''}

                        <!-- 详细描述 -->
                        ${description ? `
                            <div class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded">
                                ${description}
                            </div>
                        ` : ''}

                        <!-- 节点序号 -->
                        <div class="absolute -left-2 top-2 w-6 h-6 bg-blue-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                            ${index + 1}
                        </div>
                    </div>
                </div>
            `;
        }

        function getNodeTypeInfo(status, location, isFirst, isLast) {
            const statusLower = status.toLowerCase();

            if (isFirst || statusLower.includes('empty') || statusLower.includes('空箱')) {
                return {
                    icon: '🏭',
                    bgClass: 'bg-green-100 dark:bg-green-900',
                    borderClass: 'border-2 border-green-400',
                    statusClass: 'text-green-700 dark:text-green-300'
                };
            }

            if (isLast || statusLower.includes('discharged') || statusLower.includes('卸货')) {
                return {
                    icon: '🏁',
                    bgClass: 'bg-red-100 dark:bg-red-900',
                    borderClass: 'border-2 border-red-400',
                    statusClass: 'text-red-700 dark:text-red-300'
                };
            }

            if (statusLower.includes('loaded') || statusLower.includes('装货')) {
                return {
                    icon: '📦',
                    bgClass: 'bg-blue-100 dark:bg-blue-900',
                    borderClass: 'border-2 border-blue-400',
                    statusClass: 'text-blue-700 dark:text-blue-300'
                };
            }

            if (statusLower.includes('transit') || statusLower.includes('运输') || statusLower.includes('在途')) {
                return {
                    icon: '🚢',
                    bgClass: 'bg-yellow-100 dark:bg-yellow-900',
                    borderClass: 'border-2 border-yellow-400',
                    statusClass: 'text-yellow-700 dark:text-yellow-300'
                };
            }

            return {
                icon: '📍',
                bgClass: 'bg-gray-100 dark:bg-gray-700',
                borderClass: 'border-2 border-gray-400',
                statusClass: 'text-gray-700 dark:text-gray-300'
            };
        }

        function formatLogisticsDate(dateStr) {
            if (!dateStr) return '时间待定';
            try {
                const date = new Date(dateStr);
                if (isNaN(date.getTime())) return dateStr;

                // 标准格式：YYYY/MM/DD HH:mm（星期X），未来时间标注“还剩N天”
                const pad = (n) => String(n).padStart(2, '0');
                const yyyy = date.getFullYear();
                const mm = pad(date.getMonth() + 1);
                const dd = pad(date.getDate());
                const hh = pad(date.getHours());
                const mi = pad(date.getMinutes());

                const weekdays = ['星期日','星期一','星期二','星期三','星期四','星期五','星期六'];
                const week = weekdays[date.getDay()];

                const now = new Date();
                const oneDay = 24*60*60*1000;
                const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const startOfThatDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                const diffDays = Math.round((startOfThatDay - startOfToday) / oneDay);

                const base = `${yyyy}/${mm}/${dd} ${hh}:${mi} ${week}`;
                if (diffDays > 0) {
                    return `${base}（还有${diffDays}天）`;
                }
                return base;
            } catch (e) {
                return dateStr;
            }
        }

        function calculateTransitTime(points) {
            if (!points || points.length < 2) return '计算中...';

            try {
                const earliestDate = getEarliestDate(points);
                const latestDate = getLatestDate(points);

                if (!earliestDate || !latestDate) return '时间待定';

                const diffDays = Math.ceil((new Date(latestDate) - new Date(earliestDate)) / (1000 * 60 * 60 * 24));
                return diffDays > 0 ? `${diffDays} 天` : '计算中...';
            } catch (e) {
                return '时间待定';
            }
        }

        // 获取最早日期（起运时间）
        function getEarliestDate(points) {
            if (!points || points.length === 0) return null;

            let earliest = null;
            for (const point of points) {
                const dateStr = point.date || point.time || point.timestamp;
                if (dateStr) {
                    const date = new Date(dateStr);
                    if (!isNaN(date.getTime()) && (!earliest || date < earliest)) {
                        earliest = date;
                    }
                }
            }
            return earliest ? earliest.toISOString() : null;
        }

        // 获取最晚日期（预计到达时间）
        function getLatestDate(points) {
            if (!points || points.length === 0) return null;

            let latest = null;
            for (const point of points) {
                const dateStr = point.date || point.time || point.timestamp;
                if (dateStr) {
                    const date = new Date(dateStr);
                    if (!isNaN(date.getTime()) && (!latest || date > latest)) {
                        latest = date;
                    }
                }
            }
            return latest ? latest.toISOString() : null;
        }

        function showImagePreview(taskId, trackingNumber) {
            const imgUrl = getScreenshotUrl(taskId);
            const title = `凭证截图 - ${trackingNumber || taskId}`;

            if (!imgUrl) {
                openModal(title, '<div class="p-4 text-gray-500">暂无截图</div>');
                return;
            }

            const content = `
                <div class="image-viewer-container h-full flex flex-col">
                    <!-- 固定工具栏 -->
                    <div class="flex-shrink-0 flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                        <div class="flex space-x-2">
                            <button onclick="zoomIn()" class="px-3 py-1.5 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">放大</button>
                            <button onclick="zoomOut()" class="px-3 py-1.5 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">缩小</button>
                            <button onclick="resetZoom()" class="px-3 py-1.5 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">重置</button>
                            <span id="zoom-info" class="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400">100%</span>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="downloadImage('${imgUrl}', '${trackingNumber || taskId}')" class="px-3 py-1.5 bg-green-600 text-white rounded text-sm hover:bg-green-700">保存</button>
                            <button onclick="toggleFullscreen()" class="px-3 py-1.5 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">全屏</button>
                        </div>
                    </div>

                    <!-- 图片容器 - 占满剩余空间 -->
                    <div id="image-container" class="flex-1 overflow-hidden bg-gray-50 dark:bg-gray-800 flex items-center justify-center" style="cursor: grab;">
                        <img id="preview-image" src="${imgUrl}" alt="截图" class="transition-transform duration-200"
                             style="transform: scale(1);" onload="initImageViewer()" draggable="false" />
                    </div>
                </div>
            `;

            openModal(title, content);
        }

        function getScreenshotUrl(taskId) {
            if (!taskId) return '';
            const t = window.__latestTasks?.find(x => x.task_id === taskId);
            const path = t?.result_files?.screenshot || t?.result?.evidence_screenshot || '';
            if (!path) return '';
            if (/^https?:\/\//i.test(path)) return path;
            // Use the API endpoint to serve files with the actual file path
            return `${API_BASE_URL}/files?path=${encodeURIComponent(path)}`;
        }

        // 图片查看器功能
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY;
        let translateX = 0, translateY = 0;
        let imageNaturalWidth = 0, imageNaturalHeight = 0;
        let containerWidth = 0, containerHeight = 0;

        function initImageViewer() {
            const image = document.getElementById('preview-image');
            const container = document.getElementById('image-container');

            if (!image || !container) return;

            // 等待图片加载完成
            if (image.complete) {
                setupImageViewer();
            } else {
                image.onload = setupImageViewer;
            }
        }

        function setupImageViewer() {
            const image = document.getElementById('preview-image');
            const container = document.getElementById('image-container');

            if (!image || !container) return;

            // 获取图片尺寸
            imageNaturalWidth = image.naturalWidth;
            imageNaturalHeight = image.naturalHeight;
            containerWidth = container.clientWidth;
            containerHeight = container.clientHeight;

            // 默认显示原始大小，像微信一样
            currentZoom = 1;

            // 重置位置到居中（CSS flex已经处理居中，这里只是重置拖拽偏移）
            translateX = 0;
            translateY = 0;

            // 应用初始变换
            updateImageTransform();
            updateZoomInfo();

            // 添加事件监听
            container.addEventListener('wheel', handleWheel, { passive: false });
            container.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // 防止图片被选中和拖拽
            image.addEventListener('selectstart', e => e.preventDefault());
            image.addEventListener('dragstart', e => e.preventDefault());
        }

        function startDrag(e) {
            if (e.target.closest('#image-container')) {
                isDragging = true;
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
                document.getElementById('image-container').style.cursor = 'grabbing';
                e.preventDefault();
            }
        }

        function drag(e) {
            if (!isDragging) return;
            e.preventDefault();

            translateX = e.clientX - startX;
            translateY = e.clientY - startY;

            updateImageTransform();
        }

        function endDrag() {
            if (isDragging) {
                isDragging = false;
                const container = document.getElementById('image-container');
                if (container) {
                    container.style.cursor = 'grab';
                }
            }
        }

        function handleWheel(e) {
            e.preventDefault();
            const factor = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = Math.max(0.25, Math.min(3, currentZoom * factor));
            setZoom(newZoom);
        }

        function updateImageTransform() {
            const image = document.getElementById('preview-image');
            if (image) {
                // 设置图片原始尺寸
                image.style.width = `${imageNaturalWidth}px`;
                image.style.height = `${imageNaturalHeight}px`;

                // 使用CSS flex居中，只需要处理缩放和拖拽偏移
                image.style.transform = `scale(${currentZoom}) translate(${translateX / currentZoom}px, ${translateY / currentZoom}px)`;
                image.style.transformOrigin = 'center center';
            }
        }

        function zoomIn() {
            const newZoom = Math.min(3, currentZoom * 1.25);
            setZoom(newZoom);
        }

        function zoomOut() {
            const newZoom = Math.max(0.25, currentZoom / 1.25);
            setZoom(newZoom);
        }

        function resetZoom() {
            // 重置到原始大小，居中显示，就像微信一样
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            updateImageTransform();
            updateZoomInfo();
        }

        function setZoom(zoom) {
            currentZoom = zoom;
            updateImageTransform();
            updateZoomInfo();
        }

        function updateZoomInfo() {
            const zoomInfo = document.getElementById('zoom-info');
            if (zoomInfo) {
                zoomInfo.textContent = `缩放: ${Math.round(currentZoom * 100)}%`;
            }
        }

        async function downloadImage(url, filename) {
            try {
                // 获取图片数据
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error('下载失败');
                }

                const blob = await response.blob();

                // 创建下载链接
                const link = document.createElement('a');
                const objectUrl = URL.createObjectURL(blob);
                link.href = objectUrl;
                link.download = `${filename}_screenshot.png`;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 清理对象URL
                URL.revokeObjectURL(objectUrl);

                console.log('图片下载成功');
            } catch (error) {
                console.error('下载图片失败:', error);
                alert('下载失败，请稍后重试');
            }
        }

        function toggleFullscreen() {
            const modal = document.getElementById('modal');
            if (!document.fullscreenElement) {
                modal.requestFullscreen().catch(err => {
                    console.log(`Error attempting to enable fullscreen: ${err.message}`);
                });
            } else {
                document.exitFullscreen();
            }
        }

        function openModal(title, html) {
            let el = document.getElementById('global-modal');
            if (!el) {
                el = document.createElement('div');
                el.id = 'global-modal';
                el.innerHTML = `
                <div class="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div id="modal" class="bg-white dark:bg-slate-800 rounded-lg shadow-xl w-full h-full max-w-6xl max-h-[90vh] flex flex-col">
                        <div class="flex-shrink-0 px-4 py-3 border-b border-gray-200 dark:border-slate-700 flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-slate-100" id="modal-title"></h3>
                            <button id="modal-close" class="text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 text-xl font-bold">✕</button>
                        </div>
                        <div id="modal-content" class="flex-1 overflow-hidden"></div>
                    </div>
                </div>`;
                document.body.appendChild(el);
                el.querySelector('#modal-close').onclick = () => el.remove();
                el.onclick = (e) => { if (e.target === el.firstElementChild) el.remove(); };
            }
            el.querySelector('#modal-title').textContent = title;
            const modalContent = el.querySelector('#modal-content');
            modalContent.innerHTML = html;

            // 根据内容类型调整样式
            if (html.includes('image-viewer-container')) {
                // 图片查看器
                modalContent.style.overflow = 'hidden';
                modalContent.style.height = '100%';
            } else if (html.includes('logistics-timeline')) {
                // 物流节点弹窗 - 禁用外层滚动，内部组件自己处理滚动
                modalContent.style.overflow = 'hidden';
                modalContent.style.height = '100%';
                modalContent.style.maxHeight = '75vh';
            } else {
                // 其他弹窗
                modalContent.style.overflow = 'auto';
                modalContent.style.height = 'auto';
                modalContent.style.maxHeight = '75vh';
            }
        }

        // === 版本/缓存工具（可视化一键清缓存并强刷） ===
        (function initVersionCacheTool() {
            try {
                const toolkit = document.createElement('div');
                toolkit.id = 'version-cache-tool';
                toolkit.className = 'fixed right-4 bottom-4 z-[99999]';
                toolkit.innerHTML = `
                    <div class="bg-white/90 dark:bg-slate-800/90 border border-slate-300 dark:border-slate-700 rounded-xl shadow-xl backdrop-blur p-3 space-y-2 w-[280px]">
                        <div class="text-xs text-slate-600 dark:text-slate-300" id="vct-info">Version: 初始化中…</div>
                        <div class="flex gap-2">
                            <button id="vct-clear" class="flex-1 px-3 py-2 text-xs font-medium bg-rose-500 hover:bg-rose-600 text-white rounded">🧹 清缓存并强制刷新</button>
                            <button id="vct-reload" class="px-3 py-2 text-xs font-medium bg-blue-500 hover:bg-blue-600 text-white rounded">🔄 仅强刷</button>
                        </div>
                        <div class="flex gap-2">
                            <button id="vct-copy" class="flex-1 px-3 py-2 text-xs font-medium bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200 rounded">📄 复制调试信息</button>
                            <button id="vct-hide" class="px-3 py-2 text-xs font-medium bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200 rounded">🙈 隐藏</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(toolkit);

                // 渲染信息
                const infoEl = document.getElementById('vct-info');
                function swStatus() {
                    try {
                        if (!('serviceWorker' in navigator)) return 'unsupported';
                        const c = navigator.serviceWorker.controller;
                        return c ? 'active' : 'none';
                    } catch { return 'unknown'; }
                }
                function renderInfo() {
                    const url = new URL(location.href);
                    const v = url.searchParams.get('v') || '-';
                    const mod = document.lastModified || '-';
                    const appVer = (typeof APP_VERSION !== 'undefined' ? APP_VERSION : (window.__APP_VERSION__ || '-'));
                    const apiBase = (typeof API_BASE_URL !== 'undefined' ? API_BASE_URL : `${location.origin}/api/v1`);
                    infoEl.textContent = `版本时间: ${mod} | App:${appVer} | v=${v} | SW:${swStatus()} | API:${apiBase}`;
                }
                renderInfo();

                // 行为：仅强刷（追加 v 参数）
                document.getElementById('vct-reload').onclick = () => {
                    const url = new URL(location.href);
                    url.searchParams.set('v', Date.now().toString());
                    location.href = url.toString();
                };

                // 行为：清缓存+强刷
                document.getElementById('vct-clear').onclick = async () => {
                    try {
                        // 清本地存储
                        try { localStorage.removeItem('auth_data'); } catch {}
                        try { localStorage.removeItem('shipment_query_data'); } catch {}

                        // 删除 Cache Storage
                        if ('caches' in window) {
                            const names = await caches.keys();
                            await Promise.all(names.map(n => caches.delete(n)));
                        }
                        // 注销 SW
                        if ('serviceWorker' in navigator) {
                            const regs = await navigator.serviceWorker.getRegistrations();
                            await Promise.all(regs.map(r => r.unregister()));
                        }
                        showNotification && showNotification('已清理', '本地缓存与SW已清理，准备强制刷新', 'success');
                    } catch (e) {
                        console.error('清理缓存失败:', e);
                    } finally {
                        const url = new URL(location.href);
                        url.searchParams.set('v', Date.now().toString());
                        location.href = url.toString();
                    }
                };

                // 调试：登录链路日志按钮（仅在调试开启时显示）
                if (typeof DEBUG_LOGIN !== 'undefined' && DEBUG_LOGIN) {
                    try {
                        const container = toolkit.querySelector('div');
                        const row = document.createElement('div');
                        row.className = 'flex gap-2 mt-2';
                        row.innerHTML = `
                            <button id="vct-loginlog" class="flex-1 px-3 py-2 text-xs font-medium bg-emerald-500 hover:bg-emerald-600 text-white rounded">🪵 登录链路日志</button>
                            <button id="vct-debugoff" class="px-3 py-2 text-xs font-medium bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200 rounded">🚫 关闭调试</button>
                        `;
                        container.appendChild(row);
                        document.getElementById('vct-loginlog').onclick = () => loginDebug.show();
                        document.getElementById('vct-debugoff').onclick = () => { localStorage.setItem('debug_login','0'); const url = new URL(location.href); url.searchParams.delete('debug'); location.href = url.toString(); };
                    } catch (e) { console.warn('调试按钮初始化失败', e); }
                }

                // 行为：复制调试信息
                document.getElementById('vct-copy').onclick = async () => {
                    const payload = {
                        href: location.href,
                        api: API_BASE_URL,
                        lastModified: document.lastModified,
                        sw: swStatus(),
                        hasAuth: !!localStorage.getItem('auth_data'),
                        hasData: !!localStorage.getItem('shipment_query_data')
                    };
                    try {
                        await navigator.clipboard.writeText(JSON.stringify(payload, null, 2));
                        showNotification && showNotification('已复制', '调试信息已复制到剪贴板', 'success');
                    } catch (e) {
                        console.log('复制失败:', e);
                    }
                };

                // 隐藏按钮
                document.getElementById('vct-hide').onclick = () => {
                    toolkit.style.display = 'none';
                };
            } catch (e) {
                console.error('版本/缓存工具初始化失败:', e);
            }
        })();

    </script>
</body>
</html>
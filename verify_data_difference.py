#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证app.py和Web端数据差异的测试
"""

import sqlite3
import sys
from pathlib import Path

def check_recent_records():
    """检查最近的货运记录和任务"""
    print('🔍 检查最近的货运记录和任务')
    print('=' * 80)
    
    # 1. 检查最近的货运记录（app.py看到的）
    print('📋 最近的货运记录（app.py视图）:')
    print('-' * 60)
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, bill_of_lading, container_number, status, created_by, created_at 
            FROM shipment_records 
            ORDER BY created_at DESC 
            LIMIT 15
        ''')
        rows = cursor.fetchall()
        
        for i, row in enumerate(rows):
            bl = row[1] if row[1] else 'N/A'
            cn = row[2] if row[2] else 'N/A'
            print(f'  {i+1:2d}. ID:{row[0]:3d} | 提单:{bl:15s} | 箱号:{cn:15s} | 状态:{row[3]:6s} | 创建者:{row[4]:15s} | 时间:{row[5]}')
        
        conn.close()
        print(f'总计: {len(rows)} 条记录')
    except Exception as e:
        print(f'❌ 查询货运记录失败: {e}')
    
    # 2. 检查最近的任务记录（按用户分组）
    print(f'\n📋 最近的任务记录（按用户分组）:')
    print('-' * 60)
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT creator_id, COUNT(*) as task_count, MAX(created_at) as latest_task
            FROM task_queue 
            GROUP BY creator_id 
            ORDER BY latest_task DESC
        ''')
        rows = cursor.fetchall()
        
        print('用户ID              任务数量    最新任务时间')
        print('-' * 60)
        for row in rows:
            print(f'{row[0]:20s} {row[1]:8d}    {row[2]}')
        
        # 3. 检查TEST001用户的最近任务
        print(f'\n📋 TEST001用户的最近任务:')
        print('-' * 60)
        cursor.execute('''
            SELECT id, tracking_number, status, created_at
            FROM task_queue 
            WHERE creator_id = 'TEST001'
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        test_rows = cursor.fetchall()
        
        for i, row in enumerate(test_rows):
            print(f'  {i+1:2d}. ID:{row[0][:8]}... | 号码:{row[1]:15s} | 状态:{row[2]:8s} | 时间:{row[3]}')
        
        conn.close()
        print(f'TEST001用户任务总计: {len(test_rows)} 条')
    except Exception as e:
        print(f'❌ 查询任务记录失败: {e}')
    
    # 4. 检查是否有TEST001创建的货运记录
    print(f'\n📋 TEST001用户创建的货运记录:')
    print('-' * 60)
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, bill_of_lading, container_number, status, created_at 
            FROM shipment_records 
            WHERE created_by = 'TEST001'
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        test_shipment_rows = cursor.fetchall()
        
        if test_shipment_rows:
            for i, row in enumerate(test_shipment_rows):
                bl = row[1] if row[1] else 'N/A'
                cn = row[2] if row[2] else 'N/A'
                print(f'  {i+1:2d}. ID:{row[0]:3d} | 提单:{bl:15s} | 箱号:{cn:15s} | 状态:{row[3]:6s} | 时间:{row[4]}')
        else:
            print('  没有找到TEST001用户创建的货运记录')
        
        conn.close()
        print(f'TEST001用户货运记录总计: {len(test_shipment_rows)} 条')
    except Exception as e:
        print(f'❌ 查询TEST001货运记录失败: {e}')
    
    # 5. 分析结论
    print(f'\n💡 分析结论:')
    print('=' * 80)
    print('如果：')
    print('  - TEST001用户有任务记录，但没有货运记录')
    print('  - 说明Web端创建的任务和货运记录的created_by字段不一致')
    print('  - app.py显示所有用户记录，可能因为记录太多而看不到新记录')
    print()
    print('建议解决方案：')
    print('  1. 在app.py中添加用户筛选功能')
    print('  2. 或者修改app.py按时间倒序显示，确保新记录在顶部')
    print('  3. 检查Web端创建记录时的created_by字段设置')

if __name__ == "__main__":
    check_recent_records()

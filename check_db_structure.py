#!/usr/bin/env python3
import sqlite3

# 检查数据库表结构
print("=== 检查任务队列表结构 ===")
try:
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(task_queue)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"列名: {col[1]}, 类型: {col[2]}, 非空: {col[3]}, 默认值: {col[4]}")
    conn.close()
except Exception as e:
    print(f"检查任务队列表失败: {e}")

print("\n=== 检查货运记录表结构 ===")
try:
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(shipment_records)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"列名: {col[1]}, 类型: {col[2]}, 非空: {col[3]}, 默认值: {col[4]}")
    
    # 查看最近的记录
    print("\n=== 最近的货运记录 ===")
    cursor.execute("SELECT * FROM shipment_records ORDER BY created_at DESC LIMIT 5")
    rows = cursor.fetchall()
    if rows:
        for row in rows:
            print(row)
    else:
        print("没有货运记录")
    
    conn.close()
except Exception as e:
    print(f"检查货运记录表失败: {e}")

print("\n=== 检查Web API数据库 ===")
try:
    conn = sqlite3.connect('db/web_api.db')
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"Web API数据库表: {[t[0] for t in tables]}")
    
    if ('shipment_records',) in tables:
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        print("Web API shipment_records表结构:")
        for col in columns:
            print(f"  列名: {col[1]}, 类型: {col[2]}")
    
    conn.close()
except Exception as e:
    print(f"检查Web API数据库失败: {e}")

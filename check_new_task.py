#!/usr/bin/env python3
"""检查刚创建的任务"""

import sqlite3

def check_new_task():
    """检查新创建的任务"""
    task_id = "593ad1c9-e1da-4ca1-9e40-8b9a418bba9c"
    
    print("=== 检查任务数据库 ===")
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 查找特定任务
        cursor.execute('SELECT * FROM task_queue WHERE id = ?', (task_id,))
        task = cursor.fetchone()
        
        if task:
            print(f"找到任务 {task_id[:8]}...")
            columns = [desc[0] for desc in cursor.description]
            for i, value in enumerate(task):
                print(f"  {columns[i]}: {value}")
        else:
            print(f"未找到任务 {task_id}")
            
            # 检查最新的任务
            print("\n最新5个任务:")
            cursor.execute('SELECT id, tracking_number, created_at FROM task_queue ORDER BY created_at DESC LIMIT 5')
            rows = cursor.fetchall()
            for row in rows:
                print(f"  {row[0][:8]}... | {row[1]} | {row[2]}")
        
        conn.close()
    except Exception as e:
        print(f"检查任务出错: {e}")
    
    print("\n=== 检查货运记录 ===")
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 查找MEDUJ0618622的最新记录
        cursor.execute('''
            SELECT id, bill_of_lading, container_number, created_at, created_by 
            FROM shipment_records 
            WHERE bill_of_lading = ? OR container_number = ?
            ORDER BY created_at DESC 
            LIMIT 1
        ''', ('MEDUJ0618622', 'MEDUJ0618622'))
        
        record = cursor.fetchone()
        if record:
            print(f"找到MEDUJ0618622的记录:")
            print(f"  ID: {record[0]}")
            print(f"  提单号: {record[1]}")
            print(f"  箱号: {record[2]}")
            print(f"  创建时间: {record[3]}")
            print(f"  创建者: {record[4]}")
        else:
            print("未找到MEDUJ0618622的记录")
        
        conn.close()
    except Exception as e:
        print(f"检查货运记录出错: {e}")

if __name__ == "__main__":
    check_new_task()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用户并生成登录token
用于快速测试Web数据显示修复
"""

import requests
import json
import uuid
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def create_test_user():
    """创建测试用户"""
    print("🔧 创建测试用户...")
    
    # 生成随机用户信息
    user_id = f"test_user_{uuid.uuid4().hex[:8]}"
    username = f"testuser{uuid.uuid4().hex[:6]}"
    password = "test123456"
    
    user_data = {
        "username": username,
        "password": password,
        "email": f"{username}@test.com",
        "full_name": "测试用户"
    }
    
    try:
        # 尝试注册用户
        response = requests.post(f"{API_BASE}/auth/register", json=user_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 用户注册成功!")
            print(f"   用户名: {username}")
            print(f"   密码: {password}")
            return username, password
        else:
            print(f"❌ 用户注册失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ 注册请求失败: {e}")
        return None, None

def login_user(username, password):
    """用户登录获取token"""
    print(f"\n🔑 用户登录: {username}")
    
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            token = result.get('access_token')
            user_info = result.get('user')
            
            print(f"✅ 登录成功!")
            print(f"   Token: {token[:20]}...")
            print(f"   用户ID: {user_info.get('user_id')}")
            print(f"   用户名: {user_info.get('username')}")
            
            return token, user_info
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None, None

def test_api_with_token(token):
    """使用token测试API"""
    print(f"\n🧪 测试API访问...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # 测试获取任务列表
        response = requests.get(f"{API_BASE}/tasks/my-tasks?limit=5", headers=headers, timeout=10)
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ API访问成功!")
            print(f"   返回任务数量: {len(tasks)}")
            
            for i, task in enumerate(tasks[:3]):
                print(f"   {i+1}. {task.get('container_number', 'N/A')} - {task.get('status', 'N/A')}")
            
            return True
        else:
            print(f"❌ API访问失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API请求失败: {e}")
        return False

def generate_login_script(username, password, token, user_info):
    """生成浏览器登录脚本"""
    print(f"\n📝 生成浏览器登录脚本...")
    
    auth_data = {
        "access_token": token,
        "user": user_info
    }
    
    script = f"""
// 在浏览器Console中执行此脚本来自动登录
localStorage.setItem('auth_data', '{json.dumps(auth_data, ensure_ascii=False)}');
console.log('✅ 登录信息已保存到localStorage');
console.log('🔄 正在刷新页面...');
location.reload();
"""
    
    with open('browser_login_script.js', 'w', encoding='utf-8') as f:
        f.write(script.strip())
    
    print(f"✅ 登录脚本已保存到: browser_login_script.js")
    print(f"\n📋 使用方法:")
    print(f"1. 打开浏览器访问: http://127.0.0.1:8080")
    print(f"2. 按F12打开开发者工具")
    print(f"3. 在Console中粘贴执行browser_login_script.js的内容")
    print(f"4. 页面会自动刷新并登录")
    
    return script

def main():
    """主函数"""
    print("🚀 创建测试用户并生成登录信息...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 创建测试用户
    username, password = create_test_user()
    if not username:
        print("💥 无法创建测试用户，请检查API服务器状态")
        return
    
    # 2. 用户登录
    token, user_info = login_user(username, password)
    if not token:
        print("💥 无法登录用户，请检查API服务器状态")
        return
    
    # 3. 测试API访问
    api_success = test_api_with_token(token)
    if not api_success:
        print("💥 API访问测试失败")
        return
    
    # 4. 生成浏览器登录脚本
    generate_login_script(username, password, token, user_info)
    
    print("\n🎉 测试用户创建成功！")
    print("\n📝 手动登录信息:")
    print(f"   用户名: {username}")
    print(f"   密码: {password}")
    print(f"   登录地址: http://127.0.0.1:8080/login.html")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用邀请码登录并验证Web数据显示修复
"""

import requests
import json
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def test_login_with_invite_code(invite_code):
    """使用邀请码登录"""
    print(f"🔑 使用邀请码登录: {invite_code}")
    
    login_data = {
        "invite_code": invite_code
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                token = result.get('access_token')
                user_info = result.get('user_info')
                
                print(f"✅ 登录成功!")
                print(f"   用户: {user_info.get('name')} ({user_info.get('role')})")
                print(f"   用户ID: {user_info.get('user_id')}")
                print(f"   权限: {user_info.get('permissions', [])}")
                print(f"   Token: {token[:20]}...")
                
                return token, user_info
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return None, None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ 登录请求异常: {e}")
        return None, None

def test_tasks_api(token):
    """测试任务API"""
    print(f"\n📋 测试任务API...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{API_BASE}/tasks/my-tasks?limit=10", headers=headers, timeout=10)
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ 任务API访问成功!")
            print(f"   返回任务数量: {len(tasks)}")
            
            if tasks:
                print(f"\n📊 任务详情:")
                for i, task in enumerate(tasks[:5]):
                    container = task.get('container_number', 'N/A')
                    status = task.get('status', 'N/A')
                    eta = task.get('estimated_arrival_time', 'N/A')
                    nodes = len(task.get('shipment_dates', []))
                    screenshot = '有' if task.get('evidence_screenshot') else '无'
                    
                    print(f"   {i+1}. {container} - {status}")
                    print(f"      预计到港: {eta}")
                    print(f"      物流节点: {nodes}条")
                    print(f"      截图: {screenshot}")
                    
                    # 检查result字段
                    result = task.get('result', {})
                    if result:
                        print(f"      Result字段: {list(result.keys())}")
                    print()
            else:
                print(f"   暂无任务数据")
            
            return True
        else:
            print(f"❌ 任务API访问失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务API请求异常: {e}")
        return False

def generate_browser_login_script(token, user_info):
    """生成浏览器登录脚本"""
    print(f"\n📝 生成浏览器登录脚本...")
    
    auth_data = {
        "access_token": token,
        "user": user_info
    }
    
    script = f"""// 在浏览器Console中执行此脚本来自动登录
localStorage.setItem('auth_data', '{json.dumps(auth_data, ensure_ascii=False)}');
console.log('✅ 登录信息已保存');
console.log('🔄 正在刷新页面...');
location.reload();"""
    
    with open('browser_login_script.js', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print(f"✅ 登录脚本已保存到: browser_login_script.js")
    
    return script

def main():
    """主函数"""
    print("🚀 测试邀请码登录并验证Web数据显示修复...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 可用的邀请码
    invite_codes = [
        'TEST001',    # 测试用户
        'LL2025001',  # 李乐 (高级用户)
        'ADMIN001',   # 系统管理员
        'USER002',    # 张三 (普通用户)
        'VIP003'      # 王五 (VIP用户)
    ]
    
    print("📋 可用邀请码:")
    for code in invite_codes:
        print(f"   - {code}")
    
    # 使用TEST001邀请码登录
    invite_code = 'TEST001'
    token, user_info = test_login_with_invite_code(invite_code)
    
    if not token:
        print("💥 登录失败，无法继续测试")
        return
    
    # 测试任务API
    api_success = test_tasks_api(token)
    
    if api_success:
        # 生成浏览器登录脚本
        generate_browser_login_script(token, user_info)
        
        print("\n🎉 测试完成！")
        print("\n📝 下一步操作:")
        print("1. 打开浏览器访问: http://127.0.0.1:8080")
        print("2. 按F12打开开发者工具")
        print("3. 在Console中粘贴执行browser_login_script.js的内容")
        print("4. 页面会自动刷新并登录")
        print("5. 创建新的查询任务来测试数据显示修复")
        
        print(f"\n🔑 或者手动登录:")
        print(f"   访问: http://127.0.0.1:8080/login.html")
        print(f"   邀请码: {invite_code}")
        
    else:
        print("💥 API测试失败")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def test_shipment_lookup():
    """测试shipment记录查询"""
    print("🔍 测试shipment记录查询...")
    
    try:
        # 连接shipment数据库
        shipment_conn = sqlite3.connect('db/shipment_records.db')
        shipment_cursor = shipment_conn.cursor()
        
        # 测试查询 TEST1756560064
        tracking_number = "TEST1756560064"
        print(f"\n=== 测试查询 {tracking_number} ===")
        
        shipment_cursor.execute("""
            SELECT id, carrier_company, status, estimated_arrival_time, 
                   evidence_screenshot, remarks, bill_of_lading, container_number,
                   created_at, updated_at
            FROM shipment_records 
            WHERE bill_of_lading = ? OR container_number = ?
            ORDER BY updated_at DESC
        """, (tracking_number, tracking_number))
        
        results = shipment_cursor.fetchall()
        if results:
            print(f"  找到 {len(results)} 条记录:")
            for i, result in enumerate(results, 1):
                print(f"    {i}. ID: {result[0]}, 承运人: {result[1]}, 状态: {result[2]}")
                print(f"       ETA: {result[3]}, 截图: {result[4]}")
                print(f"       提单号: {result[6]}, 集装箱号: {result[7]}")
                print(f"       创建时间: {result[8]}, 更新时间: {result[9]}")
        else:
            print("  没有找到记录")
            
        # 测试查询 MEDUJ0616089
        tracking_number = "MEDUJ0616089"
        print(f"\n=== 测试查询 {tracking_number} ===")
        
        shipment_cursor.execute("""
            SELECT id, carrier_company, status, estimated_arrival_time, 
                   evidence_screenshot, remarks, bill_of_lading, container_number,
                   created_at, updated_at
            FROM shipment_records 
            WHERE bill_of_lading = ? OR container_number = ?
            ORDER BY updated_at DESC LIMIT 5
        """, (tracking_number, tracking_number))
        
        results = shipment_cursor.fetchall()
        if results:
            print(f"  找到 {len(results)} 条记录:")
            for i, result in enumerate(results, 1):
                print(f"    {i}. ID: {result[0]}, 承运人: {result[1]}, 状态: {result[2]}")
                print(f"       ETA: {result[3]}, 截图: {result[4]}")
                print(f"       提单号: {result[6]}, 集装箱号: {result[7]}")
                print(f"       创建时间: {result[8]}, 更新时间: {result[9]}")
        else:
            print("  没有找到记录")
            
        # 检查数据库中所有记录
        print(f"\n=== 检查所有记录（最新10条）===")
        shipment_cursor.execute("""
            SELECT id, carrier_company, status, bill_of_lading, container_number, updated_at
            FROM shipment_records 
            ORDER BY updated_at DESC LIMIT 10
        """)
        
        results = shipment_cursor.fetchall()
        if results:
            print(f"  最近的 {len(results)} 条记录:")
            for i, result in enumerate(results, 1):
                print(f"    {i}. ID: {result[0]}, 承运人: {result[1]}, 状态: {result[2]}")
                print(f"       提单号: {result[3]}, 集装箱号: {result[4]}, 更新: {result[5]}")
        
        shipment_conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_shipment_lookup()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复task_queue表结构，添加result字段
"""

import sqlite3
import os
import json

def fix_task_queue_schema():
    """为task_queue表添加result字段"""
    
    db_path = "db/task_queue.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"🔧 修复数据库表结构: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查result字段是否已存在
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'result' in column_names:
            print("✅ result字段已存在，无需修复")
            return True
        
        print("📝 添加result字段...")
        
        # 添加result字段
        cursor.execute("ALTER TABLE task_queue ADD COLUMN result TEXT")
        
        # 提交更改
        conn.commit()
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'result' in column_names:
            print("✅ result字段添加成功！")
            
            # 显示更新后的表结构
            print("\n📋 更新后的表结构:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
            
            return True
        else:
            print("❌ result字段添加失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def migrate_existing_data():
    """迁移现有的result_summary数据到result字段"""
    
    db_path = "db/task_queue.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找有result_summary但没有result的记录
        cursor.execute("""
            SELECT id, result_summary 
            FROM task_queue 
            WHERE result_summary IS NOT NULL 
            AND result_summary != ''
            AND (result IS NULL OR result = '')
        """)
        
        records = cursor.fetchall()
        
        if not records:
            print("✅ 没有需要迁移的数据")
            return True
        
        print(f"📦 发现 {len(records)} 条需要迁移的记录")
        
        migrated = 0
        for record_id, result_summary in records:
            try:
                # 尝试解析result_summary为JSON
                if result_summary.startswith('{') or result_summary.startswith('['):
                    # 看起来像JSON，直接使用
                    result_data = result_summary
                else:
                    # 纯文本，包装成JSON
                    result_data = json.dumps({"summary": result_summary}, ensure_ascii=False)
                
                # 更新result字段
                cursor.execute("""
                    UPDATE task_queue 
                    SET result = ? 
                    WHERE id = ?
                """, (result_data, record_id))
                
                migrated += 1
                
            except Exception as e:
                print(f"⚠️  迁移记录 {record_id} 失败: {e}")
                continue
        
        conn.commit()
        print(f"✅ 成功迁移 {migrated} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据迁移失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("🚀 开始修复task_queue表结构...")
    
    # 1. 添加result字段
    if not fix_task_queue_schema():
        print("💥 表结构修复失败！")
        return False
    
    # 2. 迁移现有数据
    if not migrate_existing_data():
        print("💥 数据迁移失败！")
        return False
    
    print("🎉 task_queue表结构修复完成！")
    return True

if __name__ == "__main__":
    main()

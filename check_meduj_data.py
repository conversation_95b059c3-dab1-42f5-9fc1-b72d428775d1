#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MEDUJ0616089的详细数据
"""

import sqlite3
import json

def check_meduj_data():
    """检查MEDUJ0616089的详细数据"""
    print("🔍 检查MEDUJ0616089的详细数据...")
    
    # 检查shipment_records
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, bill_of_lading, status, carrier_company, 
                   estimated_arrival_time, evidence_screenshot, remarks
            FROM shipment_records 
            WHERE bill_of_lading = 'MEDUJ0616089'
            ORDER BY updated_at DESC 
            LIMIT 1
        """)
        
        shipment = cursor.fetchone()
        if shipment:
            print(f"\n📦 shipment_records中的数据:")
            print(f"  ID: {shipment[0]}")
            print(f"  提单号: {shipment[1]}")
            print(f"  状态: {shipment[2]}")
            print(f"  承运人: {shipment[3]}")
            print(f"  预计到港: {shipment[4]}")
            print(f"  截图: {shipment[5]}")
            print(f"  备注: {shipment[6]}")
            
            # 检查物流节点
            cursor.execute("""
                SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?
            """, (shipment[0],))
            dates_count = cursor.fetchone()[0]
            print(f"  物流节点数量: {dates_count}")
            
            if dates_count > 0:
                cursor.execute("""
                    SELECT date_type, planned_date, actual_date, status, location
                    FROM shipment_dates 
                    WHERE shipment_id = ?
                    ORDER BY id
                    LIMIT 5
                """, (shipment[0],))
                
                dates = cursor.fetchall()
                print(f"  前5个物流节点:")
                for i, date in enumerate(dates, 1):
                    print(f"    {i}. {date[0]} - {date[3]} - {date[4]}")
        else:
            print(f"❌ 在shipment_records中未找到MEDUJ0616089")
        
        conn.close()
        
    except Exception as e:
        print(f"检查shipment_records失败: {e}")
    
    # 检查task_queue
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, task_stage, status, result_summary, remarks
            FROM task_queue 
            WHERE tracking_number = 'MEDUJ0616089' AND creator_id = 'TEST001'
            ORDER BY created_at DESC
        """)
        
        tasks = cursor.fetchall()
        print(f"\n📋 task_queue中的任务:")
        for task in tasks:
            print(f"  任务ID: {task[0][:8]}...")
            print(f"  阶段: {task[1]}")
            print(f"  状态: {task[2]}")
            print(f"  备注: {task[4]}")
            if task[3]:
                print(f"  结果摘要: {task[3][:100]}...")
            print(f"  ---")
        
        conn.close()
        
    except Exception as e:
        print(f"检查task_queue失败: {e}")

if __name__ == "__main__":
    check_meduj_data()

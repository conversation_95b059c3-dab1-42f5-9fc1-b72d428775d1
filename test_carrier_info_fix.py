#!/usr/bin/env python3
"""测试修复后的承运商信息处理"""

import requests
import json
import sqlite3
from datetime import datetime

def test_carrier_info_fix():
    """测试修复后的承运商信息"""
    
    # 1. 获取token
    response = requests.post(
        'http://127.0.0.1:8080/api/v1/auth/login', 
        json={'invite_code': 'TEST001'}
    )
    
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return
    
    token_data = response.json()
    token = token_data.get('access_token')
    
    if not token:
        print("无法获取token")
        return
    
    print(f"获取token成功")
    
    # 2. 使用MEDUJ0618622测试（应该识别为MSC）
    tracking_number = "MEDUJ0618622"
    print(f"测试跟踪号: {tracking_number}")
    
    # 3. 创建任务
    task_data = {
        "container_number": tracking_number,
        "carrier_code": "MSC",
        "priority": "normal"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        "http://127.0.0.1:8080/api/v1/tasks/quick-create",
        json=task_data,
        headers=headers
    )
    
    print(f"创建响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        task_id = result.get('task_id')
        print(f"任务创建成功，ID: {task_id}")
        
        # 4. 检查数据库中的承运商信息
        print("\n=== 检查任务数据库中的承运商信息 ===")
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, tracking_number, carrier, task_name FROM task_queue WHERE id = ?', (task_id,))
        task_info = cursor.fetchone()
        
        if task_info:
            print(f"任务ID: {task_info[0]}")
            print(f"跟踪号: {task_info[1]}")
            print(f"承运商: {task_info[2]}")
            print(f"任务名: {task_info[3]}")
        else:
            print("未找到任务信息")
        conn.close()
        
        print("\n=== 检查货运记录中的承运商信息 ===")
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, bill_of_lading, carrier_company FROM shipment_records WHERE bill_of_lading = ? ORDER BY created_at DESC LIMIT 1', (tracking_number,))
        record_info = cursor.fetchone()
        
        if record_info:
            print(f"记录ID: {record_info[0]}")
            print(f"提单号: {record_info[1]}")
            print(f"承运商: {record_info[2]}")
            
            # 检查承运商信息是否完整
            if "Mediterranean Shipping Company" in record_info[2]:
                print("✅ 承运商信息完整，包含详细描述")
            else:
                print("❌ 承运商信息不完整，只有简单代码")
        else:
            print("未找到货运记录")
        conn.close()
        
    else:
        print(f"任务创建失败: {response.text}")

if __name__ == "__main__":
    test_carrier_info_fix()

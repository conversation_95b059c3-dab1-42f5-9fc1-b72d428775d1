#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端API调用流程
模拟前端完整的HTTP请求流程
"""

import requests
import json
import sys
from pathlib import Path

def test_login_api():
    """测试登录API"""
    print('🔐 测试登录API')
    print('=' * 50)
    
    login_url = 'http://127.0.0.1:8080/api/v1/auth/login'
    login_data = {
        'invite_code': 'TEST001'  # 使用有效的测试邀请码
    }
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f'  请求URL: {login_url}')
        print(f'  请求数据: {login_data}')
        print(f'  响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'  响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}')
            
            if result.get('success') and result.get('access_token'):
                print('  ✅ 登录成功')
                return result['access_token'], result.get('user_info')
            else:
                print('  ❌ 登录失败')
                return None, None
        else:
            print(f'  ❌ HTTP错误: {response.status_code}')
            print(f'  错误内容: {response.text}')
            return None, None
    except Exception as e:
        print(f'  ❌ 登录请求异常: {e}')
        return None, None

def test_quick_create_api(token, container_number):
    """测试快速创建任务API"""
    print(f'\n📦 测试快速创建任务API')
    print('=' * 50)
    
    create_url = 'http://127.0.0.1:8080/api/v1/tasks/quick-create'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    create_data = {
        'container_number': container_number
    }
    
    try:
        response = requests.post(create_url, json=create_data, headers=headers, timeout=10)
        print(f'  请求URL: {create_url}')
        print(f'  请求头: {headers}')
        print(f'  请求数据: {create_data}')
        print(f'  响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'  响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}')
            print('  ✅ 任务创建成功')
            return result
        else:
            print(f'  ❌ HTTP错误: {response.status_code}')
            print(f'  错误内容: {response.text}')
            return None
    except Exception as e:
        print(f'  ❌ 创建任务请求异常: {e}')
        return None

def test_get_my_tasks_api(token):
    """测试获取我的任务列表API"""
    print(f'\n📋 测试获取任务列表API')
    print('=' * 50)
    
    tasks_url = 'http://127.0.0.1:8080/api/v1/tasks/my-tasks'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(tasks_url, headers=headers, timeout=10)
        print(f'  请求URL: {tasks_url}')
        print(f'  请求头: {headers}')
        print(f'  响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'  找到任务数量: {len(result) if isinstance(result, list) else "未知"}')
            
            if isinstance(result, list) and len(result) > 0:
                print('  最近5个任务:')
                for i, task in enumerate(result[:5]):
                    print(f'    {i+1}. ID: {task.get("task_id", "N/A")}, 号码: {task.get("container_number", "N/A")}, 状态: {task.get("status", "N/A")}')
                print('  ✅ 任务列表获取成功')
            else:
                print('  ⚠️  任务列表为空')
            return result
        else:
            print(f'  ❌ HTTP错误: {response.status_code}')
            print(f'  错误内容: {response.text}')
            return None
    except Exception as e:
        print(f'  ❌ 获取任务列表请求异常: {e}')
        return None

def check_api_server():
    """检查API服务器是否在运行"""
    print('🌐 检查API服务器状态')
    print('=' * 50)
    
    # 尝试多个可能的URL
    test_urls = [
        'http://localhost:8080/health',
        'http://localhost:8080/',
        'http://localhost:8080/api/v1/auth/login',
        'http://127.0.0.1:8080/health',
        'http://127.0.0.1:8080/',
    ]
    
    for url in test_urls:
        try:
            print(f'  尝试连接: {url}')
            response = requests.get(url, timeout=3)
            print(f'    响应状态: {response.status_code}')
            
            if response.status_code in [200, 404, 422]:  # 404和422说明服务器在运行，只是路由问题
                print(f'    响应内容: {response.text[:100]}...')
                print('  ✅ API服务器运行正常')
                return True
        except Exception as e:
            print(f'    连接失败: {str(e)[:50]}...')
            continue
    
    print('  ❌ 无法连接到API服务器')
    print('  💡 请检查API服务器状态和端口配置')
    return False

def main():
    """主测试函数"""
    print('🧪 前端API调用流程测试')
    print('=' * 80)
    
    # 1. 检查API服务器状态
    if not check_api_server():
        print('\n❌ API服务器未运行，测试终止')
        return
    
    # 2. 测试登录
    print(f'\n' + '='*80)
    token, user_info = test_login_api()
    
    if not token:
        print('\n❌ 登录失败，无法继续测试')
        return
    
    print(f'\n📊 登录信息:')
    print(f'  Token: {token[:20]}...')
    if user_info:
        print(f'  用户ID: {user_info.get("user_id", "N/A")}')
        print(f'  用户名: {user_info.get("name", "N/A")}')
        print(f'  权限: {user_info.get("permissions", [])}')
    
    # 3. 测试创建任务
    print(f'\n' + '='*80)
    from datetime import datetime
    test_container = f'HTTP{datetime.now().strftime("%m%d%H%M%S")}'
    
    task_result = test_quick_create_api(token, test_container)
    
    # 4. 测试获取任务列表
    print(f'\n' + '='*80)
    tasks_result = test_get_my_tasks_api(token)
    
    # 5. 验证新创建的任务是否在列表中
    if task_result and tasks_result:
        print(f'\n🔍 验证新创建的任务')
        print('=' * 50)
        created_task_id = task_result.get('task_id')
        found_in_list = False
        
        if isinstance(tasks_result, list):
            for task in tasks_result:
                if task.get('task_id') == created_task_id:
                    found_in_list = True
                    print(f'  ✅ 在任务列表中找到新创建的任务: {created_task_id}')
                    break
        
        if not found_in_list:
            print(f'  ❌ 在任务列表中未找到新创建的任务: {created_task_id}')
    
    # 6. 总结
    print(f'\n📋 测试总结')
    print('=' * 80)
    print(f'  API服务器状态: ✅')
    print(f'  登录功能: {"✅" if token else "❌"}')
    print(f'  任务创建功能: {"✅" if task_result else "❌"}')
    print(f'  任务列表功能: {"✅" if tasks_result is not None else "❌"}')
    
    if token and task_result and tasks_result is not None:
        print(f'\n💡 结论: 前端API调用流程正常，问题可能在:')
        print(f'  1. 任务处理器没有运行')
        print(f'  2. 任务处理器没有正确处理新任务')
        print(f'  3. 前端页面的数据刷新机制有问题')
    else:
        print(f'\n💡 结论: 前端API调用存在问题，需要进一步排查')

if __name__ == "__main__":
    main()

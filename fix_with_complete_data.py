#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用完整数据修复任务result字段
"""

import sqlite3
import json
from datetime import datetime

def fix_with_complete_data():
    """使用完整数据修复任务result字段"""
    print("🔧 使用完整数据修复任务result字段...")
    
    # 连接数据库
    task_conn = sqlite3.connect('db/task_queue.db')
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    
    task_cursor = task_conn.cursor()
    shipment_cursor = shipment_conn.cursor()
    
    try:
        # 查询所有已完成的任务
        task_cursor.execute("""
            SELECT id, tracking_number, task_stage
            FROM task_queue 
            WHERE status = 'completed' 
            AND tracking_number = 'MEDUJ0616089'
        """)
        
        tasks = task_cursor.fetchall()
        print(f"找到 {len(tasks)} 个MEDUJ0616089任务")
        
        # 使用记录225的完整数据
        shipment_cursor.execute("""
            SELECT id, estimated_arrival_time, evidence_screenshot, carrier_company
            FROM shipment_records 
            WHERE id = 225
        """)
        
        shipment_record = shipment_cursor.fetchone()
        
        if not shipment_record:
            print("❌ 未找到记录225")
            return False
        
        shipment_id, eta, screenshot, carrier = shipment_record
        print(f"✅ 使用记录225: ETA={eta}, 截图={'有' if screenshot else '无'}")
        
        # 查询物流节点
        shipment_cursor.execute("""
            SELECT date, type, location, description, status
            FROM shipment_dates
            WHERE shipment_id = 225
            ORDER BY date ASC
        """)
        
        dates_records = shipment_cursor.fetchall()
        
        # 构建tracking_points数据
        tracking_points = []
        for date, event_type, location, description, status in dates_records:
            tracking_points.append({
                "date": date,
                "type": event_type,
                "location": location,
                "description": description,
                "status": status
            })
        
        print(f"📍 找到 {len(tracking_points)} 个物流节点")
        
        fixed_count = 0
        
        for task_id, tracking_number, task_stage in tasks:
            print(f"更新任务: {tracking_number} ({task_stage})")
            
            # 构建result数据
            result_data = {
                "estimated_arrival_time": eta,
                "tracking_points": tracking_points,
                "screenshots": [screenshot] if screenshot else [],
                "carrier_company": carrier,
                "shipment_id": shipment_id,
                "task_stage": task_stage,
                "fixed_at": datetime.now().isoformat()
            }
            
            # 更新task_queue表的result字段
            result_json = json.dumps(result_data, ensure_ascii=False)
            task_cursor.execute("""
                UPDATE task_queue 
                SET result = ?
                WHERE id = ?
            """, (result_json, task_id))
            
            fixed_count += 1
        
        # 提交更改
        task_conn.commit()
        
        print(f"✅ 修复完成！共修复 {fixed_count} 个任务")
        
        # 验证修复结果
        task_cursor.execute("""
            SELECT id, tracking_number, task_stage, result
            FROM task_queue 
            WHERE tracking_number = 'MEDUJ0616089'
            AND result IS NOT NULL
        """)
        
        updated_tasks = task_cursor.fetchall()
        
        for task_id, tracking_number, task_stage, result in updated_tasks:
            result_data = json.loads(result)
            eta = result_data.get('estimated_arrival_time')
            nodes = len(result_data.get('tracking_points', []))
            screenshots = len(result_data.get('screenshots', []))
            
            print(f"验证: {tracking_number} ({task_stage}) - ETA: {eta}, 节点: {nodes}, 截图: {screenshots}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        task_conn.rollback()
        return False
        
    finally:
        task_conn.close()
        shipment_conn.close()

if __name__ == "__main__":
    fix_with_complete_data()

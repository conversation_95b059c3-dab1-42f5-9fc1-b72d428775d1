#!/usr/bin/env python3
"""快速检查数据库状态"""

import sqlite3
from datetime import datetime

def check_task_queue():
    """检查task_queue.db"""
    print("=== 检查task_queue.db ===")
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 检查最新的任务
        cursor.execute('''
            SELECT id, tracking_number, carrier, status, created_at, creator_id 
            FROM task_queue 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        rows = cursor.fetchall()
        if rows:
            for row in rows:
                print(f"Task ID: {row[0][:8]}...")
                print(f"Tracking Number: {row[1]}")
                print(f"Carrier: {row[2]}")
                print(f"Status: {row[3]}")
                print(f"Created: {row[4]}")
                print(f"Creator ID: {row[5]}")
                print("---")
        else:
            print("没有找到任务记录")
            
        # 检查总数
        cursor.execute('SELECT COUNT(*) FROM task_queue')
        count = cursor.fetchone()[0]
        print(f"总任务数: {count}")
        
        # 特别检查MEDUJ0618622的任务
        cursor.execute('SELECT * FROM task_queue WHERE tracking_number = ?', ('MEDUJ0618622',))
        meduj_tasks = cursor.fetchall()
        if meduj_tasks:
            print(f"\n找到MEDUJ0618622的任务 ({len(meduj_tasks)}个):")
            for task in meduj_tasks:
                print(f"  ID: {task[0]}")
                print(f"  创建时间: {task[8]}")
                print(f"  创建者: {task[6]}")
                print(f"  状态: {task[5]}")
        else:
            print("\n没有找到MEDUJ0618622的任务")
        
        conn.close()
    except Exception as e:
        print(f"检查task_queue.db出错: {e}")

def check_shipment_records():
    """检查shipment_records.db"""
    print("\n=== 检查shipment_records.db ===")
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 检查最新的记录
        cursor.execute('''
            SELECT container_number, bill_of_lading, carrier_company, status, created_at, created_by
            FROM shipment_records 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        rows = cursor.fetchall()
        if rows:
            for row in rows:
                print(f"Container: {row[0]}")
                print(f"Bill of Lading: {row[1]}")
                print(f"Carrier: {row[2]}")
                print(f"Status: {row[3]}")
                print(f"Created: {row[4]}")
                print(f"Creator: {row[5]}")
                print("---")
        else:
            print("没有找到货运记录")
            
        # 检查总数
        cursor.execute('SELECT COUNT(*) FROM shipment_records')
        count = cursor.fetchone()[0]
        print(f"总货运记录数: {count}")
        
        # 特别检查MEDUJ0618622的记录
        cursor.execute('SELECT * FROM shipment_records WHERE bill_of_lading = ? OR container_number = ?', 
                      ('MEDUJ0618622', 'MEDUJ0618622'))
        meduj_records = cursor.fetchall()
        if meduj_records:
            print(f"\n找到MEDUJ0618622的记录 ({len(meduj_records)}个):")
            for record in meduj_records:
                print(f"  ID: {record[0]}")
                print(f"  提单号: {record[1]}")
                print(f"  箱号: {record[2]}")
                print(f"  创建时间: {record[9]}")
                print(f"  创建者: {record[8]}")
                print(f"  状态: {record[3]}")
        else:
            print("\n没有找到MEDUJ0618622的记录")
        
        conn.close()
    except Exception as e:
        print(f"检查shipment_records.db出错: {e}")

if __name__ == "__main__":
    check_task_queue()
    check_shipment_records()

#!/usr/bin/env python3
import requests
import json

# 测试API端点
base_url = "http://127.0.0.1:8080"

def test_endpoint(path, description):
    try:
        response = requests.get(f"{base_url}{path}", timeout=5)
        print(f"{description}: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  响应: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
        else:
            print(f"  错误: {response.text[:100]}...")
        return response.status_code == 200
    except Exception as e:
        print(f"{description}: 失败 - {e}")
        return False

print("测试API端点...")
print("=" * 50)

# 测试各种端点
endpoints = [
    ("/", "根路径"),
    ("/api/v1/system/processor-status", "处理器状态"),
    ("/api/v1/tasks/my-tasks?limit=3", "用户任务列表"),
    ("/api/v1/system/task-stats", "任务统计"),
    ("/api/v1/system/shipment-stats", "货运统计"),
]

for path, desc in endpoints:
    test_endpoint(path, desc)
    print()

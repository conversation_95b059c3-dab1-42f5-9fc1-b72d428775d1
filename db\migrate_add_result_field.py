#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为task_queue表添加result字段
用于存储任务的结果数据（JSON格式）
"""

import sqlite3
import os
import sys

def migrate_add_result_field():
    """为task_queue表添加result字段"""
    
    # 数据库文件路径
    db_path = os.path.join(os.path.dirname(__file__), 'task_queue.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    print(f"🔄 开始迁移数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查result字段是否已存在
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'result' in column_names:
            print("✅ result字段已存在，无需迁移")
            return True
        
        print("📝 添加result字段...")
        
        # 添加result字段
        cursor.execute("""
            ALTER TABLE task_queue 
            ADD COLUMN result TEXT
        """)
        
        # 提交更改
        conn.commit()
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'result' in column_names:
            print("✅ result字段添加成功！")
            
            # 显示更新后的表结构
            print("\n📋 更新后的表结构:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
            
            return True
        else:
            print("❌ result字段添加失败")
            return False
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 开始数据库迁移...")
    success = migrate_add_result_field()
    if success:
        print("✨ 数据库迁移完成！")
        sys.exit(0)
    else:
        print("💥 数据库迁移失败！")
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复现有任务的result数据
"""

import sqlite3
import json
from datetime import datetime

def simple_fix_results():
    """简单修复现有任务的result数据"""
    print("🔧 简单修复现有任务的result数据...")
    
    # 连接数据库
    task_conn = sqlite3.connect('db/task_queue.db')
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    
    task_cursor = task_conn.cursor()
    shipment_cursor = shipment_conn.cursor()
    
    try:
        # 查询所有已完成但没有result数据的任务
        task_cursor.execute("""
            SELECT id, tracking_number, task_stage
            FROM task_queue 
            WHERE status = 'completed' 
            AND (result IS NULL OR result = '')
            LIMIT 5
        """)
        
        tasks = task_cursor.fetchall()
        print(f"找到 {len(tasks)} 个需要修复的任务")
        
        fixed_count = 0
        
        for task_id, tracking_number, task_stage in tasks:
            print(f"处理任务: {tracking_number} ({task_stage})")
            
            # 查询对应的货运记录
            shipment_cursor.execute("""
                SELECT id, estimated_arrival_time, evidence_screenshot
                FROM shipment_records 
                WHERE bill_of_lading = ? OR container_number = ?
                ORDER BY updated_at DESC
                LIMIT 1
            """, (tracking_number, tracking_number))
            
            shipment_record = shipment_cursor.fetchone()
            
            if not shipment_record:
                print(f"  未找到货运记录")
                continue
            
            shipment_id, eta, screenshot = shipment_record
            
            # 查询物流节点数量
            shipment_cursor.execute("""
                SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = ?
            """, (shipment_id,))
            
            node_count = shipment_cursor.fetchone()[0]
            
            # 构建简单的result数据
            result_data = {
                "estimated_arrival_time": eta,
                "tracking_points": [{"count": node_count}],  # 简化的节点数据
                "screenshots": [screenshot] if screenshot else []
            }
            
            # 更新task_queue表的result字段
            result_json = json.dumps(result_data, ensure_ascii=False)
            task_cursor.execute("""
                UPDATE task_queue 
                SET result = ?
                WHERE id = ?
            """, (result_json, task_id))
            
            print(f"  已更新: ETA={eta}, 节点={node_count}, 截图={'有' if screenshot else '无'}")
            fixed_count += 1
        
        # 提交更改
        task_conn.commit()
        
        print(f"修复完成！共修复 {fixed_count} 个任务")
        return True
        
    except Exception as e:
        print(f"修复失败: {e}")
        task_conn.rollback()
        return False
        
    finally:
        task_conn.close()
        shipment_conn.close()

if __name__ == "__main__":
    simple_fix_results()

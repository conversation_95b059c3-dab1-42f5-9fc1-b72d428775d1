#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def check_db_structure():
    """检查数据库结构"""
    print("🔍 检查task_queue数据库结构...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\n📋 数据库中的所有表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查主要表的结构
        for table_name in ['tasks', 'task_queue']:
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                if columns:
                    print(f"\n📊 表 '{table_name}' 的结构:")
                    for col in columns:
                        print(f"  {col[1]} ({col[2]})")
                    break
            except:
                continue
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库结构时出错: {e}")

if __name__ == "__main__":
    check_db_structure()

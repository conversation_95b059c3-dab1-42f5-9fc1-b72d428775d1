#!/usr/bin/env python3
"""检查数据库表结构"""

import sqlite3

def check_table_structure():
    """检查数据库表结构"""
    print("=== 检查task_queue.db表结构 ===")
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"表列表: {tables}")
        
        # 检查task_queue表结构
        if any('task_queue' in str(table) for table in tables):
            cursor.execute("PRAGMA table_info(task_queue);")
            columns = cursor.fetchall()
            print("task_queue表列信息:")
            for col in columns:
                print(f"  {col}")
        
        # 查看所有表的数据
        for table in tables:
            table_name = table[0]
            print(f"\n=== 表 {table_name} 的数据 ===")
            cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3")
            rows = cursor.fetchall()
            for row in rows:
                print(f"  {row}")
        
        conn.close()
    except Exception as e:
        print(f"检查task_queue.db出错: {e}")

    print("\n=== 检查shipment_records.db表结构 ===")
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"表列表: {tables}")
        
        # 检查shipment_records表结构
        if any('shipment_records' in str(table) for table in tables):
            cursor.execute("PRAGMA table_info(shipment_records);")
            columns = cursor.fetchall()
            print("shipment_records表列信息:")
            for col in columns:
                print(f"  {col}")
        
        # 查看所有表的数据
        for table in tables:
            table_name = table[0]
            print(f"\n=== 表 {table_name} 的数据 ===")
            cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3")
            rows = cursor.fetchall()
            for row in rows:
                print(f"  {row}")
        
        conn.close()
    except Exception as e:
        print(f"检查shipment_records.db出错: {e}")

if __name__ == "__main__":
    check_table_structure()

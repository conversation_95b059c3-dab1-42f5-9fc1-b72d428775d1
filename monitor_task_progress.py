#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控任务处理进度
"""

import requests
import time
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def get_token():
    """获取访问令牌"""
    login_data = {"invite_code": "TEST001"}
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get('access_token')
    return None

def get_tasks(token):
    """获取任务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/tasks/my-tasks", headers=headers)
    if response.status_code == 200:
        return response.json()
    return []

def display_task_progress(task):
    """显示任务进度"""
    print(f"\n📦 {task['container_number']} ({task['task_id'][:8]}...)")
    print(f"   主状态: {task['status']}")
    print(f"   网页抓取: {task.get('scraping_status', 'N/A')}")
    print(f"   AI分析: {task.get('ai_status', 'N/A')}")
    print(f"   货运状态: {task.get('shipment_status', 'N/A')}")
    print(f"   截图: {'有' if task.get('evidence_screenshot') else '无'}")
    print(f"   物流节点: {len(task.get('shipment_dates', []))}个")
    if task.get('estimated_arrival_time'):
        print(f"   预计到港: {task['estimated_arrival_time']}")

def main():
    """主监控循环"""
    print("🔍 开始监控任务处理进度...")
    print("按 Ctrl+C 停止监控")
    
    token = get_token()
    if not token:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功")
    
    last_states = {}
    
    try:
        while True:
            tasks = get_tasks(token)
            
            print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 发现 {len(tasks)} 个任务")
            
            for task in tasks:
                task_id = task['task_id']
                current_state = {
                    'status': task['status'],
                    'scraping': task.get('scraping_status', 'N/A'),
                    'ai': task.get('ai_status', 'N/A'),
                    'shipment': task.get('shipment_status', 'N/A'),
                    'screenshot': bool(task.get('evidence_screenshot')),
                    'dates_count': len(task.get('shipment_dates', []))
                }
                
                # 检查是否有状态变化
                if task_id not in last_states or last_states[task_id] != current_state:
                    print(f"\n🔄 任务状态变化:")
                    display_task_progress(task)
                    last_states[task_id] = current_state
                
                # 如果任务完成，显示详细结果
                if current_state['status'] in ['completed', 'partially_completed']:
                    print(f"🎉 任务 {task['container_number']} 处理完成!")
                    if task.get('shipment_dates'):
                        print(f"   物流节点详情:")
                        for i, date_info in enumerate(task['shipment_dates'][:3], 1):
                            print(f"     {i}. {date_info.get('date_type')} - {date_info.get('status', 'N/A')}")
            
            if not tasks:
                print("   (暂无任务)")
            
            time.sleep(10)  # 每10秒检查一次
            
    except KeyboardInterrupt:
        print("\n\n👋 监控停止")

if __name__ == "__main__":
    main()

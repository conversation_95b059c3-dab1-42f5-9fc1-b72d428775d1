<!DOCTYPE html><html lang="en" dir="ltr"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript" async="" src="https://t.contentsquare.net/uxa/fc4c51afcbd03.js"></script><script async="" src="https://www.clarity.ms/tag/uet/56292612?insights=1"></script><script type="text/javascript" async="" src="https://bat.bing.com/bat.js"></script><script async="" src="https://scripts.clarity.ms/0.8.26/clarity.js"></script><script async="" src="https://www.clarity.ms/tag/gxpeclq6od?ref=gtm2"></script><script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-7960BT6SN5&amp;cx=c&amp;gtm=4e58r1"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-9HMJRMP77C&amp;cx=c&amp;gtm=4e58r1"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=DC-9833327&amp;cx=c&amp;gtm=4e58r1"></script><script data-ot-ignore="" async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-KJGHWFR"></script><script type="module" src="/_sc/Assets/scripts/alpine.min.js?v=2526057"></script>
    <link href="/_sc/Assets/css/main.css?v=2526057" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/_sc/Assets/images/favicons/favicon.ico?v=2526057">
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('consent', 'default', {
                'ad_storage': 'denied',
                'analytics_storage': 'denied',
                'ad_personalization': 'denied',
                'ad_user_data': 'denied',
                'wait_for_update': 2000
            });
        </script>

	<script>
		var dataLayer = window.dataLayer = window.dataLayer || [];
		var dimensions = {
			"codeStatus": '200',
			"pageCategory": 'Service Page',
			"pageName": 'Track a shipment'
		};
		dataLayer.push(dimensions);
	</script>

	<!-- Google Tag Manager -->
	<script>
		(function (w, d, s, l, i) {
			w[l] = w[l] || []; w[l].push({
				'gtm.start': new Date().getTime(),
				event: 'gtm.js'
			});
			var f = d.getElementsByTagName(s)[0], j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
			j.setAttributeNode(d.createAttribute('data-ot-ignore'));
			j.async = true; j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
			f.parentNode.insertBefore(j, f);
		})(window, document, 'script', 'dataLayer', 'GTM-KJGHWFR');
	</script>
	<!-- End Google Tag Manager -->




<!-- OneTrust Cookies Consent Notice start for msc.com -->
<script src="https://cdn.cookielaw.org/consent/52856e67-db13-4ee2-a0ff-2746eb011113/otSDKStub.js" type="text/javascript" charset="UTF-8" data-domain-script="52856e67-db13-4ee2-a0ff-2746eb011113"></script>
<script type="text/javascript">
function OptanonWrapper() { }
</script>
<!-- OneTrust Cookies Consent Notice end for msc.com -->

<script src="https://privacyportal-eu-cdn.onetrust.com/consent-receipt-scripts/scripts/otconsent-1.0.min.js" type="text/javascript" data-ot-id="4d0f0420-9941-4d59-903c-3cb61fde5441" charset="UTF-8" id="consent-receipt-script" data-ot-consent-register-event="document-load"></script>


<!-- Hotjar Tracking Code for https://www.msc.com -->



	<title>Shipping Container Tracking and Tracing | MSC</title>
	<meta name="description" content="MSC&nbsp;offers an online&nbsp;tracking&nbsp;and tracing system enabling containers to be tracked throughout the world.&nbsp;Find&nbsp;your&nbsp;freight&nbsp;fast. Contact our team today!">
	<meta name="robots" content="index, follow">
	<link rel="canonical" href="https://www.msc.com/en/track-a-shipment">

		<meta name="twitter:title" content="Shipping Container Tracking and Tracing | MSC">
		<meta name="twitter:description" content="MSC&nbsp;offers an online&nbsp;tracking&nbsp;and tracing system enabling containers to be tracked throughout the world.&nbsp;Find&nbsp;your&nbsp;freight&nbsp;fast. Contact our team today!">
		<meta property="og:url" content="https://www.msc.com/en/track-a-shipment">
		<meta property="og:type" content="article">
		<meta property="og:title" content="Shipping Container Tracking and Tracing | MSC">
		<meta property="og:description" content="MSC&nbsp;offers an online&nbsp;tracking&nbsp;and tracing system enabling containers to be tracked throughout the world.&nbsp;Find&nbsp;your&nbsp;freight&nbsp;fast. Contact our team today!">
		<meta property="og:site_name" content="MSC">
		<meta property="og:locale" content="en_US">

    
<meta name="VIcurrentDateTime" content="638920761098002711">
<meta name="VirtualFolder" content="/">
<script type="text/javascript" src="/layouts/system/VisitorIdentification.js"></script>


<script>(window.BOOMR_mq=window.BOOMR_mq||[]).push(["addVar",{"rua.upush":"false","rua.cpush":"false","rua.upre":"false","rua.cpre":"false","rua.uprl":"false","rua.cprl":"false","rua.cprf":"false","rua.trans":"","rua.cook":"false","rua.ims":"false","rua.ufprl":"false","rua.cfprl":"false","rua.isuxp":"false","rua.texp":"norulematch","rua.ceh":"false","rua.ueh":"false","rua.ieh.st":"0"}]);</script>
                              <script>!function(e){var n="https://s.go-mpulse.net/boomerang/";if("False"=="True")e.BOOMR_config=e.BOOMR_config||{},e.BOOMR_config.PageParams=e.BOOMR_config.PageParams||{},e.BOOMR_config.PageParams.pci=!0,n="https://s2.go-mpulse.net/boomerang/";if(window.BOOMR_API_key="Q3Z5L-LDXFN-XQVXZ-6VBTA-NUYWW",function(){function e(){if(!o){var e=document.createElement("script");e.id="boomr-scr-as",e.src=window.BOOMR.url,e.async=!0,i.parentNode.appendChild(e),o=!0}}function t(e){o=!0;var n,t,a,r,d=document,O=window;if(window.BOOMR.snippetMethod=e?"if":"i",t=function(e,n){var t=d.createElement("script");t.id=n||"boomr-if-as",t.src=window.BOOMR.url,BOOMR_lstart=(new Date).getTime(),e=e||d.body,e.appendChild(t)},!window.addEventListener&&window.attachEvent&&navigator.userAgent.match(/MSIE [67]\./))return window.BOOMR.snippetMethod="s",void t(i.parentNode,"boomr-async");a=document.createElement("IFRAME"),a.src="about:blank",a.title="",a.role="presentation",a.loading="eager",r=(a.frameElement||a).style,r.width=0,r.height=0,r.border=0,r.display="none",i.parentNode.appendChild(a);try{O=a.contentWindow,d=O.document.open()}catch(_){n=document.domain,a.src="javascript:var d=document.open();d.domain='"+n+"';void(0);",O=a.contentWindow,d=O.document.open()}if(n)d._boomrl=function(){this.domain=n,t()},d.write("<bo"+"dy onload='document._boomrl();'>");else if(O._boomrl=function(){t()},O.addEventListener)O.addEventListener("load",O._boomrl,!1);else if(O.attachEvent)O.attachEvent("onload",O._boomrl);d.close()}function a(e){window.BOOMR_onload=e&&e.timeStamp||(new Date).getTime()}if(!window.BOOMR||!window.BOOMR.version&&!window.BOOMR.snippetExecuted){window.BOOMR=window.BOOMR||{},window.BOOMR.snippetStart=(new Date).getTime(),window.BOOMR.snippetExecuted=!0,window.BOOMR.snippetVersion=12,window.BOOMR.url=n+"Q3Z5L-LDXFN-XQVXZ-6VBTA-NUYWW";var i=document.currentScript||document.getElementsByTagName("script")[0],o=!1,r=document.createElement("link");if(r.relList&&"function"==typeof r.relList.supports&&r.relList.supports("preload")&&"as"in r)window.BOOMR.snippetMethod="p",r.href=window.BOOMR.url,r.rel="preload",r.as="script",r.addEventListener("load",e),r.addEventListener("error",function(){t(!0)}),setTimeout(function(){if(!o)t(!0)},3e3),BOOMR_lstart=(new Date).getTime(),i.parentNode.appendChild(r);else t(!1);if(window.addEventListener)window.addEventListener("load",a,!1);else if(window.attachEvent)window.attachEvent("onload",a)}}(),"".length>0)if(e&&"performance"in e&&e.performance&&"function"==typeof e.performance.setResourceTimingBufferSize)e.performance.setResourceTimingBufferSize();!function(){if(BOOMR=e.BOOMR||{},BOOMR.plugins=BOOMR.plugins||{},!BOOMR.plugins.AK){var n=""=="true"?1:0,t="",a="tiglk7ax32c6a2frx5gq-f-f84015285-clientnsv4-s.akamaihd.net",i="false"=="true"?2:1,o={"ak.v":"39","ak.cp":"439412","ak.ai":parseInt("795868",10),"ak.ol":"0","ak.cr":89,"ak.ipv":4,"ak.proto":"h2","ak.rid":"1e6a91b8","ak.r":47847,"ak.a2":n,"ak.m":"a","ak.n":"essl","ak.bpcip":"************","ak.cport":55560,"ak.gh":"************","ak.quicv":"","ak.tlsv":"tls1.3","ak.0rtt":"","ak.0rtt.ed":"","ak.csrc":"-","ak.acc":"","ak.t":"1756479309","ak.ak":"hOBiQwZUYzCg5VSAfCLimQ==0uCpSjPI0S13pR5OS6vhy9AlOaFmdZYVYlVlRX35/0qtIImb7d2p9YYNPkWvp58dkEmmFoY8GtumznmdiBFL1eAAREJ/UX+mBhUCcwJjGXWn1AFVy8qJfIaru0egrgXnmJFGMog5yyxhEl5NQhzfmORaIprkH8+0hVPrL+/goBzzfocaxud3mL6AP1WgEovL2B0ogswKfPJUuLxDNjyoI+Qmt7h4F0i8xDpJBfz09imD+EySw6Y6LsiPYV9mR056i3LyeYTZjG4oMN+GUEEnv5g7DKJo94nj6ZusJe/toPIxFheYpolMcVBN6uCMMmUmc3tRtN0QjDNZIr96sUF//EjeZYHqNE7DxQQShfyb3jSLq5lC05yygiHxZfO7QcGKb2Uk6y/R4cUZjq5yTzxzItwGGIT5PMpZomhBtNKd+5k=","ak.pv":"33","ak.dpoabenc":"","ak.tf":i};if(""!==t)o["ak.ruds"]=t;var r={i:!1,av:function(n){var t="http.initiator";if(n&&(!n[t]||"spa_hard"===n[t]))o["ak.feo"]=void 0!==e.aFeoApplied?1:0,BOOMR.addVar(o)},rv:function(){var e=["ak.bpcip","ak.cport","ak.cr","ak.csrc","ak.gh","ak.ipv","ak.m","ak.n","ak.ol","ak.proto","ak.quicv","ak.tlsv","ak.0rtt","ak.0rtt.ed","ak.r","ak.acc","ak.t","ak.tf"];BOOMR.removeVar(e)}};BOOMR.plugins.AK={akVars:o,akDNSPreFetchDomain:a,init:function(){if(!r.i){var e=BOOMR.subscribe;e("before_beacon",r.av,null,null),e("onbeacon",r.rv,null,null),r.i=!0}return this},is_complete:function(){return!0}}}}()}(window);</script><link href="https://s.go-mpulse.net/boomerang/Q3Z5L-LDXFN-XQVXZ-6VBTA-NUYWW" rel="preload" as="script"><script id="boomr-scr-as" src="https://s.go-mpulse.net/boomerang/Q3Z5L-LDXFN-XQVXZ-6VBTA-NUYWW" async=""></script><script src="https://cdn.cookielaw.org/scripttemplates/202408.1.0/otBannerSdk.js" async="" type="text/javascript"></script><style id="onetrust-style">#onetrust-banner-sdk .onetrust-vendors-list-handler{cursor:pointer;color:#1f96db;font-size:inherit;font-weight:bold;text-decoration:none;margin-left:5px}#onetrust-banner-sdk .onetrust-vendors-list-handler:hover{color:#1f96db}#onetrust-banner-sdk:focus{outline:2px solid #000;outline-offset:-2px}#onetrust-banner-sdk a:focus{outline:2px solid #000}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{outline-offset:1px}#onetrust-banner-sdk.ot-bnr-w-logo .ot-bnr-logo{height:64px;width:64px}#onetrust-banner-sdk .ot-tcf2-vendor-count.ot-text-bold{font-weight:bold}#onetrust-banner-sdk .ot-close-icon,#onetrust-pc-sdk .ot-close-icon,#ot-sync-ntfy .ot-close-icon{background-size:contain;background-repeat:no-repeat;background-position:center;height:12px;width:12px}#onetrust-banner-sdk .powered-by-logo,#onetrust-banner-sdk .ot-pc-footer-logo a,#onetrust-pc-sdk .powered-by-logo,#onetrust-pc-sdk .ot-pc-footer-logo a,#ot-sync-ntfy .powered-by-logo,#ot-sync-ntfy .ot-pc-footer-logo a{background-size:contain;background-repeat:no-repeat;background-position:center;height:25px;width:152px;display:block;text-decoration:none;font-size:.75em}#onetrust-banner-sdk .powered-by-logo:hover,#onetrust-banner-sdk .ot-pc-footer-logo a:hover,#onetrust-pc-sdk .powered-by-logo:hover,#onetrust-pc-sdk .ot-pc-footer-logo a:hover,#ot-sync-ntfy .powered-by-logo:hover,#ot-sync-ntfy .ot-pc-footer-logo a:hover{color:#565656}#onetrust-banner-sdk h3 *,#onetrust-banner-sdk h4 *,#onetrust-banner-sdk h6 *,#onetrust-banner-sdk button *,#onetrust-banner-sdk a[data-parent-id] *,#onetrust-pc-sdk h3 *,#onetrust-pc-sdk h4 *,#onetrust-pc-sdk h6 *,#onetrust-pc-sdk button *,#onetrust-pc-sdk a[data-parent-id] *,#ot-sync-ntfy h3 *,#ot-sync-ntfy h4 *,#ot-sync-ntfy h6 *,#ot-sync-ntfy button *,#ot-sync-ntfy a[data-parent-id] *{font-size:inherit;font-weight:inherit;color:inherit}#onetrust-banner-sdk .ot-hide,#onetrust-pc-sdk .ot-hide,#ot-sync-ntfy .ot-hide{display:none !important}#onetrust-banner-sdk button.ot-link-btn:hover,#onetrust-pc-sdk button.ot-link-btn:hover,#ot-sync-ntfy button.ot-link-btn:hover{text-decoration:underline;opacity:1}#onetrust-pc-sdk .ot-sdk-row .ot-sdk-column{padding:0}#onetrust-pc-sdk .ot-sdk-container{padding-right:0}#onetrust-pc-sdk .ot-sdk-row{flex-direction:initial;width:100%}#onetrust-pc-sdk [type=checkbox]:checked,#onetrust-pc-sdk [type=checkbox]:not(:checked){pointer-events:initial}#onetrust-pc-sdk [type=checkbox]:disabled+label::before,#onetrust-pc-sdk [type=checkbox]:disabled+label:after,#onetrust-pc-sdk [type=checkbox]:disabled+label{pointer-events:none;opacity:.7}#onetrust-pc-sdk #vendor-list-content{transform:translate3d(0, 0, 0)}#onetrust-pc-sdk li input[type=checkbox]{z-index:1}#onetrust-pc-sdk li .ot-checkbox label{z-index:2}#onetrust-pc-sdk li .ot-checkbox input[type=checkbox]{height:auto;width:auto}#onetrust-pc-sdk li .host-title a,#onetrust-pc-sdk li .ot-host-name a,#onetrust-pc-sdk li .accordion-text,#onetrust-pc-sdk li .ot-acc-txt{z-index:2;position:relative}#onetrust-pc-sdk input{margin:3px .1ex}#onetrust-pc-sdk .pc-logo,#onetrust-pc-sdk .ot-pc-logo{height:60px;width:180px;background-position:center;background-size:contain;background-repeat:no-repeat;display:inline-flex;justify-content:center;align-items:center}#onetrust-pc-sdk .pc-logo img,#onetrust-pc-sdk .ot-pc-logo img{max-height:100%;max-width:100%}#onetrust-pc-sdk .screen-reader-only,#onetrust-pc-sdk .ot-scrn-rdr,.ot-sdk-cookie-policy .screen-reader-only,.ot-sdk-cookie-policy .ot-scrn-rdr{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}#onetrust-pc-sdk.ot-fade-in,.onetrust-pc-dark-filter.ot-fade-in,#onetrust-banner-sdk.ot-fade-in{animation-name:onetrust-fade-in;animation-duration:400ms;animation-timing-function:ease-in-out}#onetrust-pc-sdk.ot-hide{display:none !important}.onetrust-pc-dark-filter.ot-hide{display:none !important}#ot-sdk-btn.ot-sdk-show-settings,#ot-sdk-btn.optanon-show-settings{color:#68b631;border:1px solid #68b631;height:auto;white-space:normal;word-wrap:break-word;padding:.8em 2em;font-size:.8em;line-height:1.2;cursor:pointer;-moz-transition:.1s ease;-o-transition:.1s ease;-webkit-transition:1s ease;transition:.1s ease}#ot-sdk-btn.ot-sdk-show-settings:hover,#ot-sdk-btn.optanon-show-settings:hover{color:#fff;background-color:#68b631}.onetrust-pc-dark-filter{background:rgba(0,0,0,.5);z-index:2147483646;width:100%;height:100%;overflow:hidden;position:fixed;top:0;bottom:0;left:0}@keyframes onetrust-fade-in{0%{opacity:0}100%{opacity:1}}.ot-cookie-label{text-decoration:underline}@media only screen and (min-width: 426px)and (max-width: 896px)and (orientation: landscape){#onetrust-pc-sdk p{font-size:.75em}}#onetrust-banner-sdk .banner-option-input:focus+label{outline:1px solid #000;outline-style:auto}.category-vendors-list-handler+a:focus,.category-vendors-list-handler+a:focus-visible{outline:2px solid #000}#onetrust-pc-sdk .ot-userid-title{margin-top:10px}#onetrust-pc-sdk .ot-userid-title>span,#onetrust-pc-sdk .ot-userid-timestamp>span{font-weight:700}#onetrust-pc-sdk .ot-userid-desc{font-style:italic}#onetrust-pc-sdk .ot-host-desc a{pointer-events:initial}#onetrust-pc-sdk .ot-ven-hdr>p a{position:relative;z-index:2;pointer-events:initial}#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info a{margin-right:auto}#onetrust-pc-sdk .ot-pc-footer-logo img{width:136px;height:16px}#onetrust-pc-sdk .ot-pur-vdr-count{font-weight:400;font-size:.7rem;padding-top:3px;display:block}#onetrust-banner-sdk .ot-optout-signal,#onetrust-pc-sdk .ot-optout-signal{border:1px solid #32ae88;border-radius:3px;padding:5px;margin-bottom:10px;background-color:#f9fffa;font-size:.85rem;line-height:2}#onetrust-banner-sdk .ot-optout-signal .ot-optout-icon,#onetrust-pc-sdk .ot-optout-signal .ot-optout-icon{display:inline;margin-right:5px}#onetrust-banner-sdk .ot-optout-signal svg,#onetrust-pc-sdk .ot-optout-signal svg{height:20px;width:30px;transform:scale(0.5)}#onetrust-banner-sdk .ot-optout-signal svg path,#onetrust-pc-sdk .ot-optout-signal svg path{fill:#32ae88}#onetrust-consent-sdk .ot-general-modal{overflow:hidden;position:fixed;margin:0 auto;top:50%;left:50%;width:40%;padding:1.5rem;max-width:575px;min-width:575px;z-index:**********;border-radius:2.5px;transform:translate(-50%, -50%)}#onetrust-consent-sdk .ot-signature-health-group{margin-top:1rem;padding-left:1.25rem;padding-right:1.25rem;margin-bottom:.625rem;width:calc(100% - 2.5rem)}#onetrust-consent-sdk .ot-signature-health-group .ot-signature-health-form{gap:.5rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-health-form{width:70%;gap:.35rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-input{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #d1d1d1;border-radius:4px;box-shadow:none;box-sizing:border-box}#onetrust-consent-sdk .ot-signature-health .ot-signature-subtitle{font-size:1.125rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-group-title{font-size:1.25rem;font-weight:bold}#onetrust-consent-sdk .ot-signature-health,#onetrust-consent-sdk .ot-signature-health-group{display:flex;flex-direction:column;gap:1rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-cont{display:flex;flex-direction:column;gap:.25rem}#onetrust-consent-sdk .ot-signature-health .ot-signature-paragraph,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-paragraph{margin:0;line-height:20px;font-size:max(14px,.875rem)}#onetrust-consent-sdk .ot-signature-health .ot-health-signature-error,#onetrust-consent-sdk .ot-signature-health-group .ot-health-signature-error{color:#4d4d4d;font-size:min(12px,.75rem)}#onetrust-consent-sdk .ot-signature-health .ot-signature-buttons-cont,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-buttons-cont{margin-top:max(.75rem,2%);gap:1rem;display:flex;justify-content:flex-end}#onetrust-consent-sdk .ot-signature-health .ot-signature-button,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button{flex:1;height:auto;color:#fff;cursor:pointer;line-height:1.2;min-width:125px;font-weight:600;font-size:.813em;border-radius:2px;padding:12px 10px;white-space:normal;word-wrap:break-word;word-break:break-word;background-color:#68b631;border:2px solid #68b631}#onetrust-consent-sdk .ot-signature-health .ot-signature-button.reject,#onetrust-consent-sdk .ot-signature-health-group .ot-signature-button.reject{background-color:#fff}#onetrust-consent-sdk .ot-input-field-cont{display:flex;flex-direction:column;gap:.5rem}#onetrust-consent-sdk .ot-input-field-cont .ot-signature-input{width:65%}#onetrust-consent-sdk .ot-signature-health-form{display:flex;flex-direction:column}#onetrust-consent-sdk .ot-signature-health-form .ot-signature-label{margin-bottom:0;line-height:20px;font-size:max(14px,.875rem)}@media only screen and (max-width: 600px){#onetrust-consent-sdk .ot-general-modal{min-width:100%}#onetrust-consent-sdk .ot-signature-health .ot-signature-health-form{width:100%}#onetrust-consent-sdk .ot-input-field-cont .ot-signature-input{width:100%}}#onetrust-banner-sdk,#onetrust-pc-sdk,#ot-sdk-cookie-policy,#ot-sync-ntfy{font-size:16px}#onetrust-banner-sdk *,#onetrust-banner-sdk ::after,#onetrust-banner-sdk ::before,#onetrust-pc-sdk *,#onetrust-pc-sdk ::after,#onetrust-pc-sdk ::before,#ot-sdk-cookie-policy *,#ot-sdk-cookie-policy ::after,#ot-sdk-cookie-policy ::before,#ot-sync-ntfy *,#ot-sync-ntfy ::after,#ot-sync-ntfy ::before{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}#onetrust-banner-sdk div,#onetrust-banner-sdk span,#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-banner-sdk p,#onetrust-banner-sdk img,#onetrust-banner-sdk svg,#onetrust-banner-sdk button,#onetrust-banner-sdk section,#onetrust-banner-sdk a,#onetrust-banner-sdk label,#onetrust-banner-sdk input,#onetrust-banner-sdk ul,#onetrust-banner-sdk li,#onetrust-banner-sdk nav,#onetrust-banner-sdk table,#onetrust-banner-sdk thead,#onetrust-banner-sdk tr,#onetrust-banner-sdk td,#onetrust-banner-sdk tbody,#onetrust-banner-sdk .ot-main-content,#onetrust-banner-sdk .ot-toggle,#onetrust-banner-sdk #ot-content,#onetrust-banner-sdk #ot-pc-content,#onetrust-banner-sdk .checkbox,#onetrust-pc-sdk div,#onetrust-pc-sdk span,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#onetrust-pc-sdk p,#onetrust-pc-sdk img,#onetrust-pc-sdk svg,#onetrust-pc-sdk button,#onetrust-pc-sdk section,#onetrust-pc-sdk a,#onetrust-pc-sdk label,#onetrust-pc-sdk input,#onetrust-pc-sdk ul,#onetrust-pc-sdk li,#onetrust-pc-sdk nav,#onetrust-pc-sdk table,#onetrust-pc-sdk thead,#onetrust-pc-sdk tr,#onetrust-pc-sdk td,#onetrust-pc-sdk tbody,#onetrust-pc-sdk .ot-main-content,#onetrust-pc-sdk .ot-toggle,#onetrust-pc-sdk #ot-content,#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk .checkbox,#ot-sdk-cookie-policy div,#ot-sdk-cookie-policy span,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy p,#ot-sdk-cookie-policy img,#ot-sdk-cookie-policy svg,#ot-sdk-cookie-policy button,#ot-sdk-cookie-policy section,#ot-sdk-cookie-policy a,#ot-sdk-cookie-policy label,#ot-sdk-cookie-policy input,#ot-sdk-cookie-policy ul,#ot-sdk-cookie-policy li,#ot-sdk-cookie-policy nav,#ot-sdk-cookie-policy table,#ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy tr,#ot-sdk-cookie-policy td,#ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy .ot-main-content,#ot-sdk-cookie-policy .ot-toggle,#ot-sdk-cookie-policy #ot-content,#ot-sdk-cookie-policy #ot-pc-content,#ot-sdk-cookie-policy .checkbox,#ot-sync-ntfy div,#ot-sync-ntfy span,#ot-sync-ntfy h1,#ot-sync-ntfy h2,#ot-sync-ntfy h3,#ot-sync-ntfy h4,#ot-sync-ntfy h5,#ot-sync-ntfy h6,#ot-sync-ntfy p,#ot-sync-ntfy img,#ot-sync-ntfy svg,#ot-sync-ntfy button,#ot-sync-ntfy section,#ot-sync-ntfy a,#ot-sync-ntfy label,#ot-sync-ntfy input,#ot-sync-ntfy ul,#ot-sync-ntfy li,#ot-sync-ntfy nav,#ot-sync-ntfy table,#ot-sync-ntfy thead,#ot-sync-ntfy tr,#ot-sync-ntfy td,#ot-sync-ntfy tbody,#ot-sync-ntfy .ot-main-content,#ot-sync-ntfy .ot-toggle,#ot-sync-ntfy #ot-content,#ot-sync-ntfy #ot-pc-content,#ot-sync-ntfy .checkbox{font-family:inherit;font-weight:normal;-webkit-font-smoothing:auto;letter-spacing:normal;line-height:normal;padding:0;margin:0;height:auto;min-height:0;max-height:none;width:auto;min-width:0;max-width:none;border-radius:0;border:none;clear:none;float:none;position:static;bottom:auto;left:auto;right:auto;top:auto;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;white-space:normal;background:none;overflow:visible;vertical-align:baseline;visibility:visible;z-index:auto;box-shadow:none}#onetrust-banner-sdk label:before,#onetrust-banner-sdk label:after,#onetrust-banner-sdk .checkbox:after,#onetrust-banner-sdk .checkbox:before,#onetrust-pc-sdk label:before,#onetrust-pc-sdk label:after,#onetrust-pc-sdk .checkbox:after,#onetrust-pc-sdk .checkbox:before,#ot-sdk-cookie-policy label:before,#ot-sdk-cookie-policy label:after,#ot-sdk-cookie-policy .checkbox:after,#ot-sdk-cookie-policy .checkbox:before,#ot-sync-ntfy label:before,#ot-sync-ntfy label:after,#ot-sync-ntfy .checkbox:after,#ot-sync-ntfy .checkbox:before{content:"";content:none}#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{position:relative;width:100%;max-width:100%;margin:0 auto;padding:0 20px;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{width:100%;float:left;box-sizing:border-box;padding:0;display:initial}@media(min-width: 400px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:90%;padding:0}}@media(min-width: 550px){#onetrust-banner-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container,#ot-sdk-cookie-policy .ot-sdk-container{width:100%}#onetrust-banner-sdk .ot-sdk-column,#onetrust-banner-sdk .ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-column,#onetrust-pc-sdk .ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-column,#ot-sdk-cookie-policy .ot-sdk-columns{margin-left:4%}#onetrust-banner-sdk .ot-sdk-column:first-child,#onetrust-banner-sdk .ot-sdk-columns:first-child,#onetrust-pc-sdk .ot-sdk-column:first-child,#onetrust-pc-sdk .ot-sdk-columns:first-child,#ot-sdk-cookie-policy .ot-sdk-column:first-child,#ot-sdk-cookie-policy .ot-sdk-columns:first-child{margin-left:0}#onetrust-banner-sdk .ot-sdk-two.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-two.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-two.ot-sdk-columns{width:13.3333333333%}#onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-three.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-three.ot-sdk-columns{width:22%}#onetrust-banner-sdk .ot-sdk-four.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-four.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-four.ot-sdk-columns{width:30.6666666667%}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eight.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eight.ot-sdk-columns{width:65.3333333333%}#onetrust-banner-sdk .ot-sdk-nine.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-nine.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-nine.ot-sdk-columns{width:74%}#onetrust-banner-sdk .ot-sdk-ten.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-ten.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-ten.ot-sdk-columns{width:82.6666666667%}#onetrust-banner-sdk .ot-sdk-eleven.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-eleven.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-eleven.ot-sdk-columns{width:91.3333333333%}#onetrust-banner-sdk .ot-sdk-twelve.ot-sdk-columns,#onetrust-pc-sdk .ot-sdk-twelve.ot-sdk-columns,#ot-sdk-cookie-policy .ot-sdk-twelve.ot-sdk-columns{width:100%;margin-left:0}}#onetrust-banner-sdk h1,#onetrust-banner-sdk h2,#onetrust-banner-sdk h3,#onetrust-banner-sdk h4,#onetrust-banner-sdk h5,#onetrust-banner-sdk h6,#onetrust-pc-sdk h1,#onetrust-pc-sdk h2,#onetrust-pc-sdk h3,#onetrust-pc-sdk h4,#onetrust-pc-sdk h5,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h1,#ot-sdk-cookie-policy h2,#ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy h5,#ot-sdk-cookie-policy h6{margin-top:0;font-weight:600;font-family:inherit}#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem;line-height:1.2}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem;line-height:1.25}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem;line-height:1.3}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem;line-height:1.35}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem;line-height:1.5}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem;line-height:1.6}@media(min-width: 550px){#onetrust-banner-sdk h1,#onetrust-pc-sdk h1,#ot-sdk-cookie-policy h1{font-size:1.5rem}#onetrust-banner-sdk h2,#onetrust-pc-sdk h2,#ot-sdk-cookie-policy h2{font-size:1.5rem}#onetrust-banner-sdk h3,#onetrust-pc-sdk h3,#ot-sdk-cookie-policy h3{font-size:1.5rem}#onetrust-banner-sdk h4,#onetrust-pc-sdk h4,#ot-sdk-cookie-policy h4{font-size:1.5rem}#onetrust-banner-sdk h5,#onetrust-pc-sdk h5,#ot-sdk-cookie-policy h5{font-size:1.5rem}#onetrust-banner-sdk h6,#onetrust-pc-sdk h6,#ot-sdk-cookie-policy h6{font-size:1.5rem}}#onetrust-banner-sdk p,#onetrust-pc-sdk p,#ot-sdk-cookie-policy p{margin:0 0 1em 0;font-family:inherit;line-height:normal}#onetrust-banner-sdk a,#onetrust-pc-sdk a,#ot-sdk-cookie-policy a{color:#565656;text-decoration:underline}#onetrust-banner-sdk a:hover,#onetrust-pc-sdk a:hover,#ot-sdk-cookie-policy a:hover{color:#565656;text-decoration:none}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-button,#onetrust-banner-sdk button,#onetrust-pc-sdk .ot-sdk-button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy .ot-sdk-button,#ot-sdk-cookie-policy button{display:inline-block;height:38px;padding:0 30px;color:#555;text-align:center;font-size:.9em;font-weight:400;line-height:38px;letter-spacing:.01em;text-decoration:none;white-space:nowrap;background-color:rgba(0,0,0,0);border-radius:2px;border:1px solid #bbb;cursor:pointer;box-sizing:border-box}#onetrust-banner-sdk .ot-sdk-button:hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#onetrust-pc-sdk .ot-sdk-button:hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus,#ot-sdk-cookie-policy .ot-sdk-button:hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):hover,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:not(.ot-link-btn):focus{color:#333;border-color:#888;opacity:.7}#onetrust-banner-sdk .ot-sdk-button:focus,#onetrust-banner-sdk :not(.ot-leg-btn-container)>button:focus,#onetrust-pc-sdk .ot-sdk-button:focus,#onetrust-pc-sdk :not(.ot-leg-btn-container)>button:focus,#ot-sdk-cookie-policy .ot-sdk-button:focus,#ot-sdk-cookie-policy :not(.ot-leg-btn-container)>button:focus{outline:2px solid #000}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-banner-sdk button.ot-sdk-button-primary,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary,#onetrust-pc-sdk button.ot-sdk-button-primary,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary,#ot-sdk-cookie-policy button.ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary{color:#fff;background-color:#33c3f0;border-color:#33c3f0}#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-banner-sdk button.ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-banner-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-banner-sdk button.ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-banner-sdk input[type=button].ot-sdk-button-primary:focus,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:hover,#onetrust-pc-sdk button.ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:hover,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:hover,#onetrust-pc-sdk .ot-sdk-button.ot-sdk-button-primary:focus,#onetrust-pc-sdk button.ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=submit].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=reset].ot-sdk-button-primary:focus,#onetrust-pc-sdk input[type=button].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy button.ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:hover,#ot-sdk-cookie-policy .ot-sdk-button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy button.ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=submit].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=reset].ot-sdk-button-primary:focus,#ot-sdk-cookie-policy input[type=button].ot-sdk-button-primary:focus{color:#fff;background-color:#1eaedb;border-color:#1eaedb}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{height:38px;padding:6px 10px;background-color:#fff;border:1px solid #d1d1d1;border-radius:4px;box-shadow:none;box-sizing:border-box}#onetrust-banner-sdk input[type=text],#onetrust-pc-sdk input[type=text],#ot-sdk-cookie-policy input[type=text]{-webkit-appearance:none;-moz-appearance:none;appearance:none}#onetrust-banner-sdk input[type=text]:focus,#onetrust-pc-sdk input[type=text]:focus,#ot-sdk-cookie-policy input[type=text]:focus{border:1px solid #000;outline:0}#onetrust-banner-sdk label,#onetrust-pc-sdk label,#ot-sdk-cookie-policy label{display:block;margin-bottom:.5rem;font-weight:600}#onetrust-banner-sdk input[type=checkbox],#onetrust-pc-sdk input[type=checkbox],#ot-sdk-cookie-policy input[type=checkbox]{display:inline}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{list-style:circle inside}#onetrust-banner-sdk ul,#onetrust-pc-sdk ul,#ot-sdk-cookie-policy ul{padding-left:0;margin-top:0}#onetrust-banner-sdk ul ul,#onetrust-pc-sdk ul ul,#ot-sdk-cookie-policy ul ul{margin:1.5rem 0 1.5rem 3rem;font-size:90%}#onetrust-banner-sdk li,#onetrust-pc-sdk li,#ot-sdk-cookie-policy li{margin-bottom:1rem}#onetrust-banner-sdk th,#onetrust-banner-sdk td,#onetrust-pc-sdk th,#onetrust-pc-sdk td,#ot-sdk-cookie-policy th,#ot-sdk-cookie-policy td{padding:12px 15px;text-align:left;border-bottom:1px solid #e1e1e1}#onetrust-banner-sdk button,#onetrust-pc-sdk button,#ot-sdk-cookie-policy button{margin-bottom:1rem;font-family:inherit}#onetrust-banner-sdk .ot-sdk-container:after,#onetrust-banner-sdk .ot-sdk-row:after,#onetrust-pc-sdk .ot-sdk-container:after,#onetrust-pc-sdk .ot-sdk-row:after,#ot-sdk-cookie-policy .ot-sdk-container:after,#ot-sdk-cookie-policy .ot-sdk-row:after{content:"";display:table;clear:both}#onetrust-banner-sdk .ot-sdk-row,#onetrust-pc-sdk .ot-sdk-row,#ot-sdk-cookie-policy .ot-sdk-row{margin:0;max-width:none;display:block}#onetrust-banner-sdk.otFloatingRoundedIcon{position:fixed;bottom:2em;right:0;left:5em;z-index:2147483645;background-color:#fff;width:75%;border-radius:6.5px;padding:20px 0;-webkit-box-shadow:0 0 18px rgba(0,0,0,.2);-moz-box-shadow:0 0 18px rgba(0,0,0,.2);box-shadow:0 0 18px rgba(0,0,0,.2);display:table}#onetrust-banner-sdk.otFloatingRoundedIcon.otRelFont{font-size:1rem}#onetrust-banner-sdk .banner-content{overflow-x:hidden;overflow-y:auto;max-height:20rem}#onetrust-banner-sdk .banner-content::-webkit-scrollbar{width:11px}#onetrust-banner-sdk .banner-content::-webkit-scrollbar-thumb{border-radius:10px;background:#c1c1c1}#onetrust-banner-sdk .banner-content{scrollbar-arrow-color:#c1c1c1;scrollbar-darkshadow-color:#c1c1c1;scrollbar-face-color:#c1c1c1;scrollbar-shadow-color:#c1c1c1}#onetrust-banner-sdk h3{margin-bottom:0;font-size:1.7em}#onetrust-banner-sdk #onetrust-policy{margin-left:50px}#onetrust-banner-sdk #onetrust-policy .ot-gv-list-handler{float:left;font-size:.812em;padding:0;margin-bottom:0;border:0;height:auto;line-height:normal}#onetrust-banner-sdk #onetrust-button-group-parent{text-align:center}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler{background-color:#68b631;color:#fff;border-color:#68b631;margin-bottom:.6rem}#onetrust-banner-sdk #onetrust-pc-btn-handler{background-color:#68b631;color:#fff;border:1px solid #68b631;padding:0}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{border-radius:50px;font-weight:600;letter-spacing:.05em;min-width:160px;height:auto;white-space:normal;word-break:break-word;word-wrap:break-word;padding:10px 10px;line-height:1.6;-webkit-box-shadow:0px 2px 10px -3px #999;-moz-box-shadow:0px 2px 10px -3px #999;box-shadow:0px 2px 10px -3px #999}#onetrust-banner-sdk #onetrust-pc-btn-handler.cookie-setting-link{background-color:#fff;border:none;color:#68b631;text-decoration:underline;padding-left:0;padding-right:0;box-shadow:none}#onetrust-banner-sdk .onetrust-close-btn-ui{float:right;width:44px;height:44px;background-size:12px;margin:-5px 0 0 0;border:none;padding:0}#onetrust-banner-sdk #onetrust-close-btn-container{position:absolute;top:12px;right:0}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk .ot-b-addl-desc{clear:both;float:left;margin-bottom:0;padding-bottom:10px;font-size:.8em;line-height:1.5}#onetrust-banner-sdk #onetrust-policy-text>*,#onetrust-banner-sdk .ot-b-addl-desc>*{font-size:inherit}#onetrust-banner-sdk #onetrust-policy-text a,#onetrust-banner-sdk .ot-b-addl-desc a{font-weight:600;margin-left:5px}#onetrust-banner-sdk .ot-b-addl-desc{display:block}#onetrust-banner-sdk .ot-dpd-desc>.ot-b-addl-desc{padding:0;width:100%;font-size:1em;line-height:1.5;margin-top:10px;margin-bottom:10px}#onetrust-banner-sdk #onetrust-policy-title{float:left;text-align:left;font-size:1em;line-height:1.3;font-weight:600;margin-bottom:10px;color:#2c3643}#onetrust-banner-sdk #onetrust-policy-title a{color:#6c7985}#onetrust-banner-sdk #onetrust-policy-text a{font-weight:bold}#onetrust-banner-sdk #onetrust-cookie-btn{display:block;border-radius:50%;height:64px;width:64px;cursor:pointer;padding:0;margin-bottom:0;border:0}#onetrust-banner-sdk #onetrust-cookie-btn:focus,#onetrust-banner-sdk #onetrust-cookie-btn:hover{opacity:.7}#onetrust-banner-sdk #onetrust-cookie-btn-container{position:absolute;float:left;z-index:1;left:-35px;top:53%;transform:translateY(-50%)}#onetrust-banner-sdk .banner_logo{display:none}#onetrust-banner-sdk.ot-bnr-w-logo #onetrust-cookie-btn{border-radius:0}#onetrust-banner-sdk #banner-options{float:left;margin-left:50px;margin-top:10px;margin-bottom:1em}#onetrust-banner-sdk .banner-option-input{cursor:pointer;width:auto;height:auto;border:none;padding:0;padding-right:3px;margin-bottom:6px;margin-left:20px;font-size:.82em;line-height:1.4}#onetrust-banner-sdk .banner-option-input *{pointer-events:none;font-size:inherit;line-height:inherit}#onetrust-banner-sdk .banner-option-input[aria-expanded=true] .ot-arrow-container{transform:rotate(90deg)}#onetrust-banner-sdk #purpose-option[aria-expanded=true]~.banner-option-details.purpose-option,#onetrust-banner-sdk #feature-option[aria-expanded=true]~.banner-option-details.feature-option,#onetrust-banner-sdk #information-option[aria-expanded=true]~.banner-option-details.information-option{display:block;height:auto}#onetrust-banner-sdk .banner-option-header{cursor:pointer;display:inline-block}#onetrust-banner-sdk .banner-option-header :first-child{color:dimgray;font-weight:bold;float:left}#onetrust-banner-sdk .banner-option-header .ot-arrow-container{display:inline-block;border-top:6px solid rgba(0,0,0,0);border-bottom:6px solid rgba(0,0,0,0);border-left:6px solid dimgray;margin-left:10px;vertical-align:middle;transition:all 300ms ease-in 0s;-webkit-transition:all 300ms ease-in 0s;-moz-transition:all 300ms ease-in 0s;-o-transition:all 300ms ease-in 0s}#onetrust-banner-sdk .banner-option-details{display:none;font-size:.83em;line-height:1.5;height:0px;padding:10px 10px 5px 10px;transition:all 300ms ease-in 0s;-webkit-transition:all 300ms ease-in 0s;-moz-transition:all 300ms ease-in 0s;-o-transition:all 300ms ease-in 0s}#onetrust-banner-sdk .banner-option-details *{font-size:inherit;line-height:inherit;color:dimgray}#onetrust-banner-sdk .ot-dpd-container{float:left;clear:both}#onetrust-banner-sdk .ot-dpd-container .ot-dpd-title{font-weight:600;padding-bottom:8px}#onetrust-banner-sdk .ot-dpd-container .ot-dpd-desc,#onetrust-banner-sdk .ot-dpd-container .ot-dpd-title{font-size:.813em;line-height:1.5}#onetrust-banner-sdk .ot-dpd-container .ot-dpd-desc .onetrust-vendors-list-handler{display:block;margin-left:0px;margin-top:5px;clear:both;padding:0;margin-bottom:0;border:0;line-height:normal;height:auto;width:auto}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container{position:static;margin:0;text-align:center}#onetrust-banner-sdk.ot-close-btn-link #onetrust-close-btn-container button{white-space:pre-wrap;border:none;height:auto;line-height:1.5;text-decoration:underline;font-size:.69em;min-width:175px;float:none;padding:0}#onetrust-banner-sdk.ot-close-btn-link.ot-wo-title #onetrust-group-container{margin-top:20px}@media(min-width: 550px){#onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns{width:15%}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns{width:74.333333%}}@media only screen and (min-width: 426px)and (max-width: 896px){#onetrust-banner-sdk{padding:20px 0 10px 0}#onetrust-banner-sdk .ot-optout-signal{margin-top:1.875rem}#onetrust-banner-sdk #onetrust-policy,#onetrust-banner-sdk #onetrust-button-group-parent{margin-left:0}#onetrust-banner-sdk .ot-sdk-container .ot-sdk-row{margin:0 15px 0 15px}#onetrust-banner-sdk #onetrust-cookie-btn-container{display:none}#onetrust-banner-sdk #onetrust-accept-btn-container,#onetrust-banner-sdk #onetrust-reject-btn-container,#onetrust-banner-sdk #onetrust-pc-btn-container{display:inline-block;margin-top:15px}#onetrust-banner-sdk #onetrust-pc-btn-container{float:left;margin-right:1em}#onetrust-banner-sdk #onetrust-accept-btn-container{float:right}#onetrust-banner-sdk #onetrust-button-group{display:inline-block}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk .ot-b-addl-desc{font-size:.813em;width:94%}#onetrust-banner-sdk #onetrust-policy-text>*,#onetrust-banner-sdk .ot-b-addl-desc>*{font-size:inherit}#onetrust-banner-sdk #onetrust-accept-btn-container,#onetrust-banner-sdk #onetrust-reject-btn-container{margin-right:10px}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-banner-sdk .ot-sdk-three.ot-sdk-columns{width:100%}#onetrust-banner-sdk #banner-options{margin-left:1em}#onetrust-banner-sdk.otFloatingRoundedIcon{left:0;bottom:0;border-radius:0;width:100%;padding:20px 0 10px 0}}@media only screen and (min-width: 769px){#onetrust-banner-sdk.otFloatingRoundedIcon[dir=rtl]{right:20%}}@media only screen and (min-width: 897px){#onetrust-banner-sdk.vertical-align-content #onetrust-button-group-parent{position:absolute;top:50%;margin-top:10px;transform:translateY(-50%);width:20%;left:64%}#onetrust-banner-sdk #onetrust-group-container{width:64%}#onetrust-banner-sdk #banner-options{width:60%;margin:0;padding-left:50px}#onetrust-banner-sdk #onetrust-button-group-parent{width:20%;left:64%}}@media only screen and (min-width: 1024px){#onetrust-banner-sdk .banner-content{overflow-x:initial;overflow-y:auto;padding-bottom:20px;padding-top:20px;max-height:35vh}#onetrust-banner-sdk .ot-sdk-eight.ot-sdk-columns,#onetrust-banner-sdk #onetrust-group-container{width:64%}#onetrust-banner-sdk.vertical-align-content #onetrust-button-group-parent{width:20%;left:66%}#onetrust-banner-sdk #onetrust-button-group-parent{width:20%;left:66%}#onetrust-banner-sdk #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-pc-btn-handler{width:auto}#onetrust-banner-sdk #onetrust-accept-btn-container,#onetrust-banner-sdk #onetrust-reject-btn-container,#onetrust-banner-sdk #onetrust-pc-btn-container{text-align:center}#onetrust-banner-sdk:not(.vertical-align-content){padding:0}#onetrust-banner-sdk:not(.vertical-align-content) #onetrust-button-group-parent{margin-left:66%}#onetrust-banner-sdk:not(.vertical-align-content) #onetrust-group-container{position:absolute;top:50%;transform:translateY(-50%);left:0}#onetrust-banner-sdk:not(.vertical-align-content) #onetrust-close-btn-container{margin-top:20px}#onetrust-banner-sdk #onetrust-accept-btn-container{margin-top:1em}#onetrust-banner-sdk #onetrust-close-btn-container{margin-right:20px}#onetrust-banner-sdk.ot-close-btn-link #onetrust-button-group-parent{width:28%}#onetrust-banner-sdk.ot-close-btn-link #onetrust-accept-btn-container{margin-top:0}}@media only screen and (max-width: 768px){#onetrust-banner-sdk .banner-content{max-height:85vh}#onetrust-banner-sdk #banner-options{margin-left:0;padding:0;width:100%}#onetrust-banner-sdk #onetrust-button-group-parent:not(.has-reject-all-button) #onetrust-accept-btn-container{margin-right:0;width:100%}#onetrust-banner-sdk #onetrust-button-group-parent:not(.has-reject-all-button) #onetrust-accept-btn-container #onetrust-accept-btn-handler{width:100%}#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-accept-btn-container{margin-right:0;width:47%;float:right}#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-reject-btn-container{margin-right:0;width:47%}#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-reject-btn-container button{float:right}#onetrust-banner-sdk #onetrust-pc-btn-container{width:100%;text-align:center}}@media only screen and (max-width: 425px){#onetrust-banner-sdk .ot-optout-signal{margin-top:1.875rem}#onetrust-banner-sdk #onetrust-policy{margin-left:0;margin-right:.2em}#onetrust-banner-sdk #onetrust-policy-text,#onetrust-banner-sdk .ot-b-addl-desc{line-height:1.5;font-size:.813em;padding-top:2em}#onetrust-banner-sdk #onetrust-cookie-btn-container{display:none}#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-accept-btn-container,#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-reject-btn-container,#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-pc-btn-container,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-accept-btn-container,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-reject-btn-container,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-pc-btn-container{width:100%}#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-button-group-parent.has-reject-all-button #onetrust-pc-btn-handler,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-accept-btn-handler,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-reject-all-handler,#onetrust-banner-sdk #onetrust-button-group-parent #onetrust-pc-btn-handler{width:100%;letter-spacing:0;font-size:.813em}#onetrust-banner-sdk #onetrust-policy-title{display:inline-block;line-height:1;margin-bottom:10px;width:95%}#onetrust-banner-sdk #onetrust-button-group-parent{margin-top:20px}#onetrust-banner-sdk #banner-options{margin-left:0;padding:0;width:100%}#onetrust-banner-sdk .onetrust-close-btn-ui{margin:-15px}#onetrust-banner-sdk #onetrust-group-container{margin-top:20px}#onetrust-banner-sdk #onetrust-close-btn-container{top:22px;right:22px}#onetrust-banner-sdk.otFloatingRoundedIcon{left:0;bottom:0;padding-bottom:0;padding-top:0;width:100%;border-radius:0}}
        #onetrust-consent-sdk #onetrust-banner-sdk {background-color: #FFFFFF;}
            #onetrust-consent-sdk #onetrust-policy-title,
                    #onetrust-consent-sdk #onetrust-policy-text,
                    #onetrust-consent-sdk .ot-b-addl-desc,
                    #onetrust-consent-sdk .ot-dpd-desc,
                    #onetrust-consent-sdk .ot-dpd-title,
                    #onetrust-consent-sdk #onetrust-policy-text *:not(.onetrust-vendors-list-handler),
                    #onetrust-consent-sdk .ot-dpd-desc *:not(.onetrust-vendors-list-handler),
                    #onetrust-consent-sdk #onetrust-banner-sdk #banner-options *,
                    #onetrust-banner-sdk .ot-cat-header,
                    #onetrust-banner-sdk .ot-optout-signal
                    {
                        color: #000000;
                    }
            #onetrust-consent-sdk #onetrust-banner-sdk .banner-option-details {
                    background-color: #ffffff;}
             #onetrust-consent-sdk #onetrust-banner-sdk a[href],
                    #onetrust-consent-sdk #onetrust-banner-sdk a[href] font,
                    #onetrust-consent-sdk #onetrust-banner-sdk .ot-link-btn
                        {
                            color: #000000;
                        }#onetrust-consent-sdk #onetrust-accept-btn-handler,
                         #onetrust-banner-sdk #onetrust-reject-all-handler {
                            background-color: #ffffff;border-color: #ffffff;
                color: #000000;
            }
            #onetrust-consent-sdk #onetrust-banner-sdk *:focus,
            #onetrust-consent-sdk #onetrust-banner-sdk:focus {
               outline-color: #000000;
               outline-width: 1px;
            }
            #onetrust-consent-sdk #onetrust-pc-btn-handler,
            #onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
                color: #000000; border-color: #000000;
                background-color:
                #FFFFFF;
            }#onetrust-consent-sdk #onetrust-banner-sdk {background-color: white !important ;}
#onetrust-consent-sdk #onetrust-accept-btn-handler {
    background-color: #eed484;
    border-color: #FFFFFF;
    color: #000000;
}
#onetrust-consent-sdk #onetrust-reject-all-handler {
    background-color: #FFFFFF;
    border-color: #FFFFFF;
    color: #000000;
}
#onetrust-banner-sdk #onetrust-policy-title {
    float: left;
    text-align: left;
    font-size: 1.5em;
    line-height: 1.3;
    font-weight: 800;
    margin-bottom: 10px;
}
#onetrust-banner-sdk #onetrust-policy-text, #onetrust-banner-sdk .ot-b-addl-desc { 
    clear: both;
    float: left;
    margin-bottom: 0;
    padding-bottom: 10px;
    font-size: 1.5em;
    line-height: 1.5;
}#onetrust-pc-sdk{position:fixed;width:730px;max-width:730px;height:610px;left:0;right:0;top:0;bottom:0;margin:auto;font-size:16px;z-index:**********;border-radius:2px;background-color:#fff;box-shadow:0 2px 4px 0 rgba(0,0,0,0),0 7px 14px 0 rgba(50,50,93,.1)}#onetrust-pc-sdk.otRelFont{font-size:1rem}#onetrust-pc-sdk *,#onetrust-pc-sdk ::after,#onetrust-pc-sdk ::before{box-sizing:content-box}#onetrust-pc-sdk #ot-addtl-venlst .ot-arw-cntr,#onetrust-pc-sdk .ot-hide-tgl{visibility:hidden}#onetrust-pc-sdk #ot-addtl-venlst .ot-arw-cntr *,#onetrust-pc-sdk .ot-hide-tgl *{visibility:hidden}#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk #ot-pc-lst{height:calc(100% - 185px)}#onetrust-pc-sdk li{list-style:none}#onetrust-pc-sdk ul,#onetrust-pc-sdk li{margin:0}#onetrust-pc-sdk a{text-decoration:none}#onetrust-pc-sdk .ot-link-btn{padding:0;margin-bottom:0;border:0;font-weight:normal;line-height:normal;width:auto;height:auto}#onetrust-pc-sdk .ot-grps-cntr *::-webkit-scrollbar,#onetrust-pc-sdk .ot-pc-scrollbar::-webkit-scrollbar{width:11px}#onetrust-pc-sdk .ot-grps-cntr *::-webkit-scrollbar-thumb,#onetrust-pc-sdk .ot-pc-scrollbar::-webkit-scrollbar-thumb{border-radius:10px;background:#c1c1c1}#onetrust-pc-sdk .ot-grps-cntr *,#onetrust-pc-sdk .ot-pc-scrollbar{scrollbar-arrow-color:#c1c1c1;scrollbar-darkshadow-color:#c1c1c1;scrollbar-face-color:#c1c1c1;scrollbar-shadow-color:#c1c1c1}#onetrust-pc-sdk .ot-pc-header{height:auto;padding:10px;display:block;width:calc(100% - 20px);min-height:52px;border-bottom:1px solid #d8d8d8;position:relative}#onetrust-pc-sdk .ot-pc-logo{vertical-align:middle;width:180px}#onetrust-pc-sdk .ot-pc-logo.ot-pc-logo{height:40px}#onetrust-pc-sdk .ot-title-cntr{position:relative;display:inline-block;vertical-align:middle;width:calc(100% - 190px);padding-left:10px}#onetrust-pc-sdk .ot-optout-signal{margin:.625rem .625rem .625rem 1.75rem}#onetrust-pc-sdk .ot-always-active{font-size:.813em;line-height:1.5;font-weight:700;color:#3860be}#onetrust-pc-sdk .ot-close-cntr{float:right;position:absolute;right:-9px;top:50%;transform:translateY(-50%)}#onetrust-pc-sdk #ot-pc-content{position:relative;overflow-y:auto;overflow-x:hidden}#onetrust-pc-sdk #ot-pc-content .ot-sdk-container{margin-left:0}#onetrust-pc-sdk .ot-grps-cntr,#onetrust-pc-sdk .ot-grps-cntr>*{height:100%;overflow-y:auto}#onetrust-pc-sdk .category-menu-switch-handler{cursor:pointer;border-left:10px solid rgba(0,0,0,0);background-color:#f4f4f4;border-bottom:1px solid #d7d7d7;padding-top:12px;padding-right:5px;padding-bottom:12px;padding-left:12px;overflow:hidden}#onetrust-pc-sdk .category-menu-switch-handler h3{float:left;text-align:left;margin:0;color:dimgray;line-height:1.4;font-size:.875em;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk .ot-active-menu{border-left:10px solid #68b631;background-color:#fff;border-bottom:none;position:relative}#onetrust-pc-sdk .ot-active-menu h3{color:#263238;font-weight:bold}#onetrust-pc-sdk .ot-desc-cntr{word-break:break-word;word-wrap:break-word;padding-top:20px;padding-right:16px;padding-bottom:15px}#onetrust-pc-sdk .ot-grp-desc{word-break:break-word;word-wrap:break-word;text-align:left;font-size:.813em;line-height:1.5;margin:0}#onetrust-pc-sdk .ot-grp-desc *{font-size:inherit;line-height:inherit}#onetrust-pc-sdk #ot-pc-desc a{color:#3860be;cursor:pointer;font-size:1em;margin-right:8px}#onetrust-pc-sdk #ot-pc-desc a:hover{color:#1883fd}#onetrust-pc-sdk #ot-pc-desc button{margin-right:8px}#onetrust-pc-sdk #ot-pc-desc *{font-size:inherit}#onetrust-pc-sdk #ot-pc-desc ul li{padding:10px 0px;border-bottom:1px solid #e2e2e2}#onetrust-pc-sdk #ot-pc-desc+.ot-link-btn{display:none}#onetrust-pc-sdk .ot-btn-subcntr{float:right}#onetrust-pc-sdk .ot-close-icon{background-image:url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjQ3Ljk3MSIgaGVpZ2h0PSI0Ny45NzEiIHZpZXdCb3g9IjAgMCA0Ny45NzEgNDcuOTcxIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0Ny45NzEgNDcuOTcxOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PGc+PHBhdGggZD0iTTI4LjIyOCwyMy45ODZMNDcuMDkyLDUuMTIyYzEuMTcyLTEuMTcxLDEuMTcyLTMuMDcxLDAtNC4yNDJjLTEuMTcyLTEuMTcyLTMuMDctMS4xNzItNC4yNDIsMEwyMy45ODYsMTkuNzQ0TDUuMTIxLDAuODhjLTEuMTcyLTEuMTcyLTMuMDctMS4xNzItNC4yNDIsMGMtMS4xNzIsMS4xNzEtMS4xNzIsMy4wNzEsMCw0LjI0MmwxOC44NjUsMTguODY0TDAuODc5LDQyLjg1Yy0xLjE3MiwxLjE3MS0xLjE3MiwzLjA3MSwwLDQuMjQyQzEuNDY1LDQ3LjY3NywyLjIzMyw0Ny45NywzLDQ3Ljk3czEuNTM1LTAuMjkzLDIuMTIxLTAuODc5bDE4Ljg2NS0xOC44NjRMNDIuODUsNDcuMDkxYzAuNTg2LDAuNTg2LDEuMzU0LDAuODc5LDIuMTIxLDAuODc5czEuNTM1LTAuMjkzLDIuMTIxLTAuODc5YzEuMTcyLTEuMTcxLDEuMTcyLTMuMDcxLDAtNC4yNDJMMjguMjI4LDIzLjk4NnoiLz48L2c+PC9zdmc+");background-size:12px;background-repeat:no-repeat;background-position:center;height:44px;width:44px;display:inline-block}#onetrust-pc-sdk .ot-tgl{float:right;position:relative;z-index:1}#onetrust-pc-sdk .ot-tgl input:checked+.ot-switch .ot-switch-nob{background-color:#3c7356}#onetrust-pc-sdk .ot-tgl input:checked+.ot-switch .ot-switch-nob:before{-webkit-transform:translateX(16px);-ms-transform:translateX(16px);transform:translateX(16px);background-color:#6f9681}#onetrust-pc-sdk .ot-tgl input:focus+.ot-switch .ot-switch-nob:before{box-shadow:0 0 1px #2196f3;outline-style:auto;outline-width:1px}#onetrust-pc-sdk .ot-switch{position:relative;display:inline-block;width:35px;height:10px;margin-bottom:0}#onetrust-pc-sdk .ot-switch-nob{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background-color:#767676;border:none;transition:all .2s ease-in 0s;-moz-transition:all .2s ease-in 0s;-o-transition:all .2s ease-in 0s;-webkit-transition:all .2s ease-in 0s;border-radius:46px}#onetrust-pc-sdk .ot-switch-nob:before{position:absolute;content:"";height:20px;width:20px;bottom:1px;background-color:#4d4d4d;-webkit-transition:.4s;border-radius:100%;top:-5px;transition:.4s}#onetrust-pc-sdk .ot-chkbox{z-index:1;position:relative;float:left}#onetrust-pc-sdk .ot-chkbox input:checked~label::before{background-color:#3860be}#onetrust-pc-sdk .ot-chkbox input+label::after{content:none;color:#fff}#onetrust-pc-sdk .ot-chkbox input:checked+label::after{content:""}#onetrust-pc-sdk .ot-chkbox input:focus+label::before{outline-style:solid;outline-width:2px;outline-style:auto}#onetrust-pc-sdk .ot-chkbox label{position:relative;height:20px;padding-left:30px;display:inline-block;cursor:pointer}#onetrust-pc-sdk .ot-chkbox label::before,#onetrust-pc-sdk .ot-chkbox label::after{position:absolute;content:"";display:inline-block;border-radius:3px}#onetrust-pc-sdk .ot-chkbox label::before{height:18px;width:18px;border:1px solid #3860be;left:0px}#onetrust-pc-sdk .ot-chkbox label::after{height:5px;width:9px;border-left:3px solid;border-bottom:3px solid;transform:rotate(-45deg);-o-transform:rotate(-45deg);-ms-transform:rotate(-45deg);-webkit-transform:rotate(-45deg);left:4px;top:5px}#onetrust-pc-sdk .ot-label-txt{display:none}#onetrust-pc-sdk .ot-fltr-opt .ot-label-txt{display:block}#onetrust-pc-sdk .ot-chkbox input,#onetrust-pc-sdk .ot-tgl input{position:absolute;opacity:0;width:0;height:0}#onetrust-pc-sdk .ot-arw-cntr{float:right;position:relative;pointer-events:none}#onetrust-pc-sdk .ot-arw{width:16px;height:16px;margin-left:5px;color:dimgray;display:inline-block;vertical-align:middle;-webkit-transition:all 150ms ease-in 0s;-moz-transition:all 150ms ease-in 0s;-o-transition:all 150ms ease-in 0s;transition:all 150ms ease-in 0s}#onetrust-pc-sdk input:checked~.ot-acc-hdr .ot-arw,#onetrust-pc-sdk button[aria-expanded=true]~.ot-acc-hdr .ot-arw-cntr svg{transform:rotate(90deg);-o-transform:rotate(90deg);-ms-transform:rotate(90deg);-webkit-transform:rotate(90deg)}#onetrust-pc-sdk .ot-label-status{font-size:.75em;position:relative;top:2px;display:none;padding-right:5px;float:left}#onetrust-pc-sdk #ot-lst-cnt .ot-label-status{top:-6px}#onetrust-pc-sdk .ot-fltr-opts{min-height:35px}#onetrust-pc-sdk .ot-fltr-btns{margin:10px 15px 0 15px}#onetrust-pc-sdk .ot-fltr-btns button{padding:12px 30px}#onetrust-pc-sdk .ot-pc-footer{position:absolute;bottom:0px;width:100%;max-height:160px;border-top:1px solid #d8d8d8}#onetrust-pc-sdk .ot-pc-footer button{margin-top:20px;margin-bottom:20px;font-weight:600;font-size:.813em;min-height:40px;height:auto;line-height:normal;padding:10px 30px}#onetrust-pc-sdk .ot-tab-desc{margin-left:3%}#onetrust-pc-sdk .ot-grp-hdr1{display:inline-block;width:100%;margin-bottom:10px}#onetrust-pc-sdk .ot-desc-cntr h4{color:#263238;display:inline-block;vertical-align:middle;margin:0;font-weight:bold;font-size:.875em;line-height:1.3;max-width:80%}#onetrust-pc-sdk .ot-subgrps .ot-subgrp h5{top:0;max-width:unset}#onetrust-pc-sdk #ot-pvcy-hdr{margin-bottom:10px}#onetrust-pc-sdk .ot-vlst-cntr{overflow:hidden}#onetrust-pc-sdk .category-vendors-list-handler,#onetrust-pc-sdk .category-host-list-handler,#onetrust-pc-sdk .category-vendors-list-handler+a{display:block;float:left;color:#3860be;font-size:.813em;font-weight:400;line-height:1.1;cursor:pointer;margin:5px 0px}#onetrust-pc-sdk .category-vendors-list-handler:hover,#onetrust-pc-sdk .category-host-list-handler:hover,#onetrust-pc-sdk .category-vendors-list-handler+a:hover{text-decoration-line:underline}#onetrust-pc-sdk .ot-vlst-cntr .ot-ext-lnk,#onetrust-pc-sdk .ot-ven-hdr .ot-ext-lnk{display:inline-block;height:13px;width:13px;background-repeat:no-repeat;margin-left:1px;margin-top:6px;cursor:pointer}#onetrust-pc-sdk .ot-ven-hdr .ot-ext-lnk{margin-bottom:-1px}#onetrust-pc-sdk .category-host-list-handler,#onetrust-pc-sdk .ot-vlst-cntr,#onetrust-pc-sdk #ot-pc-desc+.category-vendors-list-handler{margin-top:8px}#onetrust-pc-sdk .ot-grp-hdr1+.ot-vlst-cntr{margin-top:0px;margin-bottom:10px}#onetrust-pc-sdk .ot-always-active-group h3.ot-cat-header,#onetrust-pc-sdk .ot-subgrp.ot-always-active-group>h4{max-width:70%}#onetrust-pc-sdk .ot-always-active-group .ot-tgl-cntr{max-width:28%}#onetrust-pc-sdk .ot-grp-desc ul,#onetrust-pc-sdk li.ot-subgrp p ul{margin:0px;margin-left:15px;padding-bottom:8px}#onetrust-pc-sdk .ot-grp-desc ul li,#onetrust-pc-sdk li.ot-subgrp p ul li{font-size:inherit;padding-top:8px;display:list-item;list-style:disc}#onetrust-pc-sdk ul.ot-subgrps{margin:0;font-size:inherit}#onetrust-pc-sdk ul.ot-subgrps li{padding:0;border:none;position:relative}#onetrust-pc-sdk ul.ot-subgrps li h5,#onetrust-pc-sdk ul.ot-subgrps li p{font-size:.82em;line-height:1.4}#onetrust-pc-sdk ul.ot-subgrps li p{color:dimgray;clear:both;float:left;margin-top:10px;margin-bottom:0;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk ul.ot-subgrps li h5{color:#263238;font-weight:bold;margin-bottom:0;float:left;position:relative;top:3px}#onetrust-pc-sdk li.ot-subgrp{margin-left:30px;display:inline-block;width:calc(100% - 30px)}#onetrust-pc-sdk .ot-subgrp-tgl{float:right}#onetrust-pc-sdk .ot-subgrp-tgl.ot-always-active-subgroup{width:auto}#onetrust-pc-sdk .ot-pc-footer-logo{height:30px;width:100%;text-align:right;background:#f4f4f4;border-radius:0 0 2px 2px}#onetrust-pc-sdk .ot-pc-footer-logo a{display:inline-block;margin-top:5px;margin-right:10px}#onetrust-pc-sdk #accept-recommended-btn-handler{float:right;text-align:center}#onetrust-pc-sdk .save-preference-btn-handler{min-width:155px;background-color:#68b631;border-radius:2px;color:#fff;font-size:.9em;line-height:1.1;text-align:center;margin-left:15px;margin-right:15px}#onetrust-pc-sdk .ot-btn-subcntr button{margin-right:16px}#onetrust-pc-sdk.ot-ftr-stacked .save-preference-btn-handler,#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr{white-space:normal;text-align:center;width:min-content;float:left;min-width:40%}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr button{text-wrap:wrap;margin-left:auto;margin-right:auto;width:90%}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr button:nth-child(2){margin-top:0}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr{float:right}#onetrust-pc-sdk.ot-ftr-stacked #accept-recommended-btn-handler{float:none}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-container{overflow:hidden}#onetrust-pc-sdk #ot-pc-title{margin:0px;overflow:hidden;position:relative;line-height:1.2;max-height:2.4em;padding-right:1em;font-size:1.37em;text-overflow:ellipsis;white-space:nowrap;display:block;max-width:90%}#onetrust-pc-sdk #ot-pc-title.ot-pc-title-shrink{max-width:70%}#onetrust-pc-sdk #ot-pc-lst{width:100%;position:relative}#onetrust-pc-sdk #ot-pc-lst .ot-acc-hdr{padding-top:17px;padding-right:15px;padding-bottom:17px;padding-left:20px;display:inline-block;width:calc(100% - 35px);vertical-align:middle}#onetrust-pc-sdk #ot-pc-lst .ot-acc-txt{padding-top:6px;padding-right:15px;padding-bottom:10px;padding-left:20px}#onetrust-pc-sdk .ot-lst-cntr{height:100%}#onetrust-pc-sdk #ot-pc-hdr{padding-top:15px;padding-right:30px;padding-bottom:15px;padding-left:20px;display:inline-block;width:calc(100% - 50px);height:20px;border-bottom:1px solid #d8d8d8}#onetrust-pc-sdk #ot-pc-hdr input{height:32px;width:100%;border-radius:50px;font-size:.8em;padding-right:35px;padding-left:15px;float:left}#onetrust-pc-sdk #ot-pc-hdr input::placeholder{color:#d4d4d4;font-style:italic}#onetrust-pc-sdk #ot-lst-cnt{height:calc(100% - 86px);padding-left:30px;padding-right:27px;padding-top:20px;margin-top:8px;margin-right:3px;margin-bottom:4px;margin-left:0;overflow-x:hidden;overflow-y:auto;transform:translate3d(0, 0, 0)}#onetrust-pc-sdk #ot-back-arw{height:12px;width:12px}#onetrust-pc-sdk #ot-lst-title{display:inline-block;font-size:1em}#onetrust-pc-sdk #ot-lst-title h3{color:dimgray;font-weight:bold;margin-left:10px;display:inline-block;font-size:1em}#onetrust-pc-sdk #ot-lst-title h3 *{font-size:inherit}#onetrust-pc-sdk .ot-lst-subhdr{float:right;position:relative;bottom:6px}#onetrust-pc-sdk #ot-search-cntr{display:inline-block;vertical-align:middle;position:relative;width:300px}#onetrust-pc-sdk #ot-search-cntr svg{position:absolute;right:0px;width:30px;height:30px;font-size:1em;line-height:1;top:2px}#onetrust-pc-sdk #ot-fltr-cntr{display:inline-block;position:relative;margin-left:20px;vertical-align:middle;font-size:0}#onetrust-pc-sdk #filter-btn-handler{background-color:#3860be;border-radius:17px;-moz-transition:.1s ease;-o-transition:.1s ease;-webkit-transition:1s ease;transition:.1s ease;width:32px;height:32px;padding:0;margin:0;position:relative}#onetrust-pc-sdk #filter-btn-handler svg{cursor:pointer;width:15px;height:15px;position:absolute;left:50%;top:50%;transform:translate(-50%, -50%);padding-top:5px}#onetrust-pc-sdk #filter-btn-handler path{fill:#fff}#onetrust-pc-sdk #ot-sel-blk{min-width:200px;min-height:30px;padding-left:20px}#onetrust-pc-sdk #ot-selall-vencntr,#onetrust-pc-sdk #ot-selall-adtlvencntr{float:left;height:100%}#onetrust-pc-sdk #ot-selall-vencntr label,#onetrust-pc-sdk #ot-selall-adtlvencntr label{height:100%;padding-left:0}#onetrust-pc-sdk #ot-selall-hostcntr{width:21px;height:21px;position:relative;left:20px}#onetrust-pc-sdk #ot-selall-vencntr.line-through label::after,#onetrust-pc-sdk #ot-selall-adtlvencntr.line-through label::after,#onetrust-pc-sdk #ot-selall-licntr.line-through label::after,#onetrust-pc-sdk #ot-selall-hostcntr.line-through label::after,#onetrust-pc-sdk #ot-selall-gnvencntr.line-through label::after{height:auto;border-left:0;left:5px;top:10.5px;transform:none;-o-transform:none;-ms-transform:none;-webkit-transform:none}#onetrust-pc-sdk .ot-ven-name,#onetrust-pc-sdk .ot-host-name{color:#2c3643;font-weight:bold;font-size:.813em;line-height:1.2;margin:0;height:auto;text-align:left;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk .ot-ven-name *,#onetrust-pc-sdk .ot-host-name *{font-size:inherit}#onetrust-pc-sdk .ot-host-desc{font-size:.69em;line-height:1.4;margin-top:5px;margin-bottom:5px;color:dimgray}#onetrust-pc-sdk .ot-host-name>a{text-decoration:underline;position:relative;z-index:2;margin-bottom:5px;font-weight:bold}#onetrust-pc-sdk .ot-host-hdr{float:left;width:calc(100% - 50px);pointer-events:none;position:relative;z-index:1}#onetrust-pc-sdk .ot-host-hdr .ot-host-name{pointer-events:none}#onetrust-pc-sdk .ot-host-hdr a{pointer-events:initial}#onetrust-pc-sdk .ot-host-hdr .ot-host-name~a{margin-top:5px;font-size:.813em;text-decoration:underline}#onetrust-pc-sdk .ot-ven-hdr{width:88%;float:right}#onetrust-pc-sdk input:focus+.ot-acc-hdr{outline:#000 solid 1px !important}#onetrust-pc-sdk #ot-selall-hostcntr input[type=checkbox],#onetrust-pc-sdk #ot-selall-vencntr input[type=checkbox],#onetrust-pc-sdk #ot-selall-adtlvencntr input[type=checkbox]{position:absolute}#onetrust-pc-sdk .ot-host-item .ot-chkbox{float:left}#onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-sel-all-hdr{right:38px}#onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) #ot-sel-blk{background-color:#f9f9fc;border:1px solid #e2e2e2;width:auto;padding-bottom:5px;padding-top:5px}#onetrust-pc-sdk.ot-addtl-vendors #ot-lst-cnt:not(.ot-host-cnt) .ot-sel-all-chkbox{right:2px;width:auto}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr{position:relative;border-left:1px solid #e2e2e2;border-right:1px solid #e2e2e2;border-bottom:1px solid #e2e2e2}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr input{z-index:1}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr>.ot-acc-hdr{background:#f9f9fc;padding-top:10px;padding-bottom:10px;background-color:#f9f9fc}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr>.ot-acc-hdr input{z-index:2}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr>input[type=checkbox]:checked~.ot-acc-hdr{border-bottom:1px solid #e2e2e2}#onetrust-pc-sdk #ot-pc-lst .ot-acc-cntr .ot-addtl-venbox{display:none}#onetrust-pc-sdk #ot-addtl-venlst .ot-tgl-cntr{margin-right:13px}#onetrust-pc-sdk .ot-vensec-title{font-size:.813em;display:inline-block}#onetrust-pc-sdk .ot-ven-item>button:focus,#onetrust-pc-sdk .ot-host-item>button:focus,#onetrust-pc-sdk .ot-acc-cntr>button:focus{outline:#000 solid 2px}#onetrust-pc-sdk .ot-ven-item>button,#onetrust-pc-sdk .ot-host-item>button,#onetrust-pc-sdk .ot-acc-cntr>button{position:absolute;cursor:pointer;width:100%;height:100%;border:0;opacity:0;margin:0;top:0;left:0}#onetrust-pc-sdk .ot-ven-item>button~.ot-acc-hdr,#onetrust-pc-sdk .ot-host-item>button~.ot-acc-hdr,#onetrust-pc-sdk .ot-acc-cntr>button~.ot-acc-hdr{cursor:pointer}#onetrust-pc-sdk .ot-ven-item>button[aria-expanded=false]~.ot-acc-txt,#onetrust-pc-sdk .ot-host-item>button[aria-expanded=false]~.ot-acc-txt,#onetrust-pc-sdk .ot-acc-cntr>button[aria-expanded=false]~.ot-acc-txt{margin-top:0;max-height:0;opacity:0;overflow:hidden;width:100%;transition:.25s ease-out;display:none}#onetrust-pc-sdk .ot-ven-item>button[aria-expanded=true]~.ot-acc-txt,#onetrust-pc-sdk .ot-host-item>button[aria-expanded=true]~.ot-acc-txt,#onetrust-pc-sdk .ot-acc-cntr>button[aria-expanded=true]~.ot-acc-txt{transition:.1s ease-in;display:block}#onetrust-pc-sdk .ot-ven-item ul{list-style:none inside;font-size:100%;margin:0}#onetrust-pc-sdk .ot-ven-item ul li{margin:0 !important;padding:0;border:none !important}#onetrust-pc-sdk .ot-hide-acc>button{pointer-events:none}#onetrust-pc-sdk .ot-hide-acc .ot-arw-cntr>*{visibility:hidden}#onetrust-pc-sdk #ot-ven-lst,#onetrust-pc-sdk #ot-host-lst,#onetrust-pc-sdk #ot-addtl-venlst,#onetrust-pc-sdk #ot-gn-venlst{width:100%}#onetrust-pc-sdk #ot-ven-lst li,#onetrust-pc-sdk #ot-host-lst li,#onetrust-pc-sdk #ot-addtl-venlst li,#onetrust-pc-sdk #ot-gn-venlst li{border:1px solid #d7d7d7;border-radius:2px;position:relative;margin-top:10px}#onetrust-pc-sdk #ot-gn-venlst li.ot-host-info{padding:.5rem;overflow-y:hidden}#onetrust-pc-sdk #ot-ven-lst .ot-tgl-cntr{width:65%}#onetrust-pc-sdk #ot-host-lst .ot-tgl-cntr{width:65%;float:left}#onetrust-pc-sdk label{margin-bottom:0}#onetrust-pc-sdk .ot-host-notice{float:right}#onetrust-pc-sdk .ot-ven-link,#onetrust-pc-sdk .ot-ven-legclaim-link,#onetrust-pc-sdk .ot-host-expand{color:dimgray;font-size:.75em;line-height:.9;display:inline-block}#onetrust-pc-sdk .ot-ven-link *,#onetrust-pc-sdk .ot-ven-legclaim-link *,#onetrust-pc-sdk .ot-host-expand *{font-size:inherit}#onetrust-pc-sdk .ot-ven-link,#onetrust-pc-sdk .ot-ven-legclaim-link{position:relative;z-index:2}#onetrust-pc-sdk .ot-ven-link:hover,#onetrust-pc-sdk .ot-ven-legclaim-link:hover{text-decoration:underline}#onetrust-pc-sdk .ot-ven-dets{border-radius:2px;background-color:#f8f8f8}#onetrust-pc-sdk .ot-ven-dets li:first-child p:first-child{border-top:none}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc:not(:first-child){border-top:1px solid #ddd !important}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc:nth-child(n+3) p{display:inline-block}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc:nth-child(n+3) p:nth-of-type(odd){width:30%}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc:nth-child(n+3) p:nth-of-type(even){width:50%;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc p,#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc h5{padding-top:5px;padding-bottom:5px;display:block}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc h5{display:inline-block}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc p:nth-last-child(-n+1){padding-bottom:10px}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc p:nth-child(-n+2):not(.disc-pur){padding-top:10px}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc .disc-pur-cont{display:inline}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc .disc-pur{position:relative;width:50% !important;word-break:break-word;word-wrap:break-word;left:calc(30% + 17px)}#onetrust-pc-sdk .ot-ven-dets .ot-ven-disc .disc-pur:nth-child(-n+1){position:static}#onetrust-pc-sdk .ot-ven-dets p,#onetrust-pc-sdk .ot-ven-dets h5,#onetrust-pc-sdk .ot-ven-dets span{font-size:.69em;text-align:left;vertical-align:middle;word-break:break-word;word-wrap:break-word;margin:0;padding-bottom:10px;padding-left:15px;color:#2e3644}#onetrust-pc-sdk .ot-ven-dets h5{padding-top:5px}#onetrust-pc-sdk .ot-ven-dets span{color:dimgray;padding:0;vertical-align:baseline}#onetrust-pc-sdk .ot-ven-dets .ot-ven-pur h5{border-top:1px solid #e9e9e9;border-bottom:1px solid #e9e9e9;padding-bottom:5px;margin-bottom:5px;font-weight:bold}#onetrust-pc-sdk .ot-host-opt{display:inline-block;width:100%;margin:0;font-size:inherit}#onetrust-pc-sdk .ot-host-opt li>div div{font-size:.81em;padding:5px 0}#onetrust-pc-sdk .ot-host-opt li>div div:nth-child(1){width:30%;float:left}#onetrust-pc-sdk .ot-host-opt li>div div:nth-child(2){width:70%;float:left;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk #ot-host-lst li.ot-host-info{border:none;font-size:.8em;color:dimgray;float:left;text-align:left;padding:10px;margin-bottom:10px;width:calc(100% - 10px);background-color:#f8f8f8}#onetrust-pc-sdk #ot-host-lst li.ot-host-info a{color:dimgray}#onetrust-pc-sdk #ot-host-lst li.ot-host-info>div{overflow:auto}#onetrust-pc-sdk #no-results{text-align:center;margin-top:30px}#onetrust-pc-sdk #no-results p{font-size:1em;color:#2e3644;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk #no-results p span{font-weight:bold}#onetrust-pc-sdk .ot-tgl-cntr{display:inline-block;vertical-align:middle}#onetrust-pc-sdk .ot-arw-cntr,#onetrust-pc-sdk .ot-tgl-cntr{float:right}#onetrust-pc-sdk .ot-desc-cntr{padding-top:0px;margin-top:20px;padding-right:0px;border-radius:3px;overflow:hidden;padding-bottom:10px}#onetrust-pc-sdk .ot-desc-cntr:focus,#onetrust-pc-sdk .ot-desc-cntr:active,#onetrust-pc-sdk .ot-desc-cntr:focus-visible{outline:2px solid #101010;border-radius:2px}#onetrust-pc-sdk .ot-leg-border-color{border:1px solid #e9e9e9}#onetrust-pc-sdk .ot-leg-border-color .ot-subgrp-cntr{border-top:1px solid #e9e9e9;padding-bottom:10px}#onetrust-pc-sdk .ot-category-desc{padding-bottom:10px}#onetrust-pc-sdk .ot-grp-hdr1{padding-left:10px;width:calc(100% - 20px);padding-top:10px;margin-bottom:0px;padding-bottom:8px}#onetrust-pc-sdk .ot-subgrp-cntr{padding-top:10px}#onetrust-pc-sdk .ot-desc-cntr>*:not(.ot-grp-hdr1){padding-left:10px;padding-right:10px}#onetrust-pc-sdk .ot-pli-hdr{overflow:hidden;padding-top:7.5px;padding-bottom:7.5px;background-color:#f8f8f8;border:none;border-bottom:1px solid #e9e9e9}#onetrust-pc-sdk .ot-pli-hdr span:first-child{text-align:left;max-width:80px;padding-right:5px}#onetrust-pc-sdk .ot-pli-hdr span:last-child{padding-right:20px;text-align:center}#onetrust-pc-sdk .ot-li-title{float:right;font-size:.813em}#onetrust-pc-sdk .ot-desc-cntr .ot-tgl-cntr:first-of-type,#onetrust-pc-sdk .ot-cat-header+.ot-tgl{padding-left:7px;padding-right:7px}#onetrust-pc-sdk .ot-always-active-group .ot-grp-hdr1 .ot-tgl-cntr:first-of-type{padding-left:0px}#onetrust-pc-sdk .ot-cat-header,#onetrust-pc-sdk .ot-subgrp h4{max-width:calc(100% - 133px)}#onetrust-pc-sdk #ot-lst-cnt #ot-sel-blk{width:100%;display:inline-block;padding:0}#onetrust-pc-sdk .ot-sel-all{display:inline-block;width:100%}#onetrust-pc-sdk .ot-sel-all-hdr,#onetrust-pc-sdk .ot-sel-all-chkbox{width:100%;float:right;position:relative}#onetrust-pc-sdk .ot-sel-all-chkbox{z-index:1}#onetrust-pc-sdk :not(.ot-hosts-ui) .ot-sel-all-hdr,#onetrust-pc-sdk :not(.ot-hosts-ui) .ot-sel-all-chkbox{right:23px;width:calc(100% - 23px)}#onetrust-pc-sdk .ot-consent-hdr,#onetrust-pc-sdk .ot-li-hdr{float:right;font-size:.813em;position:relative;line-height:normal;text-align:center;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk .ot-hosts-ui .ot-consent-hdr{float:left;position:relative;left:5px}#onetrust-pc-sdk .ot-li-hdr{max-width:100px;margin-right:10px}#onetrust-pc-sdk .ot-consent-hdr{max-width:55px}#onetrust-pc-sdk .ot-ven-ctgl{margin-left:10px}#onetrust-pc-sdk .ot-ven-litgl{margin-right:55px}#onetrust-pc-sdk .ot-ven-litgl.ot-ven-litgl-only{margin-right:86px}#onetrust-pc-sdk .ot-ven-ctgl,#onetrust-pc-sdk .ot-ven-litgl,#onetrust-pc-sdk .ot-ven-gvctgl{float:left}#onetrust-pc-sdk .ot-ven-ctgl label,#onetrust-pc-sdk .ot-ven-litgl label,#onetrust-pc-sdk .ot-ven-gvctgl label{width:22px;padding:0}#onetrust-pc-sdk #ot-selall-licntr{display:block;width:21px;height:21px;position:relative;float:right;right:80px}#onetrust-pc-sdk #ot-selall-licntr input{position:absolute}#onetrust-pc-sdk #ot-selall-vencntr,#onetrust-pc-sdk #ot-selall-adtlvencntr,#onetrust-pc-sdk #ot-selall-gnvencntr{float:right;width:21px;height:21px;position:relative;right:15px}#onetrust-pc-sdk #ot-ven-lst .ot-tgl-cntr{float:right;width:auto}#onetrust-pc-sdk .ot-ven-hdr{float:left;width:60%}#onetrust-pc-sdk #vdr-lst-dsc{font-size:.812em;line-height:1.5;padding:10px 15px 5px 15px}#onetrust-pc-sdk #ot-anchor{border:12px solid rgba(0,0,0,0);display:none;position:absolute;z-index:**********;top:40px;right:35px;transform:rotate(45deg);-o-transform:rotate(45deg);-ms-transform:rotate(45deg);-webkit-transform:rotate(45deg);background-color:#fff;-webkit-box-shadow:-3px -3px 5px -2px #c7c5c7;-moz-box-shadow:-3px -3px 5px -2px #c7c5c7;box-shadow:-3px -3px 5px -2px #c7c5c7}#onetrust-pc-sdk #ot-fltr-modal{width:300px;position:absolute;z-index:2147483646;top:46px;height:90%;max-height:350px;display:none;-moz-transition:.2s ease;-o-transition:.2s ease;-webkit-transition:2s ease;transition:.2s ease;opacity:1;right:0}#onetrust-pc-sdk #ot-fltr-modal button{max-width:200px;line-height:1;word-break:break-word;white-space:normal;height:auto;font-weight:bold}#onetrust-pc-sdk #ot-fltr-cnt{background-color:#fff;margin:5px;border-radius:3px;height:100%;margin-right:10px;padding-right:10px;-webkit-box-shadow:0px 0px 12px 2px #c7c5c7;-moz-box-shadow:0px 0px 12px 2px #c7c5c7;box-shadow:0px 0px 12px 2px #c7c5c7}#onetrust-pc-sdk .ot-fltr-scrlcnt{overflow-y:auto;overflow-x:hidden;clear:both;max-height:calc(100% - 60px)}#onetrust-pc-sdk .ot-fltr-opt{margin-bottom:25px;margin-left:15px;clear:both}#onetrust-pc-sdk .ot-fltr-opt label{height:auto}#onetrust-pc-sdk .ot-fltr-opt span{cursor:pointer;color:dimgray;font-size:.8em;line-height:1.1;font-weight:normal}#onetrust-pc-sdk #clear-filters-handler{float:right;margin-top:15px;margin-bottom:10px;text-decoration:none;color:#3860be;font-size:.9em;border:none;padding:1px}#onetrust-pc-sdk #clear-filters-handler:hover{color:#1883fd}#onetrust-pc-sdk #clear-filters-handler:focus{outline:#000 solid 1px}#onetrust-pc-sdk #filter-apply-handler{margin-right:10px}#onetrust-pc-sdk .ot-grp-desc+.ot-leg-btn-container{margin-top:0}#onetrust-pc-sdk .ot-leg-btn-container{display:inline-block;width:100%;margin-top:10px}#onetrust-pc-sdk .ot-leg-btn-container button{height:auto;padding:6.5px 8px;margin-bottom:0;line-height:normal;letter-spacing:0}#onetrust-pc-sdk .ot-leg-btn-container svg{display:none;height:14px;width:14px;padding-right:5px;vertical-align:sub}#onetrust-pc-sdk .ot-active-leg-btn{cursor:default;pointer-events:none}#onetrust-pc-sdk .ot-active-leg-btn svg{display:inline-block}#onetrust-pc-sdk .ot-remove-objection-handler{border:none;text-decoration:underline;padding:0;font-size:.82em;font-weight:600;line-height:1.4;padding-left:10px}#onetrust-pc-sdk .ot-obj-leg-btn-handler span{font-weight:bold;text-align:center;font-size:.91em;line-height:1.5}#onetrust-pc-sdk.ot-close-btn-link #close-pc-btn-handler{border:none;height:auto;line-height:1.5;text-decoration:underline;font-size:.69em;background:none;width:auto}#onetrust-pc-sdk.ot-close-btn-link .ot-close-cntr{right:5px;top:5px;transform:none}#onetrust-pc-sdk .ot-grps-cntr{overflow-y:hidden}#onetrust-pc-sdk .ot-cat-header{float:left;font-weight:600;font-size:.875em;line-height:1.5;max-width:90%;vertical-align:middle}#onetrust-pc-sdk .ot-vnd-item>button:focus{outline:#000 solid 2px}#onetrust-pc-sdk .ot-vnd-item>button{position:absolute;cursor:pointer;width:100%;height:100%;margin:0;top:0;left:0;z-index:1;max-width:none;border:none}#onetrust-pc-sdk .ot-vnd-item>button[aria-expanded=false]~.ot-acc-txt{margin-top:0;max-height:0;opacity:0;overflow:hidden;width:100%;transition:.25s ease-out;display:none}#onetrust-pc-sdk .ot-vnd-item>button[aria-expanded=true]~.ot-acc-txt{transition:.1s ease-in;margin-top:10px;width:100%;overflow:auto;display:block}#onetrust-pc-sdk .ot-vnd-item>button[aria-expanded=true]~.ot-acc-grpcntr{width:auto;margin-top:0px;padding-bottom:10px}#onetrust-pc-sdk .ot-accordion-layout.ot-cat-item{position:relative;border-radius:2px;margin:0;padding:0;border:1px solid #d8d8d8;border-top:none;width:calc(100% - 2px);float:left}#onetrust-pc-sdk .ot-accordion-layout.ot-cat-item:first-of-type{margin-top:10px;border-top:1px solid #d8d8d8}#onetrust-pc-sdk .ot-accordion-layout .ot-vlst-cntr:first-child{margin-top:10px}#onetrust-pc-sdk .ot-accordion-layout .ot-vlst-cntr:last-child,#onetrust-pc-sdk .ot-accordion-layout .ot-hlst-cntr:last-child{margin-bottom:5px}#onetrust-pc-sdk .ot-accordion-layout .ot-acc-hdr{padding-top:11.5px;padding-bottom:11.5px;padding-left:20px;padding-right:20px;width:calc(100% - 40px);display:inline-block}#onetrust-pc-sdk .ot-accordion-layout .ot-acc-txt{width:100%;padding:0}#onetrust-pc-sdk .ot-accordion-layout .ot-subgrp-cntr{padding-left:20px;padding-right:15px;padding-bottom:0;width:calc(100% - 35px)}#onetrust-pc-sdk .ot-accordion-layout .ot-subgrp{padding-right:5px}#onetrust-pc-sdk .ot-accordion-layout .ot-acc-grpcntr{z-index:1;position:relative}#onetrust-pc-sdk .ot-accordion-layout .ot-cat-header+.ot-arw-cntr{position:absolute;top:50%;transform:translateY(-50%);right:20px;margin-top:-2px}#onetrust-pc-sdk .ot-accordion-layout .ot-cat-header+.ot-arw-cntr .ot-arw{width:15px;height:20px;margin-left:5px;color:dimgray}#onetrust-pc-sdk .ot-accordion-layout .ot-cat-header{float:none;color:#2e3644;margin:0;display:inline-block;height:auto;word-wrap:break-word;min-height:inherit}#onetrust-pc-sdk .ot-accordion-layout .ot-vlst-cntr,#onetrust-pc-sdk .ot-accordion-layout .ot-hlst-cntr{padding-left:20px;width:calc(100% - 20px);display:inline-block;margin-top:0;padding-bottom:2px}#onetrust-pc-sdk .ot-accordion-layout .ot-acc-hdr{position:relative;min-height:25px}#onetrust-pc-sdk .ot-accordion-layout h4~.ot-tgl,#onetrust-pc-sdk .ot-accordion-layout h4~.ot-always-active{position:absolute;top:50%;transform:translateY(-50%);right:20px}#onetrust-pc-sdk .ot-accordion-layout h4~.ot-tgl+.ot-tgl{right:95px}#onetrust-pc-sdk .ot-accordion-layout .category-vendors-list-handler,#onetrust-pc-sdk .ot-accordion-layout .category-vendors-list-handler+a{margin-top:5px}#onetrust-pc-sdk #ot-lst-cnt{margin-top:1rem;max-height:calc(100% - 96px)}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info-cntr{border:1px solid #d8d8d8;padding:.75rem 2rem;padding-bottom:0;width:auto;margin-top:.5rem}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info{margin-bottom:1rem;padding-left:.75rem;padding-right:.75rem;display:flex;flex-direction:column}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info[data-vnd-info-key*=DPOEmail]{border-top:1px solid #d8d8d8;padding-top:1rem}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info[data-vnd-info-key*=DPOLink]{border-bottom:1px solid #d8d8d8;padding-bottom:1rem}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info .ot-vnd-lbl{font-weight:bold;font-size:.85em;margin-bottom:.5rem}#onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info .ot-vnd-cnt{margin-left:.5rem;font-weight:500;font-size:.85rem}#onetrust-pc-sdk .ot-vs-list,#onetrust-pc-sdk .ot-vnd-serv{width:auto;padding:1rem 1.25rem;padding-bottom:0}#onetrust-pc-sdk .ot-vs-list .ot-vnd-serv-hdr-cntr,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-serv-hdr-cntr{padding-bottom:.75rem;border-bottom:1px solid #d8d8d8}#onetrust-pc-sdk .ot-vs-list .ot-vnd-serv-hdr-cntr .ot-vnd-serv-hdr,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-serv-hdr-cntr .ot-vnd-serv-hdr{font-weight:600;font-size:.95em;line-height:2;margin-left:.5rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item{border:none;margin:0;padding:0}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item button,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item button{outline:none;border-bottom:1px solid #d8d8d8}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item button[aria-expanded=true],#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item button[aria-expanded=true]{border-bottom:none}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item:first-child,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item:first-child{margin-top:.25rem;border-top:unset}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item:last-child,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item:last-child{margin-bottom:.5rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item:last-child button,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item:last-child button{border-bottom:none}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info-cntr,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info-cntr{border:1px solid #d8d8d8;padding:.75rem 1.75rem;padding-bottom:0;width:auto;margin-top:.5rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info{margin-bottom:1rem;padding-left:.75rem;padding-right:.75rem;display:flex;flex-direction:column}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info[data-vnd-info-key*=DPOEmail],#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info[data-vnd-info-key*=DPOEmail]{border-top:1px solid #d8d8d8;padding-top:1rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info[data-vnd-info-key*=DPOLink],#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info[data-vnd-info-key*=DPOLink]{border-bottom:1px solid #d8d8d8;padding-bottom:1rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info .ot-vnd-lbl,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info .ot-vnd-lbl{font-weight:bold;font-size:.85em;margin-bottom:.5rem}#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-vnd-info .ot-vnd-cnt,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info .ot-vnd-cnt{margin-left:.5rem;font-weight:500;font-size:.85rem}#onetrust-pc-sdk .ot-vs-list.ot-vnd-subgrp-cnt,#onetrust-pc-sdk .ot-vnd-serv.ot-vnd-subgrp-cnt{padding-left:40px}#onetrust-pc-sdk .ot-vs-list.ot-vnd-subgrp-cnt .ot-vnd-serv-hdr-cntr .ot-vnd-serv-hdr,#onetrust-pc-sdk .ot-vnd-serv.ot-vnd-subgrp-cnt .ot-vnd-serv-hdr-cntr .ot-vnd-serv-hdr{font-size:.8em}#onetrust-pc-sdk .ot-vs-list.ot-vnd-subgrp-cnt .ot-cat-header,#onetrust-pc-sdk .ot-vnd-serv.ot-vnd-subgrp-cnt .ot-cat-header{font-size:.8em}#onetrust-pc-sdk .ot-subgrp-cntr .ot-vnd-serv{margin-bottom:1rem;padding:1rem .95rem}#onetrust-pc-sdk .ot-subgrp-cntr .ot-vnd-serv .ot-vnd-serv-hdr-cntr{padding-bottom:.75rem;border-bottom:1px solid #d8d8d8}#onetrust-pc-sdk .ot-subgrp-cntr .ot-vnd-serv .ot-vnd-serv-hdr-cntr .ot-vnd-serv-hdr{font-weight:700;font-size:.8em;line-height:20px;margin-left:.82rem}#onetrust-pc-sdk .ot-subgrp-cntr .ot-cat-header{font-weight:700;font-size:.8em;line-height:20px}#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-vnd-serv .ot-vnd-lst-cont .ot-accordion-layout .ot-acc-hdr div.ot-chkbox{margin-left:.82rem}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr{padding:.7rem 0;margin:0;display:flex;width:100%;align-items:center;justify-content:space-between}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr div:first-child,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr div:first-child,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr div:first-child,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr div:first-child,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr div:first-child,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr div:first-child,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr div:first-child{margin-left:.5rem}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr div:last-child,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr div:last-child,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr div:last-child,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr div:last-child,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr div:last-child,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr div:last-child,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr div:last-child{margin-right:.5rem;margin-left:.5rem}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-always-active,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-always-active{position:relative;right:unset;top:unset;transform:unset}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-arw-cntr,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-arw-cntr{float:none;top:unset;right:unset;transform:unset;margin-top:-2px;position:relative}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-cat-header,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-cat-header{flex:1;margin:0 .5rem}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-tgl,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-tgl{position:relative;transform:none;right:0;top:0;float:none}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-chkbox{position:relative;margin:0 .5rem}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox label,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-chkbox label{padding:0}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox label::before,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-chkbox label::before{position:relative}#onetrust-pc-sdk .ot-vs-config .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk ul.ot-subgrps .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk #ot-pc-lst .ot-vs-list .ot-vnd-item .ot-acc-hdr .ot-chkbox input,#onetrust-pc-sdk .ot-accordion-layout.ot-checkbox-consent .ot-acc-hdr .ot-chkbox input{position:absolute;cursor:pointer;width:100%;height:100%;opacity:0;margin:0;top:0;left:0;z-index:1}#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp .ot-acc-hdr h5.ot-cat-header,#onetrust-pc-sdk .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp .ot-acc-hdr h4.ot-cat-header{margin:0}#onetrust-pc-sdk .ot-vs-config .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp h5{top:0;line-height:20px}#onetrust-pc-sdk .ot-vs-list{display:flex;flex-direction:column;padding:0;margin:.5rem 4px}#onetrust-pc-sdk .ot-vs-selc-all{display:flex;padding:0;float:unset;align-items:center;justify-content:flex-start}#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf{justify-content:flex-end}#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf.ot-caret-conf .ot-sel-all-chkbox{margin-right:48px}#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf .ot-sel-all-chkbox{margin:0;padding:0;margin-right:14px;justify-content:flex-end}#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf #ot-selall-vencntr.ot-chkbox,#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf #ot-selall-vencntr.ot-tgl{display:inline-block;right:unset;width:auto;height:auto;float:none}#onetrust-pc-sdk .ot-vs-selc-all.ot-toggle-conf #ot-selall-vencntr label{width:45px;height:25px}#onetrust-pc-sdk .ot-vs-selc-all .ot-sel-all-chkbox{margin-right:11px;margin-left:.75rem;display:flex;align-items:center}#onetrust-pc-sdk .ot-vs-selc-all .sel-all-hdr{margin:0 1.25rem;font-size:.812em;line-height:normal;text-align:center;word-break:break-word;word-wrap:break-word}#onetrust-pc-sdk .ot-vnd-list-cnt #ot-selall-vencntr.ot-chkbox{float:unset;right:0}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all.ot-toggle-conf.ot-caret-conf .ot-sel-all-chkbox{margin-right:50px}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all.ot-toggle-conf #ot-selall-vencntr label{width:35px;height:10px}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all.ot-toggle-conf .ot-sel-all-chkbox{justify-content:flex-end}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all .ot-sel-all-chkbox{right:unset;display:flex;align-items:center}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all .ot-sel-all-chkbox #ot-selall-vencntr.ot-chkbox{right:unset}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all .ot-sel-all-chkbox{margin-left:12px}#onetrust-pc-sdk.otPcTab .ot-vs-selc-all .ot-sel-all-chkbox .sel-all-hdr{margin:0 1rem}#onetrust-pc-sdk .ot-pgph-link{font-size:.813em;margin-top:5px;position:relative}#onetrust-pc-sdk .ot-pgph-link.ot-pgph-link-subgroup{margin-bottom:1rem}#onetrust-pc-sdk .ot-pgph-contr{margin:0 2.5rem}#onetrust-pc-sdk .ot-pgph-title{font-size:1.18rem;margin-bottom:2rem}#onetrust-pc-sdk .ot-pgph-desc{font-size:1rem;font-weight:400;margin-bottom:2rem;line-height:1.5rem}#onetrust-pc-sdk .ot-pgph-desc:not(:last-child):after{content:"";width:96%;display:block;margin:0 auto;padding-bottom:2rem;border-bottom:1px solid #e9e9e9}#onetrust-pc-sdk.otPcTab[dir=rtl] input~.ot-acc-hdr .ot-arw,#onetrust-pc-sdk.otPcTab[dir=rtl] #ot-back-arw{transform:rotate(180deg);-o-transform:rotate(180deg);-ms-transform:rotate(180deg);-webkit-transform:rotate(180deg)}#onetrust-pc-sdk.otPcTab[dir=rtl] input:checked~.ot-acc-hdr .ot-arw{transform:rotate(270deg);-o-transform:rotate(270deg);-ms-transform:rotate(270deg);-webkit-transform:rotate(270deg)}#onetrust-pc-sdk.otPcTab[dir=rtl] #ot-search-cntr svg{right:15px}#onetrust-pc-sdk.otPcTab[dir=rtl] .ot-chkbox label::after{transform:rotate(45deg);-webkit-transform:rotate(45deg);-o-transform:rotate(45deg);-ms-transform:rotate(45deg);border-left:0;border-right:3px solid}#onetrust-pc-sdk #close-pc-btn-handler.ot-close-icon{padding:0;background-color:rgba(0,0,0,0);border:none;margin:0}@media(max-width: 767px){#onetrust-pc-sdk{width:100%;border:none}#onetrust-pc-sdk .ot-optout-signal{margin:.625rem}#onetrust-pc-sdk .ot-sdk-container,#onetrust-pc-sdk .ot-sdk-container{padding:0;margin:0}#onetrust-pc-sdk .ot-title-cntr{width:75%}#onetrust-pc-sdk .ot-title-cntr #ot-pc-title{white-space:break-spaces;font-size:20px;overflow-x:visible;margin-left:10px}#onetrust-pc-sdk .ot-pc-logo{width:15%}#onetrust-pc-sdk .ot-pc-logo img{max-height:fit-content;font-size:10px}#onetrust-pc-sdk .ot-desc-cntr{margin:0;padding-top:20px;padding-right:20px;padding-bottom:15px;padding-left:20px;position:relative;left:auto}#onetrust-pc-sdk .ot-desc-cntr{margin-top:20px;margin-left:20px;padding:0;padding-bottom:10px}#onetrust-pc-sdk .ot-grps-cntr{max-height:none;overflow:hidden}#onetrust-pc-sdk #accept-recommended-btn-handler{float:none}}@media(min-width: 768px){#onetrust-pc-sdk.ot-tgl-with-label .ot-label-status{display:inline}#onetrust-pc-sdk.ot-tgl-with-label #ot-pc-lst .ot-label-status{display:none}#onetrust-pc-sdk.ot-tgl-with-label.ot-leg-opt-out .ot-pli-hdr{padding-right:8%}#onetrust-pc-sdk.ot-tgl-with-label .ot-cat-header{max-width:60%}#onetrust-pc-sdk.ot-tgl-with-label .ot-subgrp h4{max-width:58%}#onetrust-pc-sdk.ot-tgl-with-label .ot-subgrp-cntr ul.ot-subgrps li.ot-subgrp>h6{max-width:50%}#onetrust-pc-sdk.ot-tgl-with-label .ot-desc-cntr .ot-tgl-cntr:first-of-type,#onetrust-pc-sdk.ot-tgl-with-label .ot-cat-header+.ot-tgl{padding-left:15px}}@media(max-width: 640px){#onetrust-pc-sdk{height:100%}#onetrust-pc-sdk .ot-optout-signal{margin:.625rem}#onetrust-pc-sdk .ot-pc-header{padding:10px;width:calc(100% - 20px)}#onetrust-pc-sdk #ot-pc-content{overflow:auto}#onetrust-pc-sdk .ot-sdk-row .ot-sdk-columns{width:100%}#onetrust-pc-sdk .ot-desc-cntr{margin:0;overflow:hidden}#onetrust-pc-sdk .ot-desc-cntr{margin-left:10px;width:calc(100% - 15px);margin-top:5px;margin-bottom:5px}#onetrust-pc-sdk .ot-ven-hdr{max-width:80%}#onetrust-pc-sdk #ot-lst-cnt{width:calc(100% - 18px);padding-top:13px;padding-right:5px;padding-left:10px}#onetrust-pc-sdk .ot-grps-cntr{width:100%}#onetrust-pc-sdk .ot-pc-footer{max-height:300px}#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk #ot-pc-lst{height:calc(100% - 322px)}#onetrust-pc-sdk.ot-close-btn-link #close-pc-btn-handler{position:fixed;top:10px;right:15px}#onetrust-pc-sdk.ot-close-btn-link .ot-pc-header{padding-top:25px}#onetrust-pc-sdk.ot-close-btn-link #ot-pc-title{max-width:100%}}@media(max-width: 640px)and (orientation: portrait){#onetrust-pc-sdk #ot-pc-hdr{height:70px;padding:15px 0;width:100%}#onetrust-pc-sdk .ot-lst-subhdr{width:calc(100% - 15px);float:none;bottom:auto;display:inline-block;padding-top:8px;padding-left:15px}#onetrust-pc-sdk .ot-btn-subcntr{float:none}#onetrust-pc-sdk #ot-search-cntr{display:inline-block;width:calc(100% - 55px);position:relative}#onetrust-pc-sdk #ot-anchor{top:75px;right:30px}#onetrust-pc-sdk #ot-fltr-modal{top:81px}#onetrust-pc-sdk #ot-fltr-cntr{float:right;right:15px}#onetrust-pc-sdk #ot-lst-title{padding-left:15px}#onetrust-pc-sdk #ot-lst-cnt{height:auto;overflow:auto}#onetrust-pc-sdk .save-preference-btn-handler,#onetrust-pc-sdk #accept-recommended-btn-handler,#onetrust-pc-sdk .ot-pc-refuse-all-handler{width:calc(100% - 33px)}#onetrust-pc-sdk.ot-ftr-stacked .save-preference-btn-handler,#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr{max-width:none}#onetrust-pc-sdk.ot-ftr-stacked .ot-pc-footer button{margin:15px}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr button{min-width:none;max-width:none}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-subcntr button:nth-child(2){margin-top:15px}#onetrust-pc-sdk.ot-ftr-stacked .ot-btn-container button:not(:last-child){margin-bottom:0}}@media(max-width: 425px){#onetrust-pc-sdk .ot-pc-header .ot-pc-logo{width:15%}#onetrust-pc-sdk .ot-pc-header .ot-pc-logo img{max-height:fit-content;font-size:10px}#onetrust-pc-sdk .ot-title-cntr{width:75%}#onetrust-pc-sdk #ot-pc-lst .ot-acc-txt{padding-top:6px;padding-bottom:10px}#onetrust-pc-sdk #ot-pc-lst .ot-host-notice{float:left;margin-left:30px}#onetrust-pc-sdk #ot-pc-lst .ot-arw-cntr{float:none;display:inline}#onetrust-pc-sdk #ot-pc-lst .ot-ven-hdr{float:left;width:100%;max-width:85%}#onetrust-pc-sdk.ot-addtl-vendors #ot-pc-lst .ot-acc-cntr .ot-arw-cntr:first-of-type{float:right}#onetrust-pc-sdk #ot-pc-title{max-width:100%;white-space:break-spaces;font-size:20px;overflow-x:visible}#onetrust-pc-sdk .ot-subgrp-cntr li.ot-subgrp{margin-left:10px;width:calc(100% - 10px)}#onetrust-pc-sdk #ot-ven-lst .ot-tgl-cntr{width:auto;float:right}#onetrust-pc-sdk #ot-ven-lst .ot-arw-cntr{float:right}#onetrust-pc-sdk .ot-ven-hdr{max-width:47%}#onetrust-pc-sdk .ot-always-active-group .ot-tgl-cntr:first-of-type{max-width:none;padding-left:20px}}@media only screen and (max-height: 425px)and (max-width: 896px)and (orientation: landscape){#onetrust-pc-sdk{height:100%;width:100%;max-width:none}#onetrust-pc-sdk .ot-always-active-group .ot-tgl-cntr{max-width:none}#onetrust-pc-sdk .ot-pc-header{padding:10px;width:calc(100% - 20px);height:auto;min-height:20px}#onetrust-pc-sdk .ot-pc-header .ot-pc-logo{max-height:20px;width:15%}#onetrust-pc-sdk .ot-pc-header .ot-pc-logo img{max-height:fit-content;font-size:10px}#onetrust-pc-sdk .ot-title-cntr{width:75%}#onetrust-pc-sdk .ot-title-cntr #ot-pc-title{white-space:break-spaces;font-size:20px;overflow-x:visible}#onetrust-pc-sdk .ot-pc-footer{max-height:52px;overflow-y:auto}#onetrust-pc-sdk #ot-pc-lst{overflow-y:auto}#onetrust-pc-sdk #ot-pc-lst #ot-pc-hdr{height:auto}#onetrust-pc-sdk #ot-pc-lst #ot-pc-hdr #ot-pc-title{max-height:20px}#onetrust-pc-sdk #ot-pc-lst #ot-pc-hdr .ot-lst-subhdr{padding:10px 5px;float:none}#onetrust-pc-sdk #ot-pc-lst #ot-pc-hdr .ot-lst-subhdr #ot-fltr-cntr{margin-top:5px}#onetrust-pc-sdk #ot-pc-lst #ot-lst-cnt{overflow:visible}#onetrust-pc-sdk #ot-lst-cnt{height:auto;overflow:auto}#onetrust-pc-sdk #accept-recommended-btn-handler{float:right}#onetrust-pc-sdk .save-preference-btn-handler,#onetrust-pc-sdk #accept-recommended-btn-handler,#onetrust-pc-sdk .ot-pc-refuse-all-handler{width:auto}#onetrust-pc-sdk.ot-ftr-stacked #accept-recommended-btn-handler,#onetrust-pc-sdk.ot-ftr-stacked .ot-pc-refuse-all-handler{width:90%}#onetrust-pc-sdk #ot-pc-content,#onetrust-pc-sdk #ot-pc-lst{height:calc(100% - 120px)}#onetrust-pc-sdk.ot-shw-fltr .ot-lst-cntr{overflow:hidden}#onetrust-pc-sdk.ot-shw-fltr #ot-pc-lst{position:static}#onetrust-pc-sdk.ot-shw-fltr #ot-fltr-modal{top:0;width:100%;height:100%;max-height:none}#onetrust-pc-sdk.ot-shw-fltr #ot-fltr-modal>div{margin:0;box-sizing:initial;height:100%;max-height:none}#onetrust-pc-sdk.ot-shw-fltr #clear-filters-handler{padding-right:20px}#onetrust-pc-sdk.ot-shw-fltr #ot-anchor{display:none !important}#onetrust-pc-sdk .ot-pc-footer button{margin:10px}}@media(max-width: 425px),(max-width: 896px)and (max-height: 425px)and (orientation: landscape){#onetrust-pc-sdk .ot-pc-header{padding-right:20px}#onetrust-pc-sdk .ot-pc-logo{margin-left:0px;margin-top:5px;width:150px}#onetrust-pc-sdk .ot-close-icon{width:44px;height:44px;background-size:12px}#onetrust-pc-sdk .ot-grp-hdr1{float:right;padding-right:10px}#onetrust-pc-sdk .ot-grp-hdr1+.ot-vlst-cntr{padding-top:10px}}@media only screen and (max-height: 610px){#onetrust-pc-sdk{max-height:100%}}@media(max-width: 425px)and (orientation: landscape){#onetrust-pc-sdk .ot-pc-header #ot-pc-title{font-size:10px}}
            #onetrust-consent-sdk #onetrust-pc-sdk,
                #onetrust-consent-sdk #ot-search-cntr,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-switch.ot-toggle,
                #onetrust-consent-sdk #onetrust-pc-sdk ot-grp-hdr1 .checkbox,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-title:after
                ,#onetrust-consent-sdk #onetrust-pc-sdk #ot-sel-blk,
                        #onetrust-consent-sdk #onetrust-pc-sdk #ot-fltr-cnt,
                        #onetrust-consent-sdk #onetrust-pc-sdk #ot-anchor {
                    background-color: #FFFFFF;
                }
               
            #onetrust-consent-sdk #onetrust-pc-sdk h3,
                #onetrust-consent-sdk #onetrust-pc-sdk h4,
                #onetrust-consent-sdk #onetrust-pc-sdk h5,
                #onetrust-consent-sdk #onetrust-pc-sdk h6,
                #onetrust-consent-sdk #onetrust-pc-sdk p,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-ven-lst .ot-ven-opts p,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-desc,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-title,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-li-title,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-sel-all-hdr span,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-info,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-fltr-modal #modal-header,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-checkbox label span,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-sel-blk p,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-lst-title h3,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst .back-btn-handler p,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst .ot-ven-name,
                #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-lst #ot-ven-lst .consent-category,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-inactive-leg-btn,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-label-status,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-chkbox label span,
                #onetrust-consent-sdk #onetrust-pc-sdk #clear-filters-handler,
                #onetrust-consent-sdk #onetrust-pc-sdk .ot-optout-signal
                {
                    color: #222221;
                }
             #onetrust-consent-sdk #onetrust-pc-sdk .privacy-notice-link,
                    #onetrust-consent-sdk #onetrust-pc-sdk .ot-pgph-link,
                    #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler,
                    #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler + a,
                    #onetrust-consent-sdk #onetrust-pc-sdk .category-host-list-handler,
                    #onetrust-consent-sdk #onetrust-pc-sdk .ot-ven-link,
                    #onetrust-consent-sdk #onetrust-pc-sdk .ot-ven-legclaim-link,
                    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-name a,
                    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-acc-hdr .ot-host-expand,
                    #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-info a,
                    #onetrust-consent-sdk #onetrust-pc-sdk #ot-pc-content #ot-pc-desc .ot-link-btn,
                    #onetrust-consent-sdk #onetrust-pc-sdk .ot-vnd-serv .ot-vnd-item .ot-vnd-info a,
                    #onetrust-consent-sdk #onetrust-pc-sdk #ot-lst-cnt .ot-vnd-info a
                    {
                        color: #3860BE;
                    }
            #onetrust-consent-sdk #onetrust-pc-sdk .category-vendors-list-handler:hover { text-decoration: underline;}
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-acc-grpcntr.ot-acc-txt,
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-acc-txt .ot-subgrp-tgl .ot-switch.ot-toggle
             {
                background-color: #eed484;
            }
             #onetrust-consent-sdk #onetrust-pc-sdk #ot-host-lst .ot-host-info,
                    #onetrust-consent-sdk #onetrust-pc-sdk .ot-acc-txt .ot-ven-dets
                            {
                                background-color: #eed484;
                            }
        #onetrust-consent-sdk #onetrust-pc-sdk
            button:not(#clear-filters-handler):not(.ot-close-icon):not(#filter-btn-handler):not(.ot-remove-objection-handler):not(.ot-obj-leg-btn-handler):not([aria-expanded]):not(.ot-link-btn),
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-active-leg-btn {
                background-color: #222221;border-color: #222221;
                color: #FFFFFF;
            }
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-active-menu {
                border-color: #222221;
            }
            
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-remove-objection-handler{
                background-color: transparent;
                border: 1px solid transparent;
            }
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-leg-btn-container .ot-inactive-leg-btn {
                background-color: #FFFFFF;
                color: #78808E; border-color: #78808E;
            }
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-tgl input:focus + .ot-switch, .ot-switch .ot-switch-nob, .ot-switch .ot-switch-nob:before,
            #onetrust-pc-sdk .ot-checkbox input[type="checkbox"]:focus + label::before,
            #onetrust-pc-sdk .ot-chkbox input[type="checkbox"]:focus + label::before {
                outline-color: #222221;
                outline-width: 1px;
            }
            #onetrust-pc-sdk .ot-host-item > button:focus, #onetrust-pc-sdk .ot-ven-item > button:focus {
                border: 1px solid #222221;
            }
            #onetrust-consent-sdk #onetrust-pc-sdk *:focus,
            #onetrust-consent-sdk #onetrust-pc-sdk .ot-vlst-cntr > a:focus {
               outline: 1px solid #222221;
            }#onetrust-consent-sdk #onetrust-pc-sdk .category-menu-switch-handler {
                    background-color: #ffffff
                }#onetrust-consent-sdk #onetrust-pc-sdk .ot-active-menu {
                    background-color: #eed484
                }#onetrust-consent-sdk #onetrust-pc-sdk .category-menu-switch-handler {
                    background-color: #ffffff
                }#onetrust-consent-sdk #onetrust-pc-sdk .ot-active-menu {
                    background-color: #eed484
                }#onetrust-pc-sdk .ot-vlst-cntr .ot-ext-lnk,  #onetrust-pc-sdk .ot-ven-hdr .ot-ext-lnk{
                    background-image: url('https://cdn.cookielaw.org/logos/static/ot_external_link.svg');
                }
            .ot-sdk-cookie-policy{font-family:inherit;font-size:16px}.ot-sdk-cookie-policy.otRelFont{font-size:1rem}.ot-sdk-cookie-policy h3,.ot-sdk-cookie-policy h4,.ot-sdk-cookie-policy h6,.ot-sdk-cookie-policy p,.ot-sdk-cookie-policy li,.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy th,.ot-sdk-cookie-policy #cookie-policy-description,.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}.ot-sdk-cookie-policy h4{font-size:1.2em}.ot-sdk-cookie-policy h6{font-size:1em;margin-top:2em}.ot-sdk-cookie-policy th{min-width:75px}.ot-sdk-cookie-policy a,.ot-sdk-cookie-policy a:hover{background:#fff}.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}.ot-sdk-cookie-policy .ot-mobile-border{display:none}.ot-sdk-cookie-policy section{margin-bottom:2em}.ot-sdk-cookie-policy table{border-collapse:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy{font-family:inherit;font-size:1rem}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h3,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h4,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title{color:dimgray}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup{margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group-desc,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-table-header,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td{font-size:.9em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td span,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td a{font-size:inherit}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group{font-size:1em;margin-bottom:.6em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-title{margin-bottom:1.2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy>section{margin-bottom:1em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th{min-width:75px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a:hover{background:#fff}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead{background-color:#f6f6f4;font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-mobile-border{display:none}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy section{margin-bottom:2em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li{list-style:disc;margin-left:1.5em}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-subgroup ul li h4{display:inline-block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{border-collapse:inherit;margin:auto;border:1px solid #d7d7d7;border-radius:5px;border-spacing:initial;width:100%;overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border-bottom:1px solid #d7d7d7;border-right:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr th:last-child,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr td:last-child{border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:25%}.ot-sdk-cookie-policy[dir=rtl]{text-align:left}#ot-sdk-cookie-policy h3{font-size:1.5em}@media only screen and (max-width: 530px){.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) table,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tbody,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) th,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td,.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{display:block}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) thead tr{position:absolute;top:-9999px;left:-9999px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr{margin:0 0 1em 0}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd),.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) tr:nth-child(odd) a{background:#f6f6f4}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td{border:none;border-bottom:1px solid #eee;position:relative;padding-left:50%}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{position:absolute;height:100%;left:6px;width:40%;padding-right:10px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) .ot-mobile-border{display:inline-block;background-color:#e4e4e4;position:absolute;height:100%;top:0;left:45%;width:2px}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) td:before{content:attr(data-label);font-weight:bold}.ot-sdk-cookie-policy:not(#ot-sdk-cookie-policy-v2) li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table{overflow:hidden}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table td{border:none;border-bottom:1px solid #d7d7d7}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tbody,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{display:block}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-host,#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table .ot-cookies-type{width:auto}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy tr{margin:0 0 1em 0}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{height:100%;width:40%;padding-right:10px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td:before{content:attr(data-label);font-weight:bold}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li{word-break:break-word;word-wrap:break-word}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy thead tr{position:absolute;top:-9999px;left:-9999px;z-index:-9999}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td{border-bottom:1px solid #d7d7d7;border-right:0px}#ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table tr:last-child td:last-child{border-bottom:0px}}
                
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h5,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy h6,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy li,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy p,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy a,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy span,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy td,
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-description {
                        color: #222221;
                    }
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy th {
                        color: #222221;
                    }
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy .ot-sdk-cookie-policy-group {
                        color: #222221;
                    }
                    
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy #cookie-policy-title {
                            color: #222221;
                        }
                    
            
                    #ot-sdk-cookie-policy-v2.ot-sdk-cookie-policy table th {
                            background-color: #EED484;
                        }
                    
            #onetrust-pc-sdk #ot-pc-desc {
    clear: both;
    width: 100%;
    font-size: .912em;
    line-height: 1.5;
    margin-bottom: 25px;
}.ot-floating-button__front{background-image:url('https://cdn.cookielaw.org/logos/static/ot_persistent_cookie_icon.png')}</style><script src="https://bat.bing.com/p/action/56292612.js" type="text/javascript" async="" data-ueto="ueto_700b64bf1e"></script><link rel="stylesheet" type="text/css" href="/layouts/system/VisitorIdentificationCSS.aspx?tstamp=1756479318063"><script type="text/javascript" class="optanon-category-C0002">
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:2787946,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script><script async="" src="https://static.hotjar.com/c/hotjar-2787946.js?sv=6"></script></head>
<body>

	<!-- Google Tag Manager (noscript) -->
	<noscript>
		<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KJGHWFR"
				height="0" width="0" style="display:none;visibility:hidden"></iframe>
	</noscript>
	<!-- End Google Tag Manager (noscript) -->



    


<header class="msc-header c-black msc-header--sticky" :class="{ 'msc-header--opened' : opened, 'msc-header--sticky' : sticky }" x-data="mscHeader()" x-init="init()" data-open-menu-callback="false" @scroll.window="onScroll()" data-sticky="true">
    <div class="grid-container full">
        <div class="grid-x msc-header__container">
            <div class="cell small-5 msc-header__container__left">
                




<div class="msc-header__menu">
    <button class="msc-header__menu-burger" @click="toggleMenu()" :class="{ 'active': opened }">
        <span></span>
        <span></span>
        <span></span>
    </button>
    <div class="msc-navbar" :class="{ 'opened' : opened }">
        <div class="msc-navbar__steps" @click.away="closeMenu()">
            <div class="msc-navbar__step1 opened" :class="{ 'bg-white' : opened, 'opened' : step2 }">
                    <nav class="msc-navbar__nav" x-ref="navmenu" aria-label="Main Menu" data-selectedpage1="" data-selectedpage2="" data-selectedpage3="" data-firstpage1="solutions" data-firstpage2="shipping solutions">
                        <ul class="msc-navbar__ul">
                                    <li class="msc-navbar__item">
                                            <a class="msc-navbar__link" :class="{ 'active' : step2 &amp;&amp; page1 === 'solutions' }" @click="showStep2('solutions')">
                                                Solutions
                                            </a>
                                            <div class="msc-navbar__step2 msc-navbar__step2--image " :class="[step2 &amp;&amp; page1 === 'solutions' ? 'active' : '' , expand ? 'expand' : '']">
                                                <div class="msc-navbar__step2-top">
                                                    <div class="top">
                                                        <a class="title">Solutions</a>
                                                    </div>

                                                    <button class="msc-navbar__steps-back" @click="step2 = false">
                                                        <span class="msc-icon-arrow-back"></span>
                                                        <span class="desc">Back</span>
                                                    </button>
                                                </div>

                                                <div class="msc-navbar__step2-content">
                                                    <nav class="msc-navbar__nav" aria-label="Solutions">
                                                        <ul class="msc-navbar__ul">
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('shipping solutions')" :class="{ 'active' : page2 === 'shipping solutions' }">
                                                                            <span> Shipping Solutions</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'shipping solutions' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Shipping Solutions">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/shipping-solutions" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'shipping solutions' }">All Shipping Solutions</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/dry-cargo" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'dry cargo' }">Dry Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/reefer-cargo" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'reefer cargo' }">Reefer Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/project-cargo" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'project cargo' }">Project Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/liquid-cargo-solution" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'liquid cargo' }">Liquid Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/our-trade-services" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'trade services' }">Trade Services</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                            <img src="/-/media/images/msc-cargo/vessels/2010/2/msc18015651.jpg?rev=238b05002582457f862bd1c89da799dc" alt="">
                                                                                                                                                                                    <p class="description">Working with MSC will plug you into a network of experts across 675 offices, planning 300 global routes to 520 ports in over 155 countries, and we’re committed to offering competitive rates and transit times.</p>
                                                                                    </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('inland solutions')" :class="{ 'active' : page2 === 'inland solutions' }">
                                                                            <span> Inland Transportation &amp; Logistics Solutions</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'inland solutions' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Inland Transportation &amp; Logistics Solutions">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/inland-solutions" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'inland' }">All Inland Transportation &amp; Logistics Solutions</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/intermodal" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'intermodal' }">Inland Transport</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/warehousing" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'warehousing and storage' }">Warehousing &amp; Storage</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('air cargo solution')" :class="{ 'active' : page2 === 'air cargo solution' }">
                                                                            <span> Air Cargo Solution</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'air cargo solution' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Air Cargo Solution">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/air-cargo-solution" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'about msc air cargo' }">About Air Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/air-cargo-solution/business-hub/air-cargo-schedule" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'air schedule' }">Air Cargo Schedule</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/air-cargo-solution/business-hub/air-cargo-tracking" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'air tracking' }">Air Cargo Tracking</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/air-cargo-solution/business-hub/air-cargo-quote" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'air quote' }">Get an Air Cargo Quotation</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/-/media/files/msc-cargo/msc-solutions/aircargo/policies/msc-air-cargo-information-moves-cargo.pdf?rev=7ed94186cd7b46bfa153c57240f999a0" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'information moves' }">Information Moves Cargo</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/air-cargo-solution/business-hub/policies" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'air policies' }">Air Cargo Policies</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="https://forms.office.com/r/32UDuVeJHE" target="_blank" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'air claims' }">Air Cargo Claims</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('digital solutions')" :class="{ 'active' : page2 === 'digital solutions' }">
                                                                            <span> Digital Business Solutions</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'digital solutions' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Digital Business Solutions">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/digital-solutions" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'digital' }">All Digital Business Solutions</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/digital-solutions/direct-integrations" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'direct integrations' }">Direct Integrations</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/digital-solutions/ebl" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'ebl' }">eBL</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/digital-solutions/smart-containers" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'smart containers' }">Smart Containers</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/digital-solutions/ireefer" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'ireefer' }">iReefer</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('peace of mind solutions')" :class="{ 'active' : page2 === 'peace of mind solutions' }">
                                                                            <span> Cargo Cover Solutions</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'peace of mind solutions' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Cargo Cover Solutions">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/peace-of-mind-solutions" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'peace' }">All Cargo Cover Solutions</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/extended-protection" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'extended protection' }">MSC Extended Protection</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/cargo-insurance" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cargo insurance' }">Marine Cargo Insurance</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('sensitive cargo solutions')" :class="{ 'active' : page2 === 'sensitive cargo solutions' }">
                                                                            <span> Sensitive Cargo Solutions</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'sensitive cargo solutions' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Sensitive Cargo Solutions">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/solutions/thermal-liner-solution" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'msc thermal liner solution' }">MSC Thermal Liner Solution</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                    </li>
                                    <li class="msc-navbar__item">
                                            <a class="msc-navbar__link" :class="{ 'active' : step2 &amp;&amp; page1 === 'industries' }" @click="showStep2('industries')">
                                                Industries
                                            </a>
                                            <div class="msc-navbar__step2 msc-navbar__step2--image " :class="[step2 &amp;&amp; page1 === 'industries' ? 'active' : '' , expand ? 'expand' : '']">
                                                <div class="msc-navbar__step2-top">
                                                    <div class="top">
                                                        <a class="title">Industries</a>
                                                    </div>

                                                    <button class="msc-navbar__steps-back" @click="step2 = false">
                                                        <span class="msc-icon-arrow-back"></span>
                                                        <span class="desc">Back</span>
                                                    </button>
                                                </div>

                                                <div class="msc-navbar__step2-content">
                                                    <nav class="msc-navbar__nav" aria-label="Industries">
                                                        <ul class="msc-navbar__ul">
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('agriculture')" :class="{ 'active' : page2 === 'agriculture' }">
                                                                            <span> Agriculture</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'agriculture' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Agriculture">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'overview' }">Overview</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/cashews" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cashews' }">Cashews</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/cocoa" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cocoa' }">Cocoa</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/coffee" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'coffee' }">Coffee</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/cotton" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cotton' }">Cotton</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/dried-fruits-and-nuts" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'dfn' }">Dried Fruits &amp; Nuts</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/rice" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'rice' }">Rice</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/sesame" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'sesame' }">Sesame</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/agriculture/sugar" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'sugar' }">Sugar</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('automotive')" :class="{ 'active' : page2 === 'automotive' }">
                                                                            <span> Automotive</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'automotive' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Automotive">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/automotive" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'overview' }">Overview</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/automotive/complete-cars" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'complete cars' }">New Cars</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/automotive/used-cars" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'used cars' }">Used Cars</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/automotive/car-parts" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'car parts' }">Car Parts</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('chemicals')" :class="{ 'active' : page2 === 'chemicals' }">
                                                                            <span> Chemicals, Petrochemicals &amp; Plastics</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'chemicals' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Chemicals, Petrochemicals &amp; Plastics">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/chemicals-and-petrochemicals" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'chemicals and petrochemicals' }">Chemicals &amp; Petrochemicals</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/plastics-and-rubber-products" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'plastics and rubber products' }">Plastics &amp; Rubber Products</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('food and beverages')" :class="{ 'active' : page2 === 'food and beverages' }">
                                                                            <span> Food &amp; Beverages</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'food and beverages' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Food &amp; Beverages">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'overview' }">Overview</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages/beverages" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'beverages' }">Beverages</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages/dairy-and-milk-products" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'dairy' }">Dairy</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages/meat" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'meat' }">Meat</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages/seafood" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'seafood' }">Seafood</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/food-and-beverages/tea" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'tea' }">Tea</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('fruits and vegetables')" :class="{ 'active' : page2 === 'fruits and vegetables' }">
                                                                            <span> Fruit &amp; Vegetables</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'fruits and vegetables' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Fruit &amp; Vegetables">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'fruits' }">Fruits</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/avocados" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'avocados' }">Avocados</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/bananas" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'bananas' }">Bananas</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/mangoes" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'mangoes' }">Mangoes</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/cherries" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cherries' }">Cherries</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/citrus" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'citrus' }">Citrus</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/fruits/vegetables" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'vegetables' }">Vegetables</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/industries/mining-and-minerals" target="" :class="{ 'active' : page2 === 'mining and minerals' }">
                                                                            <span> Mining &amp; Minerals</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/industries/pharma" target="" :class="{ 'active' : page2 === 'pharma' }">
                                                                            <span> Pharmaceuticals</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/industries/pulp-paper-and-forestry-products" target="" :class="{ 'active' : page2 === 'pulp paper and forestry products' }">
                                                                            <span> Pulp, Paper &amp; Forestry Products</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('retail')" :class="{ 'active' : page2 === 'retail' }">
                                                                            <span> Retail</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'retail' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Retail">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/retail" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'retail' }">Overview</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/retail/apparel" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'apparel' }">Apparel</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/retail/cosmetics" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'cosmetics' }">Cosmetics</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/industries/retail/white-goods-and-electronics" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'white goods' }">White goods &amp; Electronics</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                    </li>
                                    <li class="msc-navbar__item">
                                            <a href="/en/ebusiness" target="" class="msc-navbar__link" @click="showStep2('ebusiness')" :class="{ 'active' : step2 &amp;&amp; page1 === 'ebusiness' }">
                                                eBusiness
                                            </a>
                                    </li>
                                    <li class="msc-navbar__item">
                                            <a class="msc-navbar__link" :class="{ 'active' : step2 &amp;&amp; page1 === 'sustainability' }" @click="showStep2('sustainability')">
                                                Sustainability
                                            </a>
                                            <div class="msc-navbar__step2 msc-navbar__step2--image " :class="[step2 &amp;&amp; page1 === 'sustainability' ? 'active' : '' , expand ? 'expand' : '']">
                                                <div class="msc-navbar__step2-top">
                                                    <div class="top">
                                                        <a class="title">Sustainability</a>
                                                    </div>

                                                    <button class="msc-navbar__steps-back" @click="step2 = false">
                                                        <span class="msc-icon-arrow-back"></span>
                                                        <span class="desc">Back</span>
                                                    </button>
                                                </div>

                                                <div class="msc-navbar__step2-content">
                                                    <nav class="msc-navbar__nav" aria-label="Sustainability">
                                                        <ul class="msc-navbar__ul">
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability" target="" :class="{ 'active' : page2 === 'our purpose' }">
                                                                            <span> Our Purpose</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('enabling logistics decarbonisation')" :class="{ 'active' : page2 === 'enabling logistics decarbonisation' }">
                                                                            <span> Enabling Logistics Decarbonization</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'enabling logistics decarbonisation' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Enabling Logistics Decarbonization">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/sustainability/enabling-logistics-decarbonization" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'enabling logistics  decarbonisation' }">Decarbonizing Logistics</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/sustainability/enabling-logistics-decarbonization/journey-to-net-zero" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'journey to net zero' }">MSC Biofuel Solution</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/carbon-calculator" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'carbon calculator' }">MSC Carbon Calculator</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/fostering-inclusive-trade" target="" :class="{ 'active' : page2 === 'fostering inclusive trade' }">
                                                                            <span> Fostering Inclusive Trade</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/addressing-social-challenges" target="" :class="{ 'active' : page2 === 'addressing social challenges' }">
                                                                            <span> Addressing Social Challenges</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/blue-planet/whale-protection" target="" :class="{ 'active' : page2 === 'protecting whales' }">
                                                                            <span> Protecting Whales</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('sustainability report')" :class="{ 'active' : page2 === 'sustainability report' }">
                                                                            <span> Sustainability Report</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'sustainability report' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Sustainability Report">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="https://www.msc.com/en/sustainability#downloadreport" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'latest sustainability report' }">2024 Sustainability Report</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/partnerships" target="" :class="{ 'active' : page2 === 'partnerships' }">
                                                                            <span> Partnerships</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/certifications" target="" :class="{ 'active' : page2 === 'certifications' }">
                                                                            <span> Certifications</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" @click="showStep3('code of conduct')" :class="{ 'active' : page2 === 'code of conduct' }">
                                                                            <span> Code of Conduct</span>
                                                                        </a>
                                                                        <div class="msc-navbar__step3" :class="{ 'active' : page2 === 'code of conduct' }">
                                                                            <div class="msc-navbar__step3-content">
                                                                                <nav class="msc-navbar__nav" aria-label="Code of Conduct">
                                                                                    <ul class="msc-navbar__ul">
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/sustainability/msc-code-of-conduct" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'msc code of  business conduct' }">MSC Code of  Business Conduct</a>
                                                                                            </li>
                                                                                            <li class="msc-navbar__item">
                                                                                                <a href="/en/sustainability/msc-supplier-code-of-conduct" target="" class="msc-navbar__link" :class="{ 'active' : step3 &amp;&amp; page3 === 'supplier code of conduct' }">MSC Supplier Code of Conduct</a>
                                                                                            </li>
                                                                                    </ul>

                                                                                    <div class="msc-navbar__step3-picture">
                                                                                                                                                                            </div>
                                                                                </nav>
                                                                            </div>
                                                                        </div>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/msc-modern-slavery-transparency-statement" target="" :class="{ 'active' : page2 === 'msc modern slavery statements' }">
                                                                            <span> MSC Modern Slavery Transparency Statements</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                        <a class="msc-navbar__link" href="/en/sustainability/msc-ship-recycling-policy" target="" :class="{ 'active' : page2 === 'ship recycling policy' }">
                                                                            <span> MSC Ship Recycling Policy</span>
                                                                        </a>
                                                                </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                    </li>
                                    <li class="msc-navbar__item">
                                            <a class="msc-navbar__link" @click="showStep2('about us')" :class="{ 'active' : step2 &amp;&amp; page1 === 'about us' }">
                                                About Us
                                            </a>
                                            <div class="msc-navbar__step2 bg-light-grey" :class="{ 'active' : step2 &amp;&amp; page1 === 'about us' }">
                                                <div class="msc-navbar__step2-top">
                                                    <div class="top">
                                                        <a class="title">About Us</a>
                                                    </div>

                                                    <button class="msc-navbar__steps-back" @click="step2 = false">
                                                        <span class="msc-icon-arrow-back"></span>
                                                        <span class="desc">Back</span>
                                                    </button>
                                                </div>

                                                
                                                <div class="msc-navbar__step2-content" x-on:resize.window="getWidthBurgerMenuOpened()">
                                                    <nav class="msc-navbar__nav" aria-label="About Us">
                                                        <ul class="msc-navbar__ul">
                                                                <li class="msc-navbar__item">
                                                                    

                                                                        <a class="msc-navbar__link" href="/en/about-us/management" :class="{ 'active' : step2 &amp;&amp; page2 === 'management' }">
                                                                            <span> Management</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                    

                                                                        <a class="msc-navbar__link" href="/en/about-us/msc-foundation" :class="{ 'active' : step2 &amp;&amp; page2 === 'msc foundation' }">
                                                                            <span> MSC Foundation</span>
                                                                        </a>
                                                                </li>
                                                                <li class="msc-navbar__item">
                                                                    

                                                                        <a class="msc-navbar__link" href="/en/about-us/msc-group" :class="{ 'active' : step2 &amp;&amp; page2 === 'msc group' }">
                                                                            <span> MSC Group</span>
                                                                        </a>
                                                                </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                    </li>
                        </ul>
                    </nav>
                                    <nav class="msc-navbar__small" aria-label="Small Menu">
                        <ul class="msc-navbar__ul">
                                <li class="msc-navbar__item">
                                    <span class="msc-navbar__small-icon bg-primary">
                                            <span class="msc-icon-calendar"></span>
                                    </span>
<a href="/en/search-a-schedule" class="msc-navbar__link" title="Search a schedule">Schedules</a>                                </li>
                                <li class="msc-navbar__item">
                                    <span class="msc-navbar__small-icon bg-primary">
                                            <span class="msc-icon-marker"></span>
                                    </span>
<a href="/en/track-a-shipment" class="msc-navbar__link" title="MSC Suivi">Tracking</a>                                </li>
                                <li class="msc-navbar__item">
                                    <span class="msc-navbar__small-icon bg-primary">
                                            <span class="msc-icon-user"></span>
                                    </span>
<a href="https://mymsc.com/mymsc/" class="msc-navbar__link" rel="noopener noreferrer" target="_blank" title="Log in to myMSC ">myMSC</a>                                </li>
                        </ul>
                    </nav>
                                    <nav class="msc-navbar__info" aria-label="Information Menu">
                        <ul class="msc-navbar__ul">
                                <li class="msc-navbar__item ">
<a href="/en/newsroom" class="msc-navbar__link" title="MSC Newsroom">Newsroom</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/events" class="msc-navbar__link" title="Stay up to date with events and meet us there">Events</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/lp/blog" class="msc-navbar__link" title="Catching Waves with MSC Blogs and Articles">Blog</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/local-information" class="msc-navbar__link" title="MSC Local Information">Local information</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/careers" class="msc-navbar__link" title="Careers at MSC">Careers</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/contact-us" class="msc-navbar__link" title="Contact us">Contact us</a>                                </li>
                                <li class="msc-navbar__item ">
<a href="/en/tools" class="msc-navbar__link" title="MSC Tools">Tools</a>                                </li>
                        </ul>
                    </nav>

                <nav class="msc-navbar__languages" :class="{'fixed' : stepLangs}" aria-label="Languages Menu">
                    <button class="msc-link-arrow-down-icon" @click="showStepLangs()">
                        <span class="msc-icon-globe"></span>
                        <span>Language</span>
                        <span class="msc-icon-chevron"></span>
                    </button>

                    <div class="msc-navbar__languages-list" x-show="stepLangs" style="display: none;">

<ul>
                <li>
                    <a title="English" href="https://www.msc.com/en/track-a-shipment" class="active">ENGLISH</a>
                </li>
                <li>
                    <a title="Spanish" href="https://www.msc.com/es/track-a-shipment">ESPAÑOL</a>
                </li>
                <li>
                    <a title="Turkish" href="https://www.msc.com/tr/track-a-shipment">TÜRKÇE</a>
                </li>
                <li>
                    <a title="French" href="https://www.msc.com/fr/track-a-shipment">FRANÇAIS</a>
                </li>
                <li>
                    <a title="Russian" href="https://www.msc.com/ru/track-a-shipment">PУССКИЙ</a>
                </li>
                <li>
                    <a title="Portuguese" href="https://www.msc.com/pt/track-a-shipment">PORTUGUÊS</a>
                </li>
                <li>
                    <a title="Chinese" href="https://www.msc.com/zh/track-a-shipment">中文</a>
                </li>
                <li>
                    <a title="Arabic" href="https://www.msc.com/ar/track-a-shipment">العربية</a>
                </li>
                <li>
                    <a title="German" href="https://www.msc.com/de/track-a-shipment">DEUTSCH</a>
                </li>
                <li>
                    <a title="Japanese" href="https://www.msc.com/ja/track-a-shipment">日本語</a>
                </li>
                <li>
                    <a title="Korean" href="https://www.msc.com/ko/track-a-shipment">한국어</a>
                </li>
                <li>
                    <a title="Italian" href="https://www.msc.com/it/track-a-shipment">ITALIANO</a>
                </li>
</ul>                    </div>
                </nav>
            </div>
        </div>
    </div>
</div>


<div class="msc-header__container__left--ctas">
    <div class="msc-header__menu">

            </div>
    </div>            </div>
            <div class="cell small-2 msc-header__container__center">
                <div class="msc-header__logo" title="">
    <a href="/">
		<span class="msc-icon-msc"></span>
    </a>
</div>
            </div>
            <div class="cell small-5 msc-header__container__right">
                

<div class="msc-header__nav">
    <nav aria-label="Header Menu">
        <ul class="msc-header__nav-list">

<li>
	<button class="msc-header__nav-item msc-header__search" :class="{ 'msc-header__nav-item--active' : showGlobalSearch }" @click="showSearch()">
		<span class="msc-icon-search"></span>
		<span class="name">Search</span>
	</button>
	<nav x-show="showGlobalSearch" @click.away="showGlobalSearch = false" aria-label="Search Menu" style="display: none;">


<div class="msc-global-search" x-data="mscGlobalSearch()" x-init="init()" @click.away="search = ''" data-content-type="PageContent" :class="{ 'msc-search-autocomplete--focused' : focusInput }">
	<div class="msc-search-autocomplete">
		<div class="msc-search-autocomplete__field">
			<form action="/en/search" method="get">
				<button type="button" class="msc-cta-icon-simple msc-search-autocomplete__clear" :disabled="!focusInput" x-show="focusInput" @click="clearSearchField()" disabled="disabled" style="display: none;">
					<span class="msc-icon-close"></span>
				</button>

				<button class="msc-cta-icon-simple msc-search-autocomplete__search" :disabled="!focusInput" disabled="disabled">
					<span class="msc-icon-search"></span>
				</button>

				<input type="text" name="query" placeholder="Search" x-model="query" x-ref="searchTextfield" data-type="search" style="">
			</form>
		</div>
	</div>
</div>
	</nav>
</li>


<li>
    <a href="/en/track-a-shipment" class="msc-header__nav-item">
        <span class="msc-icon-marker"></span>
        <span class="name">Tracking</span>
    </a>
</li>


<li>
	<button class="msc-header__nav-item" :class="{ 'msc-header__nav-item--active' : stepLangs }" @click="showStepLangs()">
		<span class="msc-icon-globe"></span>
		<span class="name">EN</span>
	</button>

	<nav class="msc-navbar__languages" x-show="stepLangs" @click.away="stepLangs = false" aria-label="Languages Menu" style="display: none;">
        <ul>
                        <li>
                            <a title="English" href="https://www.msc.com/en/track-a-shipment" class="active">ENGLISH</a>
                        </li>
                        <li>
                            <a title="Spanish" href="https://www.msc.com/es/track-a-shipment">ESPAÑOL</a>
                        </li>
                        <li>
                            <a title="Turkish" href="https://www.msc.com/tr/track-a-shipment">TÜRKÇE</a>
                        </li>
                        <li>
                            <a title="French" href="https://www.msc.com/fr/track-a-shipment">FRANÇAIS</a>
                        </li>
                        <li>
                            <a title="Russian" href="https://www.msc.com/ru/track-a-shipment">PУССКИЙ</a>
                        </li>
                        <li>
                            <a title="Portuguese" href="https://www.msc.com/pt/track-a-shipment">PORTUGUÊS</a>
                        </li>
                        <li>
                            <a title="Chinese" href="https://www.msc.com/zh/track-a-shipment">中文</a>
                        </li>
                        <li>
                            <a title="Arabic" href="https://www.msc.com/ar/track-a-shipment">العربية</a>
                        </li>
                        <li>
                            <a title="German" href="https://www.msc.com/de/track-a-shipment">DEUTSCH</a>
                        </li>
                        <li>
                            <a title="Japanese" href="https://www.msc.com/ja/track-a-shipment">日本語</a>
                        </li>
                        <li>
                            <a title="Korean" href="https://www.msc.com/ko/track-a-shipment">한국어</a>
                        </li>
                        <li>
                            <a title="Italian" href="https://www.msc.com/it/track-a-shipment">ITALIANO</a>
                        </li>
        </ul>
	</nav>
</li>


            <li class="msc-header__nav-list-mymsc">
                <form action="/identity/externallogin?authenticationType=MSC&amp;ReturnUrl=%2fidentity%2fexternallogincallback%3fReturnUrl%3d%252f%26sc_site%3dMSCCargo%26authenticationSource%3dDefault&amp;sc_site=MSCCargo" method="post">
                    <button class="msc-header__nav-item msc-header__nav-item--last" x-on:click="deleteIsLoggedUserCookie()" type="submit">
                        <span class="msc-icon-user-black"></span>
                        <span class="name">myMSC</span>
                    </button>
                </form>
            </li>   


        </ul>
    </nav>
    </div>
            </div>
        </div>
    </div>
</header>

        <div class="msc-main" id="main">
            



    <script type="application/ld+json">
        {
        "@context": "https://schema.org/",
        "@id":"https://www.msc.com/en/track-a-shipment#BreadcrumbList",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type" : "ListItem",
                "position" : "1",
                "name" : "Track a shipment",
                "item" : "https://www.msc.com/en/track-a-shipment"
                    }
            
        ]
        }
    </script>

<section class="msc-breadcrumbs no-print  ">
    <div class="grid-container">
        <div class="grid-x grid-margin-x">
            <div class="cell small-12">
                    <div class="msc-line-separator msc-line-separator--w-big msc-line-separator--h-small bg-light-gray"></div>
                <nav aria-label="breadcrumbs">
                    <ol class="breadcrumb__list">
                                <li class="breadcrumb__item breadcrumb__item--current">
                                    <a href="https://www.msc.com/en/track-a-shipment" aria-current="page"><span>Track a shipment</span></a>
                                </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>


<div class="msc-flow-tracking separator--bottom-medium " x-data="mscFlowTracking()" x-init="init()" data-api-url="/api/feature/tools/TrackingInfo" data-placeholder-container="Enter a Container/Bill of Lading Number" data-placeholder-booking="Enter a Booking Number">

    <div class="grid-container">
            <div class="grid-x no-print">
                <div class="cell small-12">
                    <div class="msc-flow-tracking__heading">
                        <h1 class="msc-flow-tracking__title">Tracking</h1>
                    </div>
                        <form @submit.prevent="search" class="js-form" data-gtm-form-interact-id="0">
                            <input name="__RequestVerificationToken" type="hidden" value="token1" style="">
                            <div class="msc-flow-tracking__form no-print">
                                <div class="msc-flow-tracking__radio">
                                    <div class="msc-flow-tracking__radio-group">
                                        <input type="radio" id="containeradio" name="trackingMode" value="0" x-model="trackingMode" checked="" style="">
                                        <label for="containeradio">Container/Bill of Lading Number</label>
                                    </div>
                                    <div class="msc-flow-tracking__radio-group">
                                        <input type="radio" id="bookingradio" name="trackingMode" value="1" x-model="trackingMode" style="">
                                        <label for="bookingradio">Booking Number</label>
                                    </div>
                                </div>
                                <div class="msc-search-autocomplete msc-search-autocomplete--focused" :class="{ 'msc-search-autocomplete--focused' : focusInput }">
                                    <div class="msc-search-autocomplete__field">
                                        <button type="button" class="msc-cta-icon-simple msc-search-autocomplete__clear" @click="clearInput()" x-show="focusInput" :disabled="!focusInput">
                                            <span class="msc-icon-close"></span>
                                        </button>
                                        <button class="msc-cta-icon-simple msc-search-autocomplete__search" :disabled="trackingNumber.length &lt; 1 &amp;&amp; !focusInput">
                                            <span class="msc-icon-search"></span>
                                        </button>
                                        <input type="text" id="trackingNumber" x-model="trackingNumber" data-type="search" :placeholder="getPlaceholderText()" placeholder="Enter a Container/Bill of Lading Number" data-gtm-form-interact-field-id="0" style="">
                                    </div>
                                </div>
                            </div>
                        </form>
                </div>
            </div>
            <div class="grid-x">
                <div class="cell small-12">
                    <div class="msc-flow-tracking__print">
                        <div class="msc-flow-tracking__print-header">
                            <div class="msc-flow-tracking__print-logo">
                                <img src="/_sc/Assets/images/logo.png" alt="MSC">
                            </div>
                                <h2>Tracking</h2>
                            <p>https://www.msc.com/en/track-a-shipment</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid-x">
                <div class="cell small-12">
                    <div class="msc-flow-tracking__wrapper" x-ref="results">
                        <template x-if="isLoading">
                            <div class="msc-flow-tracking__loader">
                                <div class="msc-loader">
    <div class="msc-loader__container">
        <!--?xml version="1.0" encoding="utf-8" ?-->
        <svg version="1.1" id="Livello_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 470 470" style="enable-background:new 0 0 470 470;" xml:space="preserve">
        <clipPath id="clip-mask-wave-one">
        <rect id="mask-1" width="358" height="25" x="56" y="300"></rect>
      </clipPath>
        <clipPath id="clip-mask-wave-two">
        <rect id="mask-2" width="328" height="25" x="73" y="329"></rect>
      </clipPath>
        <g class="boat">
        <!-- boat -->
        <path d="M99.72,228.88c-2.21,0-4-1.79-4-4v-48.15c0-2.21,1.79-4,4-4h36.44c2.21,0,4,1.79,4,4c0,2.21-1.79,4-4,4h-32.44v44.15
          C103.72,227.09,101.93,228.88,99.72,228.88"></path>

        <path d="M378.01,228.02c-2.21,0-4-1.79-4-4v-43.28H206.6c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h171.41c2.21,0,4,1.79,4,4v47.28
          C382.01,226.23,380.22,228.02,378.01,228.02"></path>

        <path d="M380.44,300.9c-0.8,0-1.61-0.24-2.31-0.74c-1.8-1.28-2.23-3.78-0.95-5.58c11.7-16.5,26.31-45.33,27.38-53.5
          c-12.77-0.68-81.23-1.33-160.01-1.49c-84.43-0.17-146.4,0.3-160.39,1.19c0,0.03,0,0.06,0,0.09c-0.33,9.09-1.34,36.76,0.93,45.09
          c0.58,2.13-0.68,4.33-2.81,4.91c-2.14,0.58-4.33-0.68-4.91-2.81c-2.3-8.43-1.85-29.8-1.2-47.49c0.06-1.7,0.11-2.86,0.11-3.28
          c0-1.22,0.58-2.42,1.53-3.17c1.54-1.23,2.31-1.84,53.98-2.29c28.17-0.24,67.41-0.33,110.5-0.25c44.27,0.08,160.35,0.57,165.82,1.81
          c1.84,0.42,3.29,1.71,3.99,3.53c0.73,1.9,2.42,6.33-8.85,28.95c-5.89,11.82-13.56,24.9-19.53,33.32
          C382.92,300.32,381.69,300.9,380.44,300.9"></path>

        <path d="M306.31,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C310.31,224.54,308.52,226.34,306.31,226.34"></path>

        <path d="M341.05,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C345.05,224.54,343.26,226.34,341.05,226.34"></path>

        <path d="M237.48,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C241.48,224.54,239.69,226.34,237.48,226.34"></path>

        <path d="M271.9,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C275.9,224.54,274.11,226.34,271.9,226.34"></path>

        <path d="M151.96,226.34c-2.21,0-4-1.79-4-4v-73.8c0-2.21,1.79-4,4-4h38.44c2.21,0,4,1.79,4,4v73.17c0,2.21-1.79,4-4,4s-4-1.79-4-4
          v-69.17h-30.44v69.8C155.96,224.54,154.17,226.34,151.96,226.34"></path>

        <path d="M157.88,137.94c-2.21,0-4-1.79-4-4v-16.25c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v16.25
          C161.88,136.15,160.09,137.94,157.88,137.94"></path>

        <path d="M378.01,208.22H206.6c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h171.41c2.21,0,4,1.79,4,4
          C382.01,206.43,380.22,208.22,378.01,208.22"></path>

        <path d="M136.16,208.22H99.72c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h36.44c2.21,0,4,1.79,4,4
          C140.16,206.43,138.37,208.22,136.16,208.22"></path>

        <path d="M99.72,257.29H81.5c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h18.22c2.21,0,4,1.79,4,4
          C103.72,255.5,101.93,257.29,99.72,257.29"></path>

        <path d="M281.46,265.25h-4.77c-0.3-1.04-0.69-1.82-1.17-2.35c-0.84-0.94-2.1-1.41-3.77-1.41c-1.7,0-3.03,0.66-4.02,1.99
          c-0.98,1.33-1.46,3.21-1.46,5.64c0,2.44,0.52,4.26,1.55,5.47c1.03,1.21,2.35,1.82,3.94,1.82c1.64,0,2.88-0.51,3.74-1.56
          c0.48-0.56,0.87-1.4,1.18-2.52h4.73c-0.41,2.37-1.45,4.29-3.13,5.77c-1.68,1.48-3.83,2.22-6.45,2.22c-3.24,0-5.79-1-7.65-3
          c-1.86-2.01-2.78-4.76-2.78-8.27c0-3.79,1.06-6.71,3.17-8.76c1.83-1.78,4.17-2.68,7.01-2.68c3.8,0,6.57,1.2,8.33,3.59
          C280.87,262.55,281.39,263.89,281.46,265.25"></path>

        <path d="M233.31,263.84c0,0.78,0.37,1.35,1.11,1.74c0.48,0.25,1.48,0.55,3.03,0.9l4,0.89c1.75,0.4,3.06,0.92,3.94,1.57
          c1.36,1.02,2.04,2.5,2.04,4.43c0,1.99-0.8,3.63-2.42,4.94c-1.61,1.3-3.88,1.97-6.82,1.97c-3,0-5.37-0.65-7.08-1.94
          c-1.72-1.29-2.58-3.06-2.58-5.32h4.55c0.15,0.99,0.44,1.73,0.87,2.22c0.8,0.9,2.14,1.34,4.06,1.34c1.15,0,2.08-0.12,2.8-0.36
          c1.36-0.45,2.04-1.29,2.04-2.52c0-0.71-0.33-1.26-1-1.66c-0.67-0.38-1.72-0.72-3.16-1.02l-2.47-0.51c-2.43-0.52-4.09-1.06-5-1.66
          c-1.54-1-2.3-2.56-2.3-4.7c0-1.94,0.75-3.55,2.25-4.84c1.51-1.29,3.72-1.93,6.62-1.93c2.43,0,4.51,0.6,6.23,1.82
          c1.72,1.21,2.62,2.97,2.7,5.28h-4.58c-0.09-1.31-0.7-2.24-1.84-2.78c-0.76-0.36-1.71-0.55-2.84-0.55c-1.26,0-2.27,0.24-3.02,0.71
          C233.69,262.34,233.31,263,233.31,263.84"></path>

        <path d="M214.57,279.71h-4.44v-14.67c0-0.43,0-1.01,0.02-1.77c0.01-0.76,0.02-1.35,0.02-1.76l-4.32,18.2h-4.63l-4.29-18.2
          c0,0.41,0,1,0.02,1.76c0.01,0.76,0.01,1.35,0.01,1.77v14.67h-4.44v-21.68h6.93l4.15,17.05l4.11-17.05h6.86V279.71z"></path>

        </g>
        <!-- wave one -->
        <g clip-path="url(#clip-mask-wave-one)">
        <g class="wave-one">
        <path d="M384.63,325.45c-11.22,0-21.9-4.35-29.89-12.07c-8,7.72-18.67,12.07-29.89,12.07c-11.27,0-21.98-4.39-29.99-12.17
            c-7.99,7.78-18.64,12.17-29.8,12.17c-11.22,0-21.9-4.35-29.89-12.07c-8.01,7.72-18.72,12.07-30,12.07
            c-11.27,0-21.98-4.39-29.99-12.17c-8,7.78-18.69,12.17-29.91,12.17c-11.21,0-21.86-4.34-29.86-12.04
            c-8.01,7.7-18.73,12.04-30.04,12.04c-12.7,0-24.7-5.58-32.91-15.3c-1.43-1.69-1.21-4.21,0.48-5.64c1.69-1.43,4.21-1.21,5.64,0.47
            c6.69,7.92,16.46,12.46,26.8,12.46c10.46,0,20.3-4.54,26.99-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.29,0,20.03-4.54,26.72-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.41,0,20.22-4.54,26.91-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.37,0.52,3.13,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.37,0,19.82-4.43,26.6-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c0.76-0.9,1.88-1.42,3.05-1.42c1.18,0,2.37,0.52,3.13,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c1.43-1.68,3.95-1.9,5.64-0.47c1.69,1.42,1.9,3.95,0.47,5.64
            C409.33,319.88,397.33,325.45,384.63,325.45"></path>

          </g>
          </g>
        <!-- wave two -->
        <g clip-path="url(#clip-mask-wave-two)">
        <g class="wave-two">
        <path transform="translate(25 32) scale(0.9)" d="M384.63,356.31c-11.23,0-21.9-4.35-29.89-12.07c-8,7.72-18.67,12.07-29.89,12.07c-11.27,0-21.98-4.39-29.99-12.17
              c-7.99,7.78-18.64,12.17-29.8,12.17c-11.23,0-21.9-4.35-29.89-12.07c-8.01,7.72-18.72,12.07-30,12.07
              c-11.27,0-21.98-4.39-29.99-12.17c-8,7.78-18.69,12.17-29.91,12.17c-11.21,0-21.86-4.34-29.86-12.04
              c-8.01,7.7-18.73,12.04-30.04,12.04c-12.7,0-24.7-5.58-32.91-15.3c-1.43-1.69-1.21-4.21,0.48-5.64c1.69-1.42,4.21-1.21,5.64,0.48
              c6.69,7.92,16.46,12.46,26.8,12.46c10.46,0,20.3-4.54,26.99-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.29,0,20.03-4.54,26.72-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.46,12.46,26.8,12.46c10.41,0,20.22-4.54,26.91-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.37,0.52,3.13,1.42
              c6.69,7.92,16.46,12.46,26.8,12.46c10.37,0,19.82-4.43,26.6-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c0.76-0.9,1.88-1.42,3.05-1.42c1.18,0,2.37,0.52,3.13,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c1.43-1.68,3.95-1.9,5.64-0.48c1.69,1.42,1.9,3.95,0.47,5.64
              C409.33,350.74,397.34,356.31,384.63,356.31"></path>

        </g>
        </g>
     </svg>
    </div>
</div>

                            </div>
                        </template>

                        
                            <div class="msc-flow-tracking__loader">
                                <div class="msc-loader">
    <div class="msc-loader__container">
        <!--?xml version="1.0" encoding="utf-8" ?-->
        <svg version="1.1" id="Livello_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 470 470" style="enable-background:new 0 0 470 470;" xml:space="preserve">
        <clipPath id="clip-mask-wave-one">
        <rect id="mask-1" width="358" height="25" x="56" y="300"></rect>
      </clipPath>
        <clipPath id="clip-mask-wave-two">
        <rect id="mask-2" width="328" height="25" x="73" y="329"></rect>
      </clipPath>
        <g class="boat">
        <!-- boat -->
        <path d="M99.72,228.88c-2.21,0-4-1.79-4-4v-48.15c0-2.21,1.79-4,4-4h36.44c2.21,0,4,1.79,4,4c0,2.21-1.79,4-4,4h-32.44v44.15
          C103.72,227.09,101.93,228.88,99.72,228.88"></path>

        <path d="M378.01,228.02c-2.21,0-4-1.79-4-4v-43.28H206.6c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h171.41c2.21,0,4,1.79,4,4v47.28
          C382.01,226.23,380.22,228.02,378.01,228.02"></path>

        <path d="M380.44,300.9c-0.8,0-1.61-0.24-2.31-0.74c-1.8-1.28-2.23-3.78-0.95-5.58c11.7-16.5,26.31-45.33,27.38-53.5
          c-12.77-0.68-81.23-1.33-160.01-1.49c-84.43-0.17-146.4,0.3-160.39,1.19c0,0.03,0,0.06,0,0.09c-0.33,9.09-1.34,36.76,0.93,45.09
          c0.58,2.13-0.68,4.33-2.81,4.91c-2.14,0.58-4.33-0.68-4.91-2.81c-2.3-8.43-1.85-29.8-1.2-47.49c0.06-1.7,0.11-2.86,0.11-3.28
          c0-1.22,0.58-2.42,1.53-3.17c1.54-1.23,2.31-1.84,53.98-2.29c28.17-0.24,67.41-0.33,110.5-0.25c44.27,0.08,160.35,0.57,165.82,1.81
          c1.84,0.42,3.29,1.71,3.99,3.53c0.73,1.9,2.42,6.33-8.85,28.95c-5.89,11.82-13.56,24.9-19.53,33.32
          C382.92,300.32,381.69,300.9,380.44,300.9"></path>

        <path d="M306.31,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C310.31,224.54,308.52,226.34,306.31,226.34"></path>

        <path d="M341.05,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C345.05,224.54,343.26,226.34,341.05,226.34"></path>

        <path d="M237.48,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C241.48,224.54,239.69,226.34,237.48,226.34"></path>

        <path d="M271.9,226.34c-2.21,0-4-1.79-4-4v-43.69c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v43.69
          C275.9,224.54,274.11,226.34,271.9,226.34"></path>

        <path d="M151.96,226.34c-2.21,0-4-1.79-4-4v-73.8c0-2.21,1.79-4,4-4h38.44c2.21,0,4,1.79,4,4v73.17c0,2.21-1.79,4-4,4s-4-1.79-4-4
          v-69.17h-30.44v69.8C155.96,224.54,154.17,226.34,151.96,226.34"></path>

        <path d="M157.88,137.94c-2.21,0-4-1.79-4-4v-16.25c0-2.21,1.79-4,4-4c2.21,0,4,1.79,4,4v16.25
          C161.88,136.15,160.09,137.94,157.88,137.94"></path>

        <path d="M378.01,208.22H206.6c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h171.41c2.21,0,4,1.79,4,4
          C382.01,206.43,380.22,208.22,378.01,208.22"></path>

        <path d="M136.16,208.22H99.72c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h36.44c2.21,0,4,1.79,4,4
          C140.16,206.43,138.37,208.22,136.16,208.22"></path>

        <path d="M99.72,257.29H81.5c-2.21,0-4-1.79-4-4c0-2.21,1.79-4,4-4h18.22c2.21,0,4,1.79,4,4
          C103.72,255.5,101.93,257.29,99.72,257.29"></path>

        <path d="M281.46,265.25h-4.77c-0.3-1.04-0.69-1.82-1.17-2.35c-0.84-0.94-2.1-1.41-3.77-1.41c-1.7,0-3.03,0.66-4.02,1.99
          c-0.98,1.33-1.46,3.21-1.46,5.64c0,2.44,0.52,4.26,1.55,5.47c1.03,1.21,2.35,1.82,3.94,1.82c1.64,0,2.88-0.51,3.74-1.56
          c0.48-0.56,0.87-1.4,1.18-2.52h4.73c-0.41,2.37-1.45,4.29-3.13,5.77c-1.68,1.48-3.83,2.22-6.45,2.22c-3.24,0-5.79-1-7.65-3
          c-1.86-2.01-2.78-4.76-2.78-8.27c0-3.79,1.06-6.71,3.17-8.76c1.83-1.78,4.17-2.68,7.01-2.68c3.8,0,6.57,1.2,8.33,3.59
          C280.87,262.55,281.39,263.89,281.46,265.25"></path>

        <path d="M233.31,263.84c0,0.78,0.37,1.35,1.11,1.74c0.48,0.25,1.48,0.55,3.03,0.9l4,0.89c1.75,0.4,3.06,0.92,3.94,1.57
          c1.36,1.02,2.04,2.5,2.04,4.43c0,1.99-0.8,3.63-2.42,4.94c-1.61,1.3-3.88,1.97-6.82,1.97c-3,0-5.37-0.65-7.08-1.94
          c-1.72-1.29-2.58-3.06-2.58-5.32h4.55c0.15,0.99,0.44,1.73,0.87,2.22c0.8,0.9,2.14,1.34,4.06,1.34c1.15,0,2.08-0.12,2.8-0.36
          c1.36-0.45,2.04-1.29,2.04-2.52c0-0.71-0.33-1.26-1-1.66c-0.67-0.38-1.72-0.72-3.16-1.02l-2.47-0.51c-2.43-0.52-4.09-1.06-5-1.66
          c-1.54-1-2.3-2.56-2.3-4.7c0-1.94,0.75-3.55,2.25-4.84c1.51-1.29,3.72-1.93,6.62-1.93c2.43,0,4.51,0.6,6.23,1.82
          c1.72,1.21,2.62,2.97,2.7,5.28h-4.58c-0.09-1.31-0.7-2.24-1.84-2.78c-0.76-0.36-1.71-0.55-2.84-0.55c-1.26,0-2.27,0.24-3.02,0.71
          C233.69,262.34,233.31,263,233.31,263.84"></path>

        <path d="M214.57,279.71h-4.44v-14.67c0-0.43,0-1.01,0.02-1.77c0.01-0.76,0.02-1.35,0.02-1.76l-4.32,18.2h-4.63l-4.29-18.2
          c0,0.41,0,1,0.02,1.76c0.01,0.76,0.01,1.35,0.01,1.77v14.67h-4.44v-21.68h6.93l4.15,17.05l4.11-17.05h6.86V279.71z"></path>

        </g>
        <!-- wave one -->
        <g clip-path="url(#clip-mask-wave-one)">
        <g class="wave-one">
        <path d="M384.63,325.45c-11.22,0-21.9-4.35-29.89-12.07c-8,7.72-18.67,12.07-29.89,12.07c-11.27,0-21.98-4.39-29.99-12.17
            c-7.99,7.78-18.64,12.17-29.8,12.17c-11.22,0-21.9-4.35-29.89-12.07c-8.01,7.72-18.72,12.07-30,12.07
            c-11.27,0-21.98-4.39-29.99-12.17c-8,7.78-18.69,12.17-29.91,12.17c-11.21,0-21.86-4.34-29.86-12.04
            c-8.01,7.7-18.73,12.04-30.04,12.04c-12.7,0-24.7-5.58-32.91-15.3c-1.43-1.69-1.21-4.21,0.48-5.64c1.69-1.43,4.21-1.21,5.64,0.47
            c6.69,7.92,16.46,12.46,26.8,12.46c10.46,0,20.3-4.54,26.99-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.29,0,20.03-4.54,26.72-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.41,0,20.22-4.54,26.91-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.37,0.52,3.13,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.37,0,19.82-4.43,26.6-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
            c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c0.76-0.9,1.88-1.42,3.05-1.42c1.18,0,2.37,0.52,3.13,1.42
            c6.69,7.92,16.46,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c1.43-1.68,3.95-1.9,5.64-0.47c1.69,1.42,1.9,3.95,0.47,5.64
            C409.33,319.88,397.33,325.45,384.63,325.45"></path>

          </g>
          </g>
        <!-- wave two -->
        <g clip-path="url(#clip-mask-wave-two)">
        <g class="wave-two">
        <path transform="translate(25 32) scale(0.9)" d="M384.63,356.31c-11.23,0-21.9-4.35-29.89-12.07c-8,7.72-18.67,12.07-29.89,12.07c-11.27,0-21.98-4.39-29.99-12.17
              c-7.99,7.78-18.64,12.17-29.8,12.17c-11.23,0-21.9-4.35-29.89-12.07c-8.01,7.72-18.72,12.07-30,12.07
              c-11.27,0-21.98-4.39-29.99-12.17c-8,7.78-18.69,12.17-29.91,12.17c-11.21,0-21.86-4.34-29.86-12.04
              c-8.01,7.7-18.73,12.04-30.04,12.04c-12.7,0-24.7-5.58-32.91-15.3c-1.43-1.69-1.21-4.21,0.48-5.64c1.69-1.42,4.21-1.21,5.64,0.48
              c6.69,7.92,16.46,12.46,26.8,12.46c10.46,0,20.3-4.54,26.99-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.29,0,20.03-4.54,26.72-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.46,12.46,26.8,12.46c10.41,0,20.22-4.54,26.91-12.46c0.76-0.9,1.88-1.42,3.06-1.42c1.18,0,2.37,0.52,3.13,1.42
              c6.69,7.92,16.46,12.46,26.8,12.46c10.37,0,19.82-4.43,26.6-12.46c0.76-0.9,1.88-1.42,3.06-1.42h0.27c1.18,0,2.3,0.52,3.06,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c0.76-0.9,1.88-1.42,3.05-1.42c1.18,0,2.37,0.52,3.13,1.42
              c6.69,7.92,16.45,12.46,26.8,12.46c10.34,0,20.11-4.54,26.8-12.46c1.43-1.68,3.95-1.9,5.64-0.48c1.69,1.42,1.9,3.95,0.47,5.64
              C409.33,350.74,397.34,356.31,384.63,356.31"></path>

        </g>
        </g>
     </svg>
    </div>
</div>

                            </div>
                        <template x-if="hasResults">
                            <div>
                                <div class="msc-flow-tracking__results">
                                    <template x-for="(result, index) in results.BillOfLadings">
                                        <div :data-index="index">




<div class="msc-flow-tracking__result" x-data="mscFlowTrackingResult()" x-init="init()">
    <div class="msc-flow-tracking__subheading">
        <h3 class="msc-flow-tracking__subtitle">
            <span x-text="title"></span>
            <span x-text="trackingNumber"></span>
            <span class="msc-flow-tracking__subtitle-info" x-show="type == 'Booking Number'">
                1 Bill of Lading found
            </span>
        </h3>
        <div class="msc-flow-tracking__actions no-print">
            <button @click="print()">
                <span class="msc-icon-print"></span>
            </button>
        </div>
    </div>
    <div class="msc-flow-tracking__details">
        <ul>
            <li>
                <!-- Container -->
                <div x-show="type === 'Container'">
                    <span class="msc-flow-tracking__details-heading">Container Number</span>
                    <span class="msc-flow-tracking__details-value" x-text="getContainerNumber()"></span>
                </div>

                <!-- Bill Of Lading -->
                <div x-show="type !== 'Container'">
                    <span class="msc-flow-tracking__details-heading">Bill of Lading:</span>
                    <span class="msc-flow-tracking__details-value" x-text="result.BillOfLadingNumber"></span>
                    <span class="msc-flow-tracking__details-subtitle">
                        <span>(</span><span x-text="result.NumberOfContainers"></span>
                        <span x-show="result.NumberOfContainers == 1">Container</span>
                        <span x-show="result.NumberOfContainers == 0 || result.NumberOfContainers &gt; 1">Containers</span><span>)</span>
                    </span>
                </div>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Shipped From
                </span>
                <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.ShippedFrom)"></span>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Port of Load
                </span>
                <span class="msc-flow-tracking__details-value">
                    <span x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.PortOfLoad)"></span>
                    <span x-show="result.GeneralTrackingInfo.PortOfLoadLocationCode" x-cloak="">(</span><span x-text="result.GeneralTrackingInfo.PortOfLoadLocationCode"></span><span x-show="result.GeneralTrackingInfo.PortOfLoadLocationCode" x-cloak="">)</span>
                </span>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Port of Discharge
                </span>
                <span class="msc-flow-tracking__details-value">
                    <span x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.PortOfDischarge)"></span>
                    <span x-show="result.GeneralTrackingInfo.PortOfDischargeLocationCode" x-cloak="">(</span><span x-text="result.GeneralTrackingInfo.PortOfDischargeLocationCode"></span><span x-show="result.GeneralTrackingInfo.PortOfDischargeLocationCode" x-cloak="">)</span>
                </span>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Shipped To
                </span>
                <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(result.GeneralTrackingInfo.ShippedTo)"> </span>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Transhipment
                </span>
                <template x-for="item in result.GeneralTrackingInfo.Transshipments">
                    <span class="msc-flow-tracking__details-value" x-text="convertToSentenceCaseLocation(item)"></span>
                </template>
            </li>
            <li>
                <span class="msc-flow-tracking__details-heading">
                    Price Calculation Date*
                </span>
                <span class="msc-flow-tracking__details-value" x-text="result.GeneralTrackingInfo.PriceCalculationDate"></span>
            </li>
        </ul>
    </div>

    <div class="msc-flow-tracking__containers">
        <p class="disclaimer" x-text="priceCalculationLabel"></p>
        <h4 class="subtitle">Containers</h4>
        <template x-for="(result, index) in result.ContainersInfo">
            <div :data-index="index" :data-bill-index="billIndex">

<div class="msc-flow-tracking__container" x-data="mscFlowTrackingContainer()" x-init="init()" data-copy-message="copied" x-on:resize.window="onResize()">
    <div class="msc-flow-tracking__bar" :class="{'open': open, 'complete' : isComplete}" x-on:click="more()">
        <div class="msc-flow-tracking__content">
            <div class="msc-flow-tracking__cell msc-flow-tracking__cell--one msc-flow-tracking__cell--first">
                <div class="msc-flow-tracking__cell-flex">
                    <div class="msc-flow-tracking__data">
                        <span class="msc-icon-container-empty"></span>
                        <div>
                            <span class="data-heading">
                                Container
                            </span>
                            <span class="data-value" x-text="container.ContainerNumber"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                <div class="msc-flow-tracking__cell-flex">
                    <div class="msc-flow-tracking__data">
                        <span class="msc-icon-type"></span>
                        <div>
                            <span class="data-heading">
                                Type
                            </span>
                            <span class="data-value" x-text="container.ContainerType"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three msc-flow-tracking__cell--delivered">
                <div class="msc-flow-tracking__cell-flex">
                    <div class="msc-flow-tracking__data">
                        <span class="msc-icon-marker"></span>
                        <div>
                            <span class="data-heading">
                                Latest move
                            </span>
                            <span class="data-value" x-text="container.LatestMove"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four">
                <div class="msc-flow-tracking__cell-flex">
                    <div class="msc-flow-tracking__data" x-show="container.PodEtaDate">
                        <span class="msc-icon-transit-time"></span>
                        <div>
                            <span class="data-heading">
                                POD ETA
                            </span>
                            <span class="data-value" x-text="container.PodEtaDate"></span>
                        </div>
                    </div>

                    <span class="msc-flow-tracking__complete" x-show="isComplete">
                        <span class="msc-icon-checkmark icon-negative-round-border"></span>
                    </span>
                </div>
            </div>
            <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--last">
                <div class="msc-flow-tracking__cell-flex">
                    <button class="msc-cta-icon-simple msc-flow-tracking__more-button no-print" :disabled="orderedEvents.length === 0">
                        <span class="msc-icon-plus icon-negative-round-border" x-show="isComplete &amp;&amp; !open"></span>
                        <span class="msc-icon-minus icon-negative-round-border" x-show="isComplete &amp;&amp; open"></span>
                        <span class="msc-icon-plus icon-secondary " x-show="!isComplete &amp;&amp; !open"></span>
                        <span class="msc-icon-minus icon-secondary " x-show="!isComplete &amp;&amp; open"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Tracking Part -->
    <div x-show="open" class="msc-flow-tracking__tracking" x-transition:enter="fade-in-enter" x-transition:enter-start="fade-in-enter-start" x-transition:enter-end="fade-in-enter-end" x-transition:leave="fade-in-leave" x-transition:leave-start="fade-in-leave-start" x-transition:leave-end="fade-in-leave-end" x-cloak="">

        <div class="msc-flow-tracking__header show-for-large">
            <div class="msc-flow-tracking__cell--one">
            </div>
            <div class="msc-flow-tracking__cell--two">
                <span class="data-heading">Date</span>
            </div>
            <div class="msc-flow-tracking__cell--three">
                <span class="data-heading">Location</span>
            </div>
            <div class="msc-flow-tracking__cell--four">
                <span class="data-heading">Description</span>
            </div>
            <div class="msc-flow-tracking__cell--five">
                <span class="data-heading data-heading--break">Empty/Laden/Vessel/Voyage</span>
            </div>
            <div class="msc-flow-tracking__cell--six">
                <span class="data-heading">Equipment handling facility name</span>
            </div>
        </div>
        <div class="msc-flow-tracking__steps">
            <template x-for="(event, idx) in orderedEvents">
                <div class="msc-flow-tracking__port">
                    <div class="msc-flow-tracking__step" :class="dynamicStepClass(idx).container">
                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--one msc-flow-tracking__cell--first show-for-large">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <span class="data-step" :class="dynamicStepClass(idx).step"></span>
                                </div>
                            </div>
                        </div>

                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <span class="data-step mobile hide-for-large" :class="dynamicStepClass(idx).step"></span>
                                    <span class="data-value" x-text="event.Date">
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <span class="data-value text-capitalize" x-text="convertToSentenceCaseLocation(event.Location)"></span>
                                </div>
                            </div>
                        </div>
                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four msc-flow-tracking__cell--container">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <span class="data-value" x-text="event.Description"></span>
                                </div>
                            </div>
                        </div>
                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five msc-flow-tracking__cell--container">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <template x-if="event.Detail">
                                        <div>
                                        <span class="data-value">
                                            <span x-text="eventDetailsLabel(event.Detail)"></span>
                                        </span>
                                        <template x-if="event.Vessel &amp;&amp; (event.Vessel.Flag || event.Vessel.IMO || event.Vessel.Built)">
                                            <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                                                <div class="msc-flow-tracking__tooltip">
                                                    <button x-on:mouseover="!isMobile &amp;&amp; showTooltip('vesselInfo', idx)" x-on:mouseout="!isMobile &amp;&amp; closeTooltip('vesselInfo', idx)" x-on:click="isMobile &amp;&amp; toggleTooltip('vesselInfo', idx)" class="msc-flow-tracking__info msc-flow-tracking__info--details">
                                                        <span class="msc-icon-info"></span>
                                                    </button>
                                                    <div class="msc-flow-tracking__tooltip-box vessel" x-on:mouseover="!isMobile &amp;&amp; showTooltip('vesselInfo', idx)" x-on:mouseout="!isMobile &amp;&amp; closeTooltip('vesselInfo', idx)" x-show="isActiveTooltip('vesselInfo', idx)">
                                                        <div class="msc-flow-tracking__tooltip-box__info">
                                                            <template x-if="isMobile">
                                                                <div class="msc-flow-tracking__tooltip-box__close">
                                                                    <button class="msc-flow-tracking__info msc-flow-tracking__info--details" x-on:click="closeTooltip('vesselInfo', idx)">
                                                                        <span class="msc-icon-close"></span>
                                                                    </button>
                                                                </div>
                                                            </template>
                                                            <ul>
                                                                <template x-if="event.Vessel.FlagName">
                                                                    <li>
                                                                        <div class="tooltip-row">
                                                                            <span class="data-tooltip-title">Flag</span>
                                                                            <span class="data-tooltip-value" x-text="event.Vessel.FlagName"></span>
                                                                        </div>
                                                                    </li>
                                                                </template>
                                                                <template x-if="event.Vessel.IMO">
                                                                    <li>
                                                                        <div class="tooltip-row">
                                                                            <span class="data-tooltip-title">IMO</span>
                                                                            <span class="data-tooltip-value" x-text="event.Vessel.IMO"></span>
                                                                        </div>
                                                                    </li>
                                                                </template>
                                                                <template x-if="event.Vessel.Built">
                                                                    <li>
                                                                        <div class="tooltip-row">
                                                                            <span class="data-tooltip-title">Built</span>
                                                                            <span class="data-tooltip-value" x-text="event.Vessel.Built"></span>
                                                                        </div>
                                                                    </li>
                                                                </template>
                                                            </ul>
                                                        </div>
                                                         <template x-if="!isMobile">
                                                           <div class="msc-flow-tracking__tooltip-box__copy">
                                                            <button class="msc-flow-tracking__info msc-flow-tracking__info--details" x-on:click="copyToClipboard(event.Vessel, 'vessel')">
                                                              <span class="msc-icon-copy"></span>
                                                            </button>
                                                          </div>
                                                        </template> 
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six msc-flow-tracking__cell--container">
                            <div class="msc-flow-tracking__cell-flex">
                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                    <template x-if="!event.EquipmentHandling">
                                        <span class="data-value">N.A</span>
                                    </template>
                                    <template x-if="event.EquipmentHandling">
                                        <div class="msc-flow-tracking__data msc-flow-tracking__data--tooltip">
                                            <span class="data-value text-capitalize" x-text="convertToSentenceCase(event.EquipmentHandling.Name)"></span>

                                            <div class="msc-flow-tracking__tooltip">
                                                <button x-on:mouseover="!isMobile &amp;&amp; showTooltip('info', idx)" x-on:mouseout="!isMobile &amp;&amp; closeTooltip('info', idx)" x-on:click="isMobile &amp;&amp; toggleTooltip('info', idx)" class="msc-flow-tracking__info msc-flow-tracking__info--details" x-show="event.EquipmentHandling.Smdg || event.EquipmentHandling.Code || event.EquipmentHandling.Bic">
                                                    <span class="msc-icon-info"></span>
                                                </button>
                                                <div class="msc-flow-tracking__tooltip-box" x-on:mouseover="!isMobile &amp;&amp; showTooltip('info', idx)" x-on:mouseout="!isMobile &amp;&amp; closeTooltip('info', idx)" x-show="isActiveTooltip('info', idx)">
                                                    <div class="msc-flow-tracking__tooltip-box__info">
                                                        <template x-if="isMobile">
                                                            <div class="msc-flow-tracking__tooltip-box__close">
                                                                <button class="msc-flow-tracking__info msc-flow-tracking__info--details" x-on:click="closeTooltip('info', idx)">
                                                                    <span class="msc-icon-close"></span>
                                                                </button>
                                                            </div>
                                                        </template>
                                                        <ul>
                                                            <template x-if="event.EquipmentHandling.Smdg">
                                                                <li>
                                                                    <div class="tooltip-row">
                                                                        <span class="data-tooltip-title">SMDG</span>
                                                                        <span class="data-tooltip-value" x-text="event.EquipmentHandling.Smdg"></span>
                                                                    </div>
                                                                </li>
                                                            </template>

                                                            <template x-if="event.EquipmentHandling.Bic">
                                                                <li>
                                                                    <div class="tooltip-row">
                                                                        <span class="data-tooltip-title">BIC</span>
                                                                        <span class="data-tooltip-value" x-text="event.EquipmentHandling.Bic"></span>
                                                                    </div>
                                                                </li>
                                                            </template>

                                                            <template x-if="event.EquipmentHandling.Code">
                                                                <li>
                                                                    <div class="tooltip-row">
                                                                        <span class="data-tooltip-title" x-text="event.EquipmentHandling.CodeType"></span>
                                                                        <span class="data-tooltip-value" x-text="event.EquipmentHandling.Code"></span>
                                                                    </div>
                                                                </li>
                                                            </template>
                                                        </ul>
                                                    </div>
                                                    <template x-if="!isMobile">
                                                        <div class="msc-flow-tracking__tooltip-box__copy">
                                                            <button class="msc-flow-tracking__info msc-flow-tracking__info--details" x-on:click="copyToClipboard(event.EquipmentHandling, 'info')">
                                                                <span class="msc-icon-copy"></span>
                                                            </button>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>

                    <template x-if="showToggleButton(event.IntermediaryPortCalls)">
                        <div class="msc-flow-tracking__intermediary">

                            <button class="msc-cta-icon msc-cta-icon--show no-print" x-on:click="showIntermediatePorts(event.Order)" x-show="!intermediatePortsOpened.includes(event.Order)">
                                <span class="msc-icon-plus"></span>
                                <span class="tit">Show all*</span>
                            </button>

                            <button class="msc-cta-icon msc-cta-icon--show open no-print" x-on:click="closeIntermediatePorts(event.Order)" x-show="!isLoading &amp;&amp; intermediatePortsOpened.includes(event.Order) &amp;&amp; !noIntermediatesPortsIds.includes(event.Order)">
                                <span class="msc-icon-minus"></span>
                                <span class="tit">Show less*</span>
                            </button>

                            <button class="msc-cta-icon msc-cta-icon--show open no-print" x-show="!isLoading &amp;&amp; noIntermediatesPortsIds.includes(event.Order)">
                                <span class="msc-icon-minus"></span>
                                <span class="tit">No intermediate port calls</span>
                            </button>

                            <div x-show="!isLoading &amp;&amp; intermediatePortsOpened.includes(event.Order) &amp;&amp; resultsIntermediatePorts.length &gt; 0">
                                <template x-for="({order, data}) in resultsIntermediatePorts">
                                    <div>
                                        <template x-if="order === event.Order">
                                            <div>

                                                <template x-for="port in data">
                                                    <div class="msc-flow-tracking__step msc-flow-tracking__step--grey msc-flow-tracking__step--intermediate">
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--one msc-flow-tracking__cell--first show-for-large">
                                                            <div class="msc-flow-tracking__cell-flex">
                                                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                                                    <span class="data-step grey"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--two">
                                                            <div class="msc-flow-tracking__cell-flex">
                                                                <div class="msc-flow-tracking__data msc-flow-tracking__data--tracking">
                                                                    <span class="data-step grey mobile hide-for-large"></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--three">
                                                            <div class="msc-flow-tracking__cell-flex">
                                                                <div class="msc-flow-tracking__data">
                                                                    <span class="data-value">
                                                                        <span class="text-capitalize" x-text="convertToSentenceCase(port.LocationName)"></span><span>,</span>
                                                                        <span x-text="port.Country"></span>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--four">
                                                            <div class="msc-flow-tracking__cell-flex">
                                                                <div class="msc-flow-tracking__data">
                                                                    <span class="data-value">
                                                                        <span>ETD:</span> <span x-text="port.Etd"></span>
                                                                    </span>
                                                                    <span class="data-value">
                                                                        <span>ETA:</span> <span x-text="port.Eta"></span>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--five"></div>
                                                        <div class="msc-flow-tracking__cell msc-flow-tracking__cell--six"></div>
                                                    </div>
                                                </template>
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>
            </template>
        </div>
    </div>
</div>
            </div>
        </template>
    </div>
</div>
                                        </div>
                                    </template>
                                </div>
                                <div class="msc-flow-tracking-result__footer">
                                    <p x-text="trackingResultsLabel"></p>
                                        <p>*<u>Show all</u> Intermediate Port Calls.<br>If you have any questions regarding the results of your shipment tracking results, please contact your local MSC team at the number below. By using the shipment tracking service you agree to the <a href="/en/terms-and-conditions">terms of use</a>&nbsp;of msc.com.</p>
                                </div>
                            </div>
                        </template>

                        <template x-if="!isSuccess">
                            <div class="msc-flow-tracking__error">
                                <span class="msc-icon-dry-container-logo-empty"></span>
                                <p x-text="errorMessage"></p>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
    </div>
</div>



<div x-data="mscTrackingSubscriptionBanner()" x-init="init()" x-show="showBanner" data-api-url="/api/feature/PageContent/SetCrisisCookie?CrisisBannerID=" data-handle-form-url="/api/feature/PageContent/HandleTrackingSubscriptionForm" data-tracking-banner-id="6C122B8D4C434A2C8B79EE31056131A4" data-ot-id="16FBF7F7B4BB4CCD9C1A1E4F826C5FCB" x-on:resize.window="detectResize()" data-promotional-banner="true" data-global-crisis-banner="false" data-close-button="True" data-show-subscribe-to-newsletter="False" data-force-banner="False" data-is-trigger-cta="False" x-ref="trackingSubscriptionBanner" style="display: none;">

    <template x-if="!isTriggerCta">
        <div x-on:click="$store.banners.openBanner()" class="msc-tracking-subscription-banner__trigger separator--bottom-medium">
            <div class="msc-tracking-subscription-banner__trigger-text bg-primary">
                <div class="msc-content-text">
                    <h2 class="msc-content-title">
                        Subscribe to Track &amp; Trace Notifications
                    </h2>
                    <div class="msc-content-description">
                        <p>
                            Get cargo updates in your inbox about the status of your cargo. Just search for your shipment and click "Notify me" below the results.
                        </p>
                    </div>
                </div>
                <div class="msc-tracking-subscription-banner__trigger-cta">
                    <span class="msc-link-arrow-simple">
                        Subscribe now!
                    </span>
                </div>
            </div>
            <div class="msc-tracking-subscription-banner__trigger-image">
                <img class="desktop lazy" data-src="https://assets.msc.com/api/public/content/ad48ba7aba8341d38c7988d43b73c956?v=b752cc46" alt="Dry containers">
                <img class="mobile lazy" data-src="https://assets.msc.com/api/public/content/ad48ba7aba8341d38c7988d43b73c956?v=b752cc46" alt="Dry containers">
            </div>
        </div>
    </template>

    
        <div x-on:click="$store.banners.openBanner()" class="msc-tracking-subscription-banner__trigger separator--bottom-medium">
            <div class="msc-tracking-subscription-banner__trigger-text bg-primary">
                <div class="msc-content-text">
                    <h2 class="msc-content-title">
                        Subscribe to Track &amp; Trace Notifications
                    </h2>
                    <div class="msc-content-description">
                        <p>
                            Get cargo updates in your inbox about the status of your cargo. Just search for your shipment and click "Notify me" below the results.
                        </p>
                    </div>
                </div>
                <div class="msc-tracking-subscription-banner__trigger-cta">
                    <span class="msc-link-arrow-simple">
                        Subscribe now!
                    </span>
                </div>
            </div>
            <div class="msc-tracking-subscription-banner__trigger-image">
                <img class="desktop lazy" data-src="https://assets.msc.com/api/public/content/ad48ba7aba8341d38c7988d43b73c956?v=b752cc46" alt="Dry containers">
                <img class="mobile lazy" data-src="https://assets.msc.com/api/public/content/ad48ba7aba8341d38c7988d43b73c956?v=b752cc46" alt="Dry containers">
            </div>
        </div>
    <template x-if="isTriggerCta">
        <div class="msc-tracking-subscription-banner__trigger-cta-version separator--bottom-medium">
            <button class="msc-cta msc-cta--black " x-on:click="$store.banners.openBanner()" :disabled="isBannerOpen &amp;&amp; !forceBanner">
                Receive updates on your shipment
            </button>
        </div>
    </template>

    <template x-if="isBannerOpen">
        <div class="msc-tracking-subscription-banner bg-white">
            <div>
                <template x-if="hasCloseButton">
                    <button class="msc-cta-icon-simple msc-cta--close" x-ref="btnSetCookie" x-on:click="handleBannerAction('close')">
                        <span class="msc-icon-close" :class="{'color-black': onResponse }"></span>
                    </button>
                </template>

                <template x-if="!onResponse">
                    <div class="grid-x msc-tracking-subscription-banner__wrapper">
                        <div class="msc-tracking-subscription-banner__title-and-description">
                            <template x-if="!isMobile &amp;&amp; !isTablet">
                                <div>
                                    


                                </div>
                            </template>

                            <div class="msc-title msc-title--no-dec msc-title--left no-print msc-tracking-subscription-banner__title-and-description-text">
<h1 class="msc-tracking-subscription-banner__title-and-description-title msc-heading">Stay informed!</h1>                                                                <div class="msc-tracking-subscription-banner__title-and-description-description">
                                    Subscribe for notifications and keep track of your shipments easily!
                                </div>
                            </div>
                        </div>

                        <form x-on:submit.prevent="sendFormData()" id="form6C122B8D4C434A2C8B79EE31056131A4" class="msc-tracking-subscription-banner__form">
                            <label class="d-none" for="firstName6C122B8D4C434A2C8B79EE31056131A4">First name</label>
                            <label class="d-none" for="lastName6C122B8D4C434A2C8B79EE31056131A4">Last name</label>
                            <label class="d-none" for="email6C122B8D4C434A2C8B79EE31056131A4">Email</label>
                            <label class="d-none" for="companyName6C122B8D4C434A2C8B79EE31056131A4">Company name</label>

                            <div class="msc-tracking-subscription-banner__form-input-container">
                                <input required="" type="text" name="firstName" id="firstName6C122B8D4C434A2C8B79EE31056131A4" placeholder="First name">
                                <input required="" type="text" name="lastName" id="lastName6C122B8D4C434A2C8B79EE31056131A4" placeholder="Last name">
                            </div>

                            <div class="msc-tracking-subscription-banner__form-input-container">
                                <input required="" type="email" name="email" id="email6C122B8D4C434A2C8B79EE31056131A4" placeholder="Email">
                                <input required="" type="text" name="companyName" id="companyName6C122B8D4C434A2C8B79EE31056131A4" placeholder="Company name">
                            </div>

                            <div class="msc-tracking-subscription-banner__form-grid">
                                <div class="msc-tracking-subscription-banner__form-grid-subscribe">
                                    <template x-if="showSubscribeToNewsletter">
                                        <div>
                                            <input type="checkbox" id="subscribeCheckbox6C122B8D4C434A2C8B79EE31056131A4" name="subscribeCheckbox" value="true">
                                            <label for="subscribeCheckbox6C122B8D4C434A2C8B79EE31056131A4">Subscribe to our Newsletter</label>
                                        </div>
                                    </template>
                                    <div class="msc-tracking-subscription-banner__form-grid-subscribe-disclaimer">
                                        <p>By clicking 'Sign up' you agree to the <strong><a href="/en/privacy-policy">Privacy Policy</a></strong> and you agree to be contacted by MSC in regards to your query.</p>
<p>You can opt-out of such communication at any time.</p>
                                    </div>
                                </div>

                                <div class="msc-tracking-subscription-banner__form-grid-cta">
                                    <button id="submit6C122B8D4C434A2C8B79EE31056131A4" type="submit" :class="$refs.trackingSubscriptionBanner.classList.contains('bg-primary') ? 'msc-cta msc-cta--secondary' : 'msc-cta msc-cta--primary'">
                                        Sign Up
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </template>

                <template x-if="onResponse">
                    <div class="grid-x msc-tracking-subscription-banner__wrapper-subscribed bg-primary">
                        <p class="msc-tracking-subscription-banner__subscribed-title" x-html="titleMessage"></p>
                        <p class="msc-tracking-subscription-banner__subscribed-description" x-html="subtitleMessage"></p>
                    </div>
                </template>
            </div>
        </div>
    </template>
</div>

<div class="msc-solution separator--top-big separator--bottom-small " x-data="mscSolution()" data-template-mobile-row="false" data-big-grid="false" x-on:resize.window="onResize()" x-init="init()">
    <div class="grid-container">
        <div class="grid-x">
                <div class="cell small-12 medium-6 medium-offset-3 msc-ocean-solution__headings">
                    <h2 class="msc-heading msc-ocean-solution__title">Did you know?</h2>
                    <div class="msc-bodytext">
                        MSC also offers Tracking data via DCSA compliant APIs
                    </div>
                </div>
        </div>
        <div class="grid-x">
            <div class="cell small-12">
                <div class="msc-solution__container msc-solution__container--small-grid msc-solution__container--layout-8x1-center" :class="{'msc-solution__container--inline': isMobile &amp;&amp; templateMobileRow,
                   'msc-solution__container--big-grid': bigGrid, 
                   'msc-solution__container--small-grid': !bigGrid,
                   'msc-solution__container--layout-3x1-center': !isMobile &amp;&amp; layout3x1Center,
                   'msc-solution__container--layout-6x2-center': !isMobile &amp;&amp; layout6x2Center,
                   'msc-solution__container--layout-8x1-center': !isMobile &amp;&amp; layout8x1Center,
                   'msc-solution__container--layout-4x2-center': !isMobile &amp;&amp; layout4x2Center,
                   'msc-solution__container--layout-8x3-center': !isMobile &amp;&amp; layout8x3Center}" x-ref="solution-container">

                    



<div class="msc-solution__card" :class="{'msc-solution__card--inline-card': isMobile &amp;&amp; templateMobileRow}">

        <img class="lazy msc-solution__card-image lazy-loaded" :class="{'d-none': isMobile}" data-src="https://msc-p-001.sitecorecontenthub.cloud/api/public/content/6406c4ebd0b442feb9a2ceda5f2a57db?v=4076f49e" alt="" src="https://msc-p-001.sitecorecontenthub.cloud/api/public/content/6406c4ebd0b442feb9a2ceda5f2a57db?v=4076f49e">
        <img class="lazy msc-solution__card-image d-none" :class="{'d-none': !isMobile}" data-src="https://msc-p-001.sitecorecontenthub.cloud/api/public/content/6406c4ebd0b442feb9a2ceda5f2a57db?v=4076f49e" alt="">
    <div class="msc-solution__card-content">
                <div class="msc-solution__card-content-title">
                    Learn more on our Direct Integrations page
                </div>

        <div class="msc-solution__card-content-text">
            <p></p>



<a href="/en/solutions/digital-solutions/direct-integrations" class="msc-cta-arrow msc-cta-arrow--primary msc-solution__card-content-link" title="MSC Direct Integrations">                    <span class="">Read more</span>
</a>
        </div>

    </div>
</div>



                </div>
            </div>
        </div>
    </div>
</div>


        </div>
    
    

<footer class="msc-footer" x-data="mscFooter()" x-init="init()" data-office-label="Offices">
    <div class="grid-container">
    <div class="grid-x grid-margin-x">
        <div class="cell small-12">
                <div class="msc-footer__toTop msc-footer__toTop--small" :class="{ 'msc-footer__toTop--logo' : logoCountry }">
                    <button class="msc-cta-arrow msc-cta-arrow--simple msc-cta-arrow--top" @click="scrollToTop()" id="back-to-top-btn">
                        <span>Top</span>
                    </button>
                </div>
        </div>
    </div>
</div><div class="grid-container">
    <div class="grid-x grid-margin-x">
        <div class="cell small-12">
        </div>
    </div>
</div>
    <div class="msc-footer__border" x-show="!isMobile"></div>
    <div class="grid-container msc-footer__content">
        <div class="grid-x grid-margin-x">
            <div class="cell small-12">
                <div class="msc-footer__wrapper">
                    <div class="msc-footer__agency">
    <p class="subtitle">COUNTRY-LOCATION / LOCAL OFFICE</p>

<div class="msc-footer-agency" x-ref="footerAgency" data-api-url="/api/feature/LocalInformation/GetCountryAgencies" data-agency-iso-code="JP" data-agency-name="MSC TOKYO" data-company-id="204">
    <div class="msc-footer-agency__wrapper">
        <form class="msc-footer-agency__form">
            <div class="msc-form-group msc-form-group--country" :class="{ 'opened' : countriesVisible &amp;&amp; filteredCountries().length &gt; 0 }">
                <div class="msc-footer-agency__dropdown">
                    <div class="msc-select-dropdown" @click.away="closeBoxCountries()">
                        <input type="text" class="msc-form-control" x-model="searchCountry" @click="openBoxCountries()" style="">
                        <button type="button" class="msc-cta-icon-simple msc-cta--clear" @click="clearInputCountry()" x-show="searchCountry">
                            <span class="msc-icon-close"></span>
                        </button>
                        <button type="button" class="msc-cta-icon-simple" :class="{ 'opened': countriesVisible }" @click="toggleBoxCountries()" :disabled="searchCountry.length !== 0" disabled="disabled">
                            <span class="msc-icon-chevron"></span>
                        </button>
                    </div>
                    <div class="msc-footer-agency__results msc-footer-agency__results--country" x-show="countriesVisible &amp;&amp; filteredCountries().length &gt; 0" style="display: none;">
                        <ul>
                            <template x-for="country in filteredCountries()">
                                <li>
                                    <a @click="selectCountry(country)" x-text="country.Country"></a>
                                </li>
                            </template>
                        
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            
                                
                            </ul>
                    </div>
                </div>
            </div>

            <div class="msc-form-group msc-form-group--agency" :class="{ 'opened' : agenciesVisible }">
                <div class="msc-footer-agency__dropdown">
                    <div class="msc-select-dropdown" @click.away="closeBoxAgencies()">
                        <input type="text" class="msc-form-control" x-model="searchAgency" :disabled="!searchCountry" @click="openBoxAgencies()" style="">
                        <button type="button" class="msc-cta-icon-simple" :class="{ 'opened': agenciesVisible }" :disabled="!searchCountry" @click="toggleBoxAgencies()">
                            <span class="msc-icon-chevron"></span>
                        </button>

                    </div>

                    <div class="msc-footer-agency__results msc-footer-agency__results--agency" x-show="agenciesVisible" style="display: none;">
                        <ul>
                            <template x-for="agency in filteredAgencies()">
                                <li>
                                    <a @click="selectAgency(agency, true)" x-text="agency.Name"></a>
                                </li>
                            </template>
                        
                                <li>
                                    <a @click="selectAgency(agency, true)" x-text="agency.Name">MSC TOKYO</a>
                                </li>
                            </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
    <nav aria-labelledby="footer-agency-links" class="msc-footer__agency-links">
        <ul>
            <li>
                <a x-ref="agencyPhone" @click="track('phoneLink')" x-bind:href="'tel:' + $store.agency.phone" data-phone="+81 345895290" x-show="$store.agency.phone" href="tel:+81 345895290">
                    <span class="msc-icon-phone"></span>
                    <span class="ltr-direction" x-text="$store.agency.phone">+81 345895290</span>
                </a>
            </li>
            <li>
                <a x-ref="agencyEmail" @click="track('emailClick')" x-bind:href="'mailto:' + $store.agency.email" data-email="<EMAIL>" x-show="$store.agency.email" href="mailto:<EMAIL>">
                    <span class="msc-icon-email-open"></span>
                    <span x-text="$store.agency.email"><EMAIL></span>
                </a>
            </li>
            <li>
                <a :href="getLinkOffice()" class="msc-footer__agency-link" x-show="selectedCountry &amp;&amp; selectedCountry.LocalInfoPageLink" href="/en/local-information/asia-pacific/japan?office=msc-tokyo#Offices">
                    <span class="msc-icon-offices"></span>
                    <span>Office details</span>
                </a>
            </li>
        </ul>
    </nav>








</div>





    <div class="msc-footer__business align-center">
        <nav class="msc-footer__links" aria-labelledby="footer-menu-business">
            <p class="subtitle">Doing business together</p>
            <ul>
                    <li>
<a href="/en/solutions" title="Solutions">                            <span>Solutions</span>
</a>                    </li>
                    <li>
<a href="/en/local-information" title="MSC Local Information">                            <span>Local information</span>
</a>                    </li>
                    <li>
<a href="/en/ebusiness" title="E-Business">                            <span>E-Business</span>
</a>                    </li>
                    <li>
<a href="/en/sustainability" title="Sustainability">                            <span>Sustainability</span>
</a>                    </li>
                    <li>
<a href="https://mymsc.com/mymsc/" rel="noopener noreferrer" title="Log in to myMSC " target="_blank">                            <span>myMSC</span>
</a>                    </li>
            </ul>
        </nav>
    </div>



    <div class="msc-footer__contact align-right" :class="{ 'msc-footer__contact--logo' : logoCountry }">
        <div class="msc-footer__contact-links">
            <div class="msc-footer__menu">
                <nav class="msc-footer__links" aria-labelledby="footer-menu-contact">
                    <p class="subtitle"><span style="background-color: #ffffff; color: #202124;">Get to know us</span></p>
                    <ul>
                            <li>
<a href="https://www.mscgroup.com/" rel="noopener noreferrer" title="Learn more about the MSC Group" target="_blank">                                    <span>MSC Group</span>
</a>                            </li>
                            <li>
<a href="/en/newsroom" title="MSC Newsroom">                                    <span>Newsroom</span>
</a>                            </li>
                            <li>
<a href="/en/events" title="Stay up to date with events and meet us there">                                    <span>Events</span>
</a>                            </li>
                            <li>
<a href="/en/lp/blog" title="Catching Waves with MSC Blogs and Articles">                                    <span>Blog</span>
</a>                            </li>
                            <li>
<a href="/en/careers" title="Careers at MSC">                                    <span>Careers</span>
</a>                            </li>
                            <li>
<a href="/en/contact-us" title="Contact us">                                    <span>Contact us</span>
</a>                            </li>
                    </ul>
                </nav>
            </div>
            <div class="msc-footer__social align-right">
                <nav aria-labelledby="footer-social-links" class="msc-footer__social-links">
                    <ul>
                            <li>
<a href="https://www.facebook.com/MSCCargo/" rel="noopener noreferrer" class="icon msc-icon--msc-icon-facebook" target="_blank" title="Facebook MSC Cargo">                                    <span class="msc-icon-facebook"></span>
</a>                            </li>
                            <li>
<a href="https://x.com/msccargo" rel="noopener noreferrer" class="icon msc-icon--msc-icon-twitter" target="_blank" title="X MSC Cargo">                                    <span class="msc-icon-twitter"></span>
</a>                            </li>
                            <li>
<a href="https://www.instagram.com/msccargo/" rel="noopener noreferrer" class="icon msc-icon--msc-icon-instagram" target="_blank" title="Instagram MSC Cargo">                                    <span class="msc-icon-instagram"></span>
</a>                            </li>
                            <li>
<a href="https://www.linkedin.com/company/msc-mediterranean-shipping-co--s-a-/mycompany/verification/" rel="noopener noreferrer" class="icon msc-icon--msc-icon-linkedin" target="_blank" title="Linkedin MSC Cargo">                                    <span class="msc-icon-linkedin"></span>
</a>                            </li>
                            <li>
<a href="https://www.youtube.com/@MSCCargo" rel="noopener noreferrer" class="icon msc-icon--msc-icon-youtube" target="_blank" title="YouTube MSC Cargo">                                    <span class="msc-icon-youtube"></span>
</a>                            </li>
                    </ul>
                </nav>
            </div>
        </div>

    </div>
                    <div class="msc-footer__logo hidden">
                        <a href=""><span class="msc-icon-msc"></span></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    



<div class="msc-footer__office ltr-direction">
    <nav class="msc-footer__office-links" aria-labelledby="footer-menu-office">
        <ul>
            <li>
                <span>
                    Headquarters: 
                </span>
            </li>
                <li>
                    <a href="tel:+41 227038888" data-phone="+41 227038888"><span>+41 227038888</span></a>
                </li>
                            <li>
                    <a href="mailto:<EMAIL>" data-email="<EMAIL>"><span> <EMAIL></span></a>
                </li>
                            <li>
                    <span>Chemin Rieu 12, 1208 Geneva</span>
                </li>
                            <li>
                    <span>Switzerland</span>
                </li>
        </ul>
    </nav>
</div>


<div class="msc-footer__admin">
    <div class="grid-x grid-margin-x">
        <div class="cell small-12">
            <nav aria-labelledby="footer-admin-links" class="msc-footer__admin-links">
                <ul>
                            <li>
<a href="javascript:Optanon.ToggleInfoDisplay()" title="Cookie settings">                                    <span>Cookie Settings</span>
</a>                            </li>
                            <li>
<a href="/en/data-privacy" title="Your privacy and confidentiality are important to us">                                    <span>Data Privacy</span>
</a>                            </li>
                            <li>
<a href="/en/personal-data-request" title="Personal Data Request">                                    <span>Personal Data Request</span>
</a>                            </li>
                            <li>
<a href="/en/terms-and-conditions" title="Terms of Use">                                    <span>Terms of Use</span>
</a>                            </li>
                            <li>
<a href="/en/carrier-terms" title="Carrier's Terms &amp; Conditions">                                    <span>Carrier's Terms &amp; Conditions</span>
</a>                            </li>
                            <li>
<a href="/-/media/files/legal-files/signed_commitments_160620_fine.pdf?rev=41212e85711f4a9ca7c2d51128b3fa42" title="EU Commitments">                                    <span>EU Commitments</span>
</a>                            </li>
                            <li>
<a href="/en/sustainability/msc-code-of-conduct" title="MSC Code of Business Conduct">                                    <span>Code of Conduct</span>
</a>                            </li>
                            <li>
<a href="/en/sustainability/certifications" title="MSC Certifications">                                    <span>Certifications</span>
</a>                            </li>
                            <li>
<a href="/en/speak-up" title="MSC Speak-up line">                                    <span>Speak Up Line</span>
</a>                            </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

</footer>
    <script src="/_sc/Assets/scripts/main.js?v=2526057" type="text/javascript"></script>
    
    

<script type="text/javascript" id="" charset="">(function(a,e,b,f,g,c,d){a[b]=a[b]||function(){(a[b].q=a[b].q||[]).push(arguments)};c=e.createElement(f);c.async=1;c.src="https://www.clarity.ms/tag/"+g+"?ref\x3dgtm2";d=e.getElementsByTagName(f)[0];d.parentNode.insertBefore(c,d)})(window,document,"clarity","script","gxpeclq6od");</script><div id="onetrust-consent-sdk" data-nosnippet="true"><div class="onetrust-pc-dark-filter ot-fade-in" style="display: none;z-index:2147483645;visibility: hidden;
                    opacity: 0;transition: visibility 0s 400ms, opacity 400ms linear;"></div><div id="onetrust-banner-sdk" class="otFloatingRoundedIcon otRelFont default" tabindex="0" role="region" aria-label="Cookie banner" style="display: none;
                    transition: visibility 0s 400ms, opacity 400ms linear;
                    opacity: 0;visibility: hidden;"><div role="dialog" aria-modal="true" aria-label="我们重视您的隐私"><!-- Cookie Button --><div id="onetrust-cookie-btn-container"><button class="ot-link-btn" id="onetrust-cookie-btn" role="button" aria-label="Cookies"><svg width="65" height="65" viewBox="-2 -2 35 35" xmlns="http://www.w3.org/2000/svg"><title>Cookies Button</title><g fill="none" fill-rule="evenodd"><circle cx="15" cy="15" r="16.5" fill="#2e3643"></circle><circle cx="15" cy="15" r="14.5" fill="#fff"></circle><circle cx="15" cy="15" r="14" fill="#2e3643"></circle><path d="M25 14.95V15c0 5.523-4.477 10-10 10S5 20.523 5 15c0-4.842 3.442-8.881 8.013-9.803A3.5 3.5 0 0 0 16.13 8.98a4 4 0 0 0 6.025 4.39A2.501 2.501 0 0 0 25 14.95z" stroke="#323F4E" stroke-width=".5" fill="#FFF"></path><circle fill="#BDBDBD" cx="10" cy="12" r="2"></circle><circle fill="#BDBDBD" cx="12.5" cy="19.5" r="1.5"></circle><circle fill="#BDBDBD" cx="20" cy="18" r="2"></circle><circle fill="#6CC04A" cx="14" cy="14" r="1"></circle><circle fill="#6CC04A" cx="17" cy="22" r="1"></circle><circle fill="#6CC04A" cx="9" cy="18" r="1"></circle></g></svg></button></div><div class="banner-content"><div class="ot-sdk-container"><!-- Cookie Button END --><div class="ot-sdk-row"><div id="onetrust-group-container" class="ot-sdk-eight ot-sdk-columns"><div class="banner_logo"></div><div id="onetrust-policy"><h2 id="onetrust-policy-title">我们重视您的隐私</h2><div id="onetrust-policy-text">点击“接受所有 Cookie”，即表示您同意在您的设备上存储 Cookie，以优化网站导航、分析网站使用情况和协助我们开展营销工作。<a class="ot-cookie-policy-link" href="https://www.msc.com/en/cookie-policy" aria-label="有关您的隐私的更多信息, 在新窗口中打开" rel="noopener" target="_blank">查看我们的 Cookie 政策</a></div></div></div><div id="onetrust-button-group-parent" class="ot-sdk-three ot-sdk-columns has-reject-all-button"><div id="onetrust-button-group" class="ot-sdk-row"><div id="onetrust-accept-btn-container"><button id="onetrust-accept-btn-handler">接受所有 Cookie</button></div><div id="onetrust-reject-btn-container"><button id="onetrust-reject-all-handler">拒绝所有 Cookie</button></div><div id="onetrust-pc-btn-container"><button id="onetrust-pc-btn-handler">Cookie 设置</button></div></div></div></div></div><!-- Close Button --><div id="onetrust-close-btn-container"></div><!-- Close Button END --></div></div></div><div id="onetrust-pc-sdk" class="otPcTab ot-hide ot-fade-in otRelFont" lang="zh" aria-label="Preference center" role="region" style="display: none;
                    transition: visibility 0s 400ms, opacity 400ms linear;
                    opacity: 0;visibility: hidden;"><div role="dialog" aria-modal="true" style="height: 100%;" aria-label="隐私偏好中心"><!-- pc header --><div class="ot-pc-header" role="presentation"><!-- Header logo --><div class="ot-pc-logo" role="img" aria-label="公司徽标"><img alt="公司徽标" src="https://cdn.cookielaw.org/logos/4d0f0420-9941-4d59-903c-3cb61fde5441/5b4f737d-981f-4af7-9842-966b1aa26c42/863735b5-020c-4083-b9b5-3256c9323baf/1200px-Mediterranean_Shipping_Company_logo.svg.png"></div><div class="ot-title-cntr"><h2 id="ot-pc-title">隐私偏好中心</h2><div class="ot-close-cntr"><button id="close-pc-btn-handler" class="ot-close-icon" aria-label="关闭" style="background-image: url(&quot;https://cdn.cookielaw.org/logos/static/ot_close.svg&quot;);"></button></div></div></div><!-- content --><!-- Groups / Sub groups with cookies --><div id="ot-pc-content" class="ot-pc-scrollbar ot-sdk-row"><div class="ot-optout-signal ot-hide"><div class="ot-optout-icon"><svg xmlns="http://www.w3.org/2000/svg"><path class="ot-floating-button__svg-fill" d="M14.588 0l.445.328c1.807 1.303 3.961 2.533 6.461 3.688 2.015.93 4.576 1.746 7.682 2.446 0 14.178-4.73 24.133-14.19 29.864l-.398.236C4.863 30.87 0 20.837 0 6.462c3.107-.7 5.668-1.516 7.682-2.446 2.709-1.251 5.01-2.59 6.906-4.016zm5.87 13.88a.75.75 0 00-.974.159l-5.475 6.625-3.005-2.997-.077-.067a.75.75 0 00-.983 1.13l4.172 4.16 6.525-7.895.06-.083a.75.75 0 00-.16-.973z" fill="#FFF" fill-rule="evenodd"></path></svg></div><span></span></div><div class="ot-sdk-container ot-grps-cntr ot-sdk-column"><div class="ot-sdk-four ot-sdk-columns ot-tab-list" aria-label="Cookie Categories"><ul class="ot-cat-grp" role="tablist" aria-orientation="vertical"><li class="ot-abt-tab" role="presentation"><!-- About Privacy container --><div class="ot-active-menu category-menu-switch-handler" role="tab" tabindex="0" aria-selected="true" aria-controls="ot-tab-desc"><h3 id="ot-pvcy-txt">您的隐私</h3></div></li><li class="ot-cat-item ot-vs-config" role="presentation" data-optanongroupid="C0002"><div class="category-menu-switch-handler" role="tab" tabindex="-1" aria-selected="false" aria-controls="ot-desc-id-C0002"><h3 id="ot-header-id-C0002">性能 Cookie</h3></div></li><li class="ot-cat-item ot-vs-config" role="presentation" data-optanongroupid="C0004"><div class="category-menu-switch-handler" role="tab" tabindex="-1" aria-selected="false" aria-controls="ot-desc-id-C0004"><h3 id="ot-header-id-C0004">定向 Cookie</h3></div></li><li class="ot-cat-item ot-vs-config" role="presentation" data-optanongroupid="C0003"><div class="category-menu-switch-handler" role="tab" tabindex="-1" aria-selected="false" aria-controls="ot-desc-id-C0003"><h3 id="ot-header-id-C0003">功能 Cookie</h3></div></li><li class="ot-cat-item ot-always-active-group ot-vs-config" role="presentation" data-optanongroupid="C0001"><div class="category-menu-switch-handler" role="tab" tabindex="-1" aria-selected="false" aria-controls="ot-desc-id-C0001"><h3 id="ot-header-id-C0001">绝对必要的 Cookie</h3></div></li></ul></div><div class="ot-tab-desc ot-sdk-eight ot-sdk-columns"><div class="ot-desc-cntr" id="ot-tab-desc" tabindex="0" role="tabpanel" aria-labelledby="ot-pvcy-hdr"><h4 id="ot-pvcy-hdr">您的隐私</h4><p id="ot-pc-desc" class="ot-grp-desc">您访问任何网站时，网站都可能在您的浏览器上存储或检索信息，大多数是以 Cookie 的形式进行。此信息可能与您、您的偏好、您的设备相关，或者该信息被用于使网站按照您期望的方式工作。这些信息通常不会直接识别您，但它可为您提供更多个性化的 Web 体验。您可以选择不允许使用某些类型的 Cookie。单击不同类别标题以了解更多信息并更改默认设置。但是，您应该知道，阻止某些类型的 Cookie 可能会影响您的网站体验和我们能够提供的服务。
            <br><a href="https://cookiepedia.co.uk/giving-consent-to-cookies" class="privacy-notice-link" rel="noopener" target="_blank" aria-label="有关您的隐私的更多信息, 在新窗口中打开">更多信息</a></p></div><div class="ot-desc-cntr ot-hide" role="tabpanel" tabindex="0" id="ot-desc-id-C0002"><div class="ot-grp-hdr1"><h4 class="ot-cat-header">性能 Cookie</h4><div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0002" id="ot-group-id-C0002" role="switch" class="category-switch-handler" data-optanongroupid="C0002" aria-labelledby="ot-header-id-C0002" checked="" style=""> <label class="ot-switch" for="ot-group-id-C0002"><span class="ot-switch-nob"></span> <span class="ot-label-txt">性能 Cookie</span></label> </div><div class="ot-tgl-cntr"></div></div><p class="ot-grp-desc ot-category-desc">使用 Cookie，我们可以计算访问量和流量来源，以便衡量和提高我们网站的性能。Cookie 有助于我们了解哪些页面最受欢迎、哪些最不受欢迎，并查看访问者如何浏览网站。这些 Cookie 收集的所有信息都聚合在一起，因此是匿名处理方式。如果您不允许使用这些 Cookie，我们将不知道您何时访问了我们的网站。</p></div><div class="ot-desc-cntr ot-hide" role="tabpanel" tabindex="0" id="ot-desc-id-C0004"><div class="ot-grp-hdr1"><h4 class="ot-cat-header">定向 Cookie</h4><div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0004" id="ot-group-id-C0004" role="switch" class="category-switch-handler" data-optanongroupid="C0004" aria-labelledby="ot-header-id-C0004" checked="" style=""> <label class="ot-switch" for="ot-group-id-C0004"><span class="ot-switch-nob"></span> <span class="ot-label-txt">定向 Cookie</span></label> </div><div class="ot-tgl-cntr"></div></div><p class="ot-grp-desc ot-category-desc">这些 Cookie 由广告合作伙伴通过我们的网站进行设置。这些公司可能利用 Cookie 构建您的兴趣分布图并向您展示其他网站上的相关广告。它们只需识别您的浏览器和设备便可发挥作用。如果您不允许使用这些 Cookie，您将不能体验不同网站上的定向广告。</p></div><div class="ot-desc-cntr ot-hide" role="tabpanel" tabindex="0" id="ot-desc-id-C0003"><div class="ot-grp-hdr1"><h4 class="ot-cat-header">功能 Cookie</h4><div class="ot-tgl"><input type="checkbox" name="ot-group-id-C0003" id="ot-group-id-C0003" role="switch" class="category-switch-handler" data-optanongroupid="C0003" aria-labelledby="ot-header-id-C0003" checked="" style=""> <label class="ot-switch" for="ot-group-id-C0003"><span class="ot-switch-nob"></span> <span class="ot-label-txt">功能 Cookie</span></label> </div><div class="ot-tgl-cntr"></div></div><p class="ot-grp-desc ot-category-desc">这些 Cookie 允许提供增强功能和个性化内容，如视频和实时聊天。我们或我们已将其服务添加至我们页面上的第三方提供者可以进行设置。如果您不允许使用这些 Cookie，则可能无法实现部分或全部功能的正常工作。</p></div><div class="ot-desc-cntr ot-hide ot-always-active-group" role="tabpanel" tabindex="0" id="ot-desc-id-C0001"><div class="ot-grp-hdr1"><h4 class="ot-cat-header">绝对必要的 Cookie</h4><div class="ot-tgl-cntr"><div class="ot-always-active">始终处于活动状态</div></div></div><p class="ot-grp-desc ot-category-desc">网站运行离不开这些 Cookie 且您不能在系统中将其关闭。通常仅根据您所做出的操作（即服务请求）来设置这些 Cookie，如设置隐私偏好、登录或填充表格。您可以将您的浏览器设置为阻止或向您提示这些 Cookie，但可能会导致某些网站功能无法工作。</p></div></div></div></div><!-- Vendors / Hosts --><section id="ot-pc-lst" class="ot-hide ot-pc-scrollbar ot-enbl-chr"><div class="ot-lst-cntr ot-pc-scrollbar"><div id="ot-pc-hdr"><div id="ot-lst-title"><button class="ot-link-btn back-btn-handler" aria-label="Back"><svg id="ot-back-arw" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 444.531 444.531" xml:space="preserve"><title>Back Button</title><g><path fill="#656565" d="M213.13,222.409L351.88,83.653c7.05-7.043,10.567-15.657,10.567-25.841c0-10.183-3.518-18.793-10.567-25.835
                  l-21.409-21.416C323.432,3.521,314.817,0,304.637,0s-18.791,3.521-25.841,10.561L92.649,196.425
                  c-7.044,7.043-10.566,15.656-10.566,25.841s3.521,18.791,10.566,25.837l186.146,185.864c7.05,7.043,15.66,10.564,25.841,10.564
                  s18.795-3.521,25.834-10.564l21.409-21.412c7.05-7.039,10.567-15.604,10.567-25.697c0-10.085-3.518-18.746-10.567-25.978
                  L213.13,222.409z"></path></g></svg></button><h3>Cookie 列表</h3></div><div class="ot-lst-subhdr"><div id="ot-search-cntr"><p role="status" class="ot-scrn-rdr"></p><input id="vendor-search-handler" type="text" name="vendor-search-handler" placeholder="搜索..." aria-label="Cookie list search" style=""> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 -30 110 110" aria-hidden="true"><path fill="#2e3644" d="M55.146,51.887L41.588,37.786c3.486-4.144,5.396-9.358,5.396-14.786c0-12.682-10.318-23-23-23s-23,10.318-23,23
              s10.318,23,23,23c4.761,0,9.298-1.436,13.177-4.162l13.661,14.208c0.571,0.593,1.339,0.92,2.162,0.92
              c0.779,0,1.518-0.297,2.079-0.837C56.255,54.982,56.293,53.08,55.146,51.887z M23.984,6c9.374,0,17,7.626,17,17s-7.626,17-17,17
              s-17-7.626-17-17S14.61,6,23.984,6z"></path></svg></div><div id="ot-fltr-cntr"><button id="filter-btn-handler" aria-label="Filter" aria-haspopup="true"><svg role="presentation" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 402.577 402.577" xml:space="preserve"><title>Filter Button</title><g><path fill="#2c3643" d="M400.858,11.427c-3.241-7.421-8.85-11.132-16.854-11.136H18.564c-7.993,0-13.61,3.715-16.846,11.136
                            c-3.234,7.801-1.903,14.467,3.999,19.985l140.757,140.753v138.755c0,4.955,1.809,9.232,5.424,12.854l73.085,73.083
                            c3.429,3.614,7.71,5.428,12.851,5.428c2.282,0,4.66-0.479,7.135-1.43c7.426-3.238,11.14-8.851,11.14-16.845V172.166L396.861,31.413
                            C402.765,25.895,404.093,19.231,400.858,11.427z"></path></g></svg></button></div></div></div><section id="ot-lst-cnt" class="ot-pc-scrollbar"><div class="ot-sdk-row"><div class="ot-sdk-column"><div id="ot-sel-blk"><div class="ot-sel-all"><div class="ot-sel-all-hdr"><span class="ot-consent-hdr">Consent</span> <span class="ot-li-hdr">Leg.Interest</span></div><div class="ot-sel-all-chkbox"><div class="ot-chkbox" id="ot-selall-hostcntr"><input id="select-all-hosts-groups-handler" type="checkbox" style=""> <label for="select-all-hosts-groups-handler"><span class="ot-label-txt">checkbox label</span></label> <span class="ot-label-status">label</span></div><div class="ot-chkbox" id="ot-selall-vencntr"><input id="select-all-vendor-groups-handler" type="checkbox" style=""> <label for="select-all-vendor-groups-handler"><span class="ot-label-txt">checkbox label</span></label> <span class="ot-label-status">label</span></div><div class="ot-chkbox" id="ot-selall-licntr"><input id="select-all-vendor-leg-handler" type="checkbox" style=""> <label for="select-all-vendor-leg-handler"><span class="ot-label-txt">checkbox label</span></label> <span class="ot-label-status">label</span></div></div></div></div></div></div></section></div><div id="ot-anchor"></div><section id="ot-fltr-modal"><div id="ot-fltr-cnt"><button id="clear-filters-handler">Clear</button><div class="ot-fltr-scrlcnt ot-pc-scrollbar"><div class="ot-fltr-opts"><div class="ot-fltr-opt"><div class="ot-chkbox"><input id="chkbox-id" type="checkbox" class="category-filter-handler" style=""> <label for="chkbox-id"><span class="ot-label-txt">checkbox label</span></label> <span class="ot-label-status">label</span></div></div></div><div class="ot-fltr-btns"><button id="filter-apply-handler">Apply</button> <button id="filter-cancel-handler">Cancel</button></div></div></div></section></section><!-- Footer buttons and logo --><div class="ot-pc-footer ot-pc-scrollbar"><div class="ot-btn-container"><button class="save-preference-btn-handler onetrust-close-btn-handler">确认我的选择</button><div class="ot-btn-subcntr"><button class="ot-pc-refuse-all-handler">全部拒绝</button> <button id="accept-recommended-btn-handler">全部允许</button></div></div><div class="ot-pc-footer-logo"><a href="https://www.onetrust.com/products/cookie-consent/" target="_blank" rel="noopener noreferrer" aria-label="Powered by OneTrust 在新窗口中打开"><img alt="Powered by Onetrust" src="https://cdn.cookielaw.org/logos/static/powered_by_logo.svg" title="Powered by OneTrust 在新窗口中打开"></a></div></div><!-- Cookie subgroup container --><!-- Vendor list link --><!-- Cookie lost link --><!-- Toggle HTML element --><!-- Checkbox HTML --><!-- Arrow SVG element --><!-- Accordion basic element --><span class="ot-scrn-rdr" aria-atomic="true" aria-live="polite">您的隐私 [“对话已关闭”]</span><!-- Vendor Service container and item template --></div><iframe class="ot-text-resize" sandbox="allow-same-origin" title="onetrust-text-resize" style="position: absolute; top: -50000px; width: 100em;" aria-hidden="true"></iframe></div></div><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe><div id="batBeacon261579023368" style="width: 0px; height: 0px; display: none; visibility: hidden;"><img id="batBeacon643969413268" width="0" height="0" alt="" src="https://bat.bing.net/action/0?ti=56292612&amp;tm=gtm002&amp;Ver=2&amp;mid=6320f83d-a8a4-4a99-ae96-0cb9052440c8&amp;bo=3&amp;uach=pv%3D10.0.0&amp;pi=918639831&amp;lg=zh-CN&amp;sw=1280&amp;sh=720&amp;sc=24&amp;nwd=1&amp;tl=Shipping%20Container%20Tracking%20and%20Tracing%20%7C%20MSC&amp;p=https%3A%2F%2Fwww.msc.com%2Fen%2Ftrack-a-shipment&amp;r=&amp;lt=4153&amp;mtp=20&amp;evt=pageLoad&amp;sv=1&amp;asc=D&amp;cdb=AQER&amp;rn=919360" style="width: 0px; height: 0px; display: none; visibility: hidden;"></div><script type="text/javascript" id="" charset="">(function(){var a=google_tag_manager["rm"]["9529786"](124)-0+1,b=".msc.com";document.cookie="jcoPageCount\x3d"+a+";domain\x3d"+b+";path\x3d/;"})();</script><script id="" text="" charset="" type="text/javascript" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js"></script>
<script type="text/javascript" id="" charset="">window.yjDataLayer=window.yjDataLayer||[];function ytag(){yjDataLayer.push(arguments)}ytag({type:"ycl_cookie"});</script><cs-native-frame-holder hidden=""></cs-native-frame-holder><iframe src="https://csxd.contentsquare.net/uxa/xdframe-single-domain-1.2.0.html?pid=81668" hidden="" title="Intentionally blank"></iframe></body></html>
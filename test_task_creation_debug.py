#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查任务创建问题的详细测试脚本
"""

import sqlite3
import sys
import json
from pathlib import Path
from datetime import datetime

def check_task_queue_table():
    """检查task_queue表结构和数据"""
    print('📋 task_queue表结构:')
    print('=' * 60)
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute('PRAGMA table_info(task_queue)')
        columns = cursor.fetchall()
        
        for col in columns:
            default_val = f' DEFAULT {col[4]}' if col[4] else ''
            pk_info = 'PRIMARY KEY' if col[5] else ''
            null_info = 'NOT NULL' if col[3] else 'NULL'
            print(f'  {col[1]} ({col[2]}){default_val} - {null_info} {pk_info}')
        
        # 检查最近的数据
        print(f'\n📊 最近10条任务记录:')
        print('=' * 60)
        cursor.execute('SELECT id, tracking_number, status, creator_id, created_at FROM task_queue ORDER BY created_at DESC LIMIT 10')
        rows = cursor.fetchall()
        
        if not rows:
            print('  ❌ 没有任务记录')
        else:
            for row in rows:
                print(f'  ID: {row[0]}, 号码: {row[1]}, 状态: {row[2]}, 创建者: {row[3]}, 时间: {row[4]}')
        
        conn.close()
        return True
    except Exception as e:
        print(f'❌ 检查task_queue表失败: {e}')
        return False

def check_shipment_records_data():
    """检查shipment_records最近数据"""
    print(f'\n📊 最近10条货运记录:')
    print('=' * 60)
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, bill_of_lading, container_number, status, created_by, created_at FROM shipment_records ORDER BY created_at DESC LIMIT 10')
        rows = cursor.fetchall()
        
        if not rows:
            print('  ❌ 没有货运记录')
        else:
            for row in rows:
                bl = row[1] if row[1] else 'N/A'
                cn = row[2] if row[2] else 'N/A'
                print(f'  ID: {row[0]}, 提单号: {bl}, 箱号: {cn}, 状态: {row[3]}, 创建者: {row[4]}, 时间: {row[5]}')
        
        conn.close()
        return True
    except Exception as e:
        print(f'❌ 检查shipment_records表失败: {e}')
        return False

def test_user_task_service():
    """测试用户任务服务"""
    print(f'\n🧪 测试用户任务服务:')
    print('=' * 60)
    
    try:
        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from api.services.user_task_service import UserTaskService
        from api.models.user_schemas import TaskCreateRequest, UserInfo, TaskPriority, UserRole, UserStatus
        
        # 创建测试用户
        test_user = UserInfo(
            user_id="test_web_user",
            name="测试用户",
            role=UserRole.NORMAL,
            avatar="👤",
            status=UserStatus.ACTIVE,
            permissions=["query:create", "query:view"],
            query_count=0,
            success_rate=0.0
        )
        
        # 创建测试请求
        test_container = f"WEBT{datetime.now().strftime('%m%d%H%M%S')}"
        test_request = TaskCreateRequest(
            container_number=test_container,
            carrier_code="TEST",
            priority=TaskPriority.NORMAL
        )
        
        print(f'  📦 测试箱号: {test_container}')
        print(f'  👤 测试用户: {test_user.name} ({test_user.user_id})')
        
        # 创建服务实例
        service = UserTaskService()
        
        # 执行任务创建
        print(f'  🔄 正在创建任务...')
        import asyncio
        
        async def create_task():
            try:
                result = await service.create_single_task(test_request, test_user)
                return result
            except Exception as e:
                print(f'  ❌ 任务创建异常: {e}')
                import traceback
                traceback.print_exc()
                return None
        
        # 运行异步任务
        result = asyncio.run(create_task())
        
        if result:
            print(f'  ✅ 任务创建成功!')
            print(f'     任务ID: {result.task_id}')
            print(f'     箱号: {result.container_number}')
            print(f'     状态: {result.status}')
            print(f'     承运人: {result.carrier_code}')
            
            # 验证数据库中是否真的有这个任务
            print(f'  🔍 验证数据库中的任务...')
            return verify_task_in_db(test_container, result.task_id)
        else:
            print(f'  ❌ 任务创建失败')
            return False
        
    except Exception as e:
        print(f'  ❌ 测试用户任务服务失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def verify_task_in_db(container_number, expected_task_id):
    """验证任务是否在数据库中"""
    try:
        # 检查task_queue表
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, status, creator_id FROM task_queue WHERE tracking_number = ? ORDER BY created_at DESC LIMIT 1', (container_number,))
        task_row = cursor.fetchone()
        conn.close()
        
        if task_row:
            print(f'     ✅ 在task_queue中找到任务: ID={task_row[0]}, 状态={task_row[1]}, 创建者={task_row[2]}')
            if str(task_row[0]) == str(expected_task_id):
                print(f'     ✅ 任务ID匹配!')
            else:
                print(f'     ⚠️  任务ID不匹配: 期望={expected_task_id}, 实际={task_row[0]}')
        else:
            print(f'     ❌ 在task_queue中未找到任务')
        
        # 检查shipment_records表
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, status, created_by FROM shipment_records WHERE container_number = ? OR bill_of_lading = ? ORDER BY created_at DESC LIMIT 1', (container_number, container_number))
        shipment_row = cursor.fetchone()
        conn.close()
        
        if shipment_row:
            print(f'     ✅ 在shipment_records中找到记录: ID={shipment_row[0]}, 状态={shipment_row[1]}, 创建者={shipment_row[2]}')
        else:
            print(f'     ❌ 在shipment_records中未找到记录')
        
        return task_row is not None and shipment_row is not None
        
    except Exception as e:
        print(f'     ❌ 验证数据库失败: {e}')
        return False

def test_shipment_manager_direct():
    """直接测试ShipmentManager"""
    print(f'\n🧪 直接测试ShipmentManager:')
    print('=' * 60)
    
    try:
        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from shipment_manager import ShipmentManager
        
        # 测试数据
        test_container = f"SHIP{datetime.now().strftime('%m%d%H%M%S')}"
        
        print(f'  📦 测试箱号: {test_container}')
        
        # 创建货运记录
        shipment_manager = ShipmentManager()
        record_id = shipment_manager.create_shipment_record(
            container_number=test_container,
            carrier_company="TEST_DIRECT",
            created_by="test_direct_user"
        )
        
        print(f'  ✅ 货运记录创建成功，ID: {record_id}')
        
        # 检查是否有对应的任务
        from task_manager import TaskManager
        task_manager = TaskManager()
        conn = task_manager._get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, status, creator_id FROM task_queue WHERE tracking_number = ? ORDER BY created_at DESC LIMIT 1', (test_container,))
        task_row = cursor.fetchone()
        conn.close()
        
        if task_row:
            print(f'  ✅ 关联任务创建成功，ID: {task_row[0]}, 状态: {task_row[1]}, 创建者: {task_row[2]}')
            return True
        else:
            print(f'  ❌ 没有找到关联的任务')
            return False
        
    except Exception as e:
        print(f'  ❌ 直接测试ShipmentManager失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print('🔍 检查任务创建问题的详细测试')
    print('=' * 80)
    
    # 1. 检查数据库表结构和现有数据
    task_ok = check_task_queue_table()
    shipment_ok = check_shipment_records_data()
    
    # 2. 测试ShipmentManager直接创建
    if task_ok and shipment_ok:
        print(f'\n' + '='*80)
        direct_ok = test_shipment_manager_direct()
        
        # 3. 测试用户任务服务
        print(f'\n' + '='*80)
        service_ok = test_user_task_service()
        
        # 总结
        print(f'\n📋 测试总结:')
        print('=' * 80)
        print(f'  数据库表检查: {"✅" if task_ok and shipment_ok else "❌"}')
        print(f'  ShipmentManager直接测试: {"✅" if direct_ok else "❌"}')
        print(f'  UserTaskService测试: {"✅" if service_ok else "❌"}')
        
        if not service_ok and direct_ok:
            print(f'\n💡 分析: ShipmentManager工作正常，问题可能在UserTaskService或API层')
        elif not direct_ok:
            print(f'\n💡 分析: 问题在ShipmentManager层，任务创建机制有问题')
        else:
            print(f'\n💡 分析: 所有组件工作正常，问题可能在前端调用或数据传递')
    else:
        print(f'\n❌ 数据库表检查失败，无法进行进一步测试')

if __name__ == "__main__":
    main()

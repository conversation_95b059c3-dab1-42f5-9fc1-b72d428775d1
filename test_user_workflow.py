#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_user_workflow():
    """测试完整的用户工作流程：创建任务 → 查看任务列表"""
    print("🔍 测试用户工作流程...")
    
    # API配置
    base_url = "http://localhost:8080"
    invite_code = "TEST001"
    
    try:
        # 1. 登录获取token
        print("\n📝 1. 用户登录...")
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json={"invite_code": invite_code}
        )
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"响应: {login_response.text}")
            return
            
        token = login_response.json()["access_token"]
        print("✅ 登录成功")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. 创建新任务
        print("\n📝 2. 创建新查询任务...")
        test_tracking = f"TEST{int(time.time())}"  # 生成唯一的测试号
        
        create_response = requests.post(
            f"{base_url}/api/v1/tasks/create",
            headers=headers,
            json={
                "container_number": test_tracking,  # 修正字段名
                "carrier_code": "MSC"
            }
        )
        
        if create_response.status_code == 200:
            create_data = create_response.json()
            print(f"✅ 成功创建任务: {test_tracking}")
            print(f"任务ID: {create_data.get('task_id', 'N/A')[:8]}...")
        else:
            print(f"❌ 创建任务失败: {create_response.status_code}")
            print(f"响应: {create_response.text}")
            return
        
        # 3. 立即查看任务列表
        print("\n📝 3. 立即查看任务列表...")
        tasks_response = requests.get(
            f"{base_url}/api/v1/tasks/my-tasks?limit=10",
            headers=headers
        )
        
        if tasks_response.status_code == 200:
            tasks = tasks_response.json()
            print(f"✅ 获取到 {len(tasks)} 个任务")
            
            # 查找我们刚创建的任务
            found_new_task = False
            for i, task in enumerate(tasks, 1):
                container_number = task.get('container_number', 'N/A')
                status = task.get('status', 'N/A')
                created_at = task.get('created_at', 'N/A')
                
                print(f"  {i}. {container_number} - {status} - {created_at}")
                
                if container_number == test_tracking:
                    found_new_task = True
                    print(f"     🎯 找到刚创建的任务!")
                    print(f"     详情: {json.dumps(task, indent=6, ensure_ascii=False)}")
            
            if not found_new_task:
                print(f"❌ 没有找到刚创建的任务: {test_tracking}")
                print("这就是问题所在！")
            else:
                print("✅ 新创建的任务出现在列表中")
        else:
            print(f"❌ 获取任务列表失败: {tasks_response.status_code}")
            print(f"响应: {tasks_response.text}")
        
        # 4. 等待一会儿再查看（模拟自动刷新）
        print(f"\n📝 4. 等待3秒后再次查看（模拟自动刷新）...")
        time.sleep(3)
        
        tasks_response2 = requests.get(
            f"{base_url}/api/v1/tasks/my-tasks?limit=10",
            headers=headers
        )
        
        if tasks_response2.status_code == 200:
            tasks2 = tasks_response2.json()
            print(f"✅ 获取到 {len(tasks2)} 个任务")
            
            # 再次查找我们的任务
            found_new_task2 = False
            for i, task in enumerate(tasks2, 1):
                container_number = task.get('container_number', 'N/A')
                status = task.get('status', 'N/A')
                
                print(f"  {i}. {container_number} - {status}")
                
                if container_number == test_tracking:
                    found_new_task2 = True
                    print(f"     🎯 刷新后仍然能找到任务!")
            
            if not found_new_task2:
                print(f"❌ 刷新后任务消失了: {test_tracking}")
                print("这确认了问题！")
            else:
                print("✅ 刷新后任务依然存在")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_user_workflow()

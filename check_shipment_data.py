#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查shipment_records表中的数据
"""

import sqlite3
import json

def check_shipment_data():
    """检查shipment_records表中的数据"""
    print("🔍 检查shipment_records表中的数据...")
    
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    
    # 查询最近的货运记录
    cursor.execute('''
        SELECT container_number, estimated_arrival_time, evidence_screenshot, 
               shipment_dates, ai_analysis_result, updated_at
        FROM shipment_records 
        ORDER BY updated_at DESC 
        LIMIT 5
    ''')
    
    records = cursor.fetchall()
    print(f'找到 {len(records)} 个货运记录:')
    
    for i, (container_number, eta, screenshot, dates, ai_result, updated_at) in enumerate(records):
        print(f'\n{i+1}. {container_number}')
        print(f'   预计到港时间: {eta}')
        print(f'   截图: {"有" if screenshot else "无"}')
        print(f'   更新时间: {updated_at}')
        
        # 解析shipment_dates
        if dates:
            try:
                dates_data = json.loads(dates)
                print(f'   物流节点数量: {len(dates_data)}')
                if dates_data:
                    print(f'   第一个节点: {dates_data[0]}')
            except:
                print(f'   物流节点解析失败')
        else:
            print(f'   物流节点: 无')
        
        # 解析AI分析结果
        if ai_result:
            try:
                ai_data = json.loads(ai_result)
                print(f'   AI分析结果字段: {list(ai_data.keys())}')
            except:
                print(f'   AI分析结果解析失败')
        else:
            print(f'   AI分析结果: 无')
    
    conn.close()

if __name__ == "__main__":
    check_shipment_data()

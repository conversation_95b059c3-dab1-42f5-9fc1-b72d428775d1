#!/usr/bin/env python3
"""用新的跟踪号测试web端任务创建"""

import requests
import json
import sqlite3
from datetime import datetime

def test_with_new_tracking_number():
    """使用新的跟踪号测试"""
    
    # 1. 获取token
    response = requests.post(
        'http://127.0.0.1:8080/api/v1/auth/login', 
        json={'invite_code': 'TEST001'}
    )
    
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return
    
    token_data = response.json()
    token = token_data.get('access_token')
    
    if not token:
        print("无法获取token")
        return
    
    print(f"获取token成功")
    
    # 2. 生成新的跟踪号
    timestamp = str(int(datetime.now().timestamp()))
    new_tracking_number = f"NEWTEST{timestamp}"
    
    print(f"使用新跟踪号: {new_tracking_number}")
    
    # 3. 检查创建前的数据库状态
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM task_queue')
    before_count = cursor.fetchone()[0]
    conn.close()
    
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM shipment_records')
    before_records = cursor.fetchone()[0]
    conn.close()
    
    print(f"创建前: 任务{before_count}个, 记录{before_records}个")
    
    # 4. 创建新任务
    task_data = {
        "container_number": new_tracking_number,
        "carrier_code": "MSC",
        "priority": "normal"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        "http://127.0.0.1:8080/api/v1/tasks/quick-create",
        json=task_data,
        headers=headers
    )
    
    print(f"创建响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        task_id = result.get('task_id')
        print(f"任务创建成功，ID: {task_id}")
        
        # 5. 检查创建后的数据库状态
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM task_queue')
        after_count = cursor.fetchone()[0]
        
        # 查找新任务
        cursor.execute('SELECT id, tracking_number, created_at, creator_id, status FROM task_queue WHERE tracking_number = ?', (new_tracking_number,))
        new_task = cursor.fetchone()
        conn.close()
        
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM shipment_records')
        after_records = cursor.fetchone()[0]
        
        # 查找新记录
        cursor.execute('SELECT id, bill_of_lading, created_at, created_by FROM shipment_records WHERE bill_of_lading = ? OR container_number = ?', (new_tracking_number, new_tracking_number))
        new_record = cursor.fetchone()
        conn.close()
        
        print(f"创建后: 任务{after_count}个(+{after_count-before_count}), 记录{after_records}个(+{after_records-before_records})")
        
        if new_task:
            print(f"✅ 找到新任务: {new_task}")
        else:
            print("❌ 没有找到新任务")
            
        if new_record:
            print(f"✅ 找到新记录: {new_record}")
        else:
            print("❌ 没有找到新记录")
    else:
        print(f"任务创建失败: {response.text}")

if __name__ == "__main__":
    test_with_new_tracking_number()

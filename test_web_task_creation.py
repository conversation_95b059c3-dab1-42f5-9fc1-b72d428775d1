#!/usr/bin/env python3
"""测试web端任务创建流程"""

import requests
import json
import sqlite3
from datetime import datetime

# 配置
API_BASE_URL = "http://127.0.0.1:8080"
AUTH_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMDAzIiwiaW52aXRlX2NvZGUiOiJQUk9EVUNUSU9OX0FETUlOIiwiZXhwIjoxNzI1NDI1MzI2fQ.P4G6Y1zJr1iqWrCLJ0IJ1YjEq5g9HPQdaRbSy6EgGI8"

def check_database_before():
    """检查创建任务前的数据库状态"""
    print("=== 创建任务前的数据库状态 ===")
    
    # 检查任务数量
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM task_queue')
    task_count = cursor.fetchone()[0]
    print(f"任务总数: {task_count}")
    
    # 检查最新任务
    cursor.execute('SELECT id, tracking_number, created_at FROM task_queue ORDER BY created_at DESC LIMIT 1')
    latest_task = cursor.fetchone()
    if latest_task:
        print(f"最新任务: {latest_task}")
    conn.close()
    
    # 检查货运记录数量
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM shipment_records')
    record_count = cursor.fetchone()[0]
    print(f"货运记录总数: {record_count}")
    
    # 检查最新记录
    cursor.execute('SELECT id, bill_of_lading, created_at FROM shipment_records ORDER BY created_at DESC LIMIT 1')
    latest_record = cursor.fetchone()
    if latest_record:
        print(f"最新记录: {latest_record}")
    conn.close()
    
    return task_count, record_count

def create_web_task():
    """通过web API创建任务"""
    print("\n=== 通过web API创建任务 ===")
    
    # 创建任务数据
    task_data = {
        "container_number": "TESTAPI" + str(int(datetime.now().timestamp())),
        "carrier_code": "MSC",
        "priority": "normal"
    }
    
    print(f"创建任务: {task_data}")
    
    # 发送请求
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{API_BASE_URL}/api/v1/tasks/quick-create",
        json=task_data,
        headers=headers
    )
    
    print(f"响应状态: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"任务创建成功: {result}")
        return result.get('task_id'), task_data['container_number']
    else:
        print(f"任务创建失败: {response.text}")
        return None, task_data['container_number']

def check_database_after(container_number):
    """检查创建任务后的数据库状态"""
    print("\n=== 创建任务后的数据库状态 ===")
    
    # 检查任务数量
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM task_queue')
    task_count = cursor.fetchone()[0]
    print(f"任务总数: {task_count}")
    
    # 查找新创建的任务
    cursor.execute('SELECT id, tracking_number, created_at, creator_id FROM task_queue WHERE tracking_number = ?', (container_number,))
    new_tasks = cursor.fetchall()
    print(f"找到 {len(new_tasks)} 个匹配的任务:")
    for task in new_tasks:
        print(f"  任务: {task}")
    conn.close()
    
    # 检查货运记录数量
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM shipment_records')
    record_count = cursor.fetchone()[0]
    print(f"货运记录总数: {record_count}")
    
    # 查找新创建的记录
    cursor.execute('SELECT id, bill_of_lading, created_at, created_by FROM shipment_records WHERE bill_of_lading = ? OR container_number = ?', (container_number, container_number))
    new_records = cursor.fetchall()
    print(f"找到 {len(new_records)} 个匹配的记录:")
    for record in new_records:
        print(f"  记录: {record}")
    conn.close()

def main():
    """主函数"""
    print("开始测试web端任务创建流程...")
    
    # 1. 检查创建前状态
    before_tasks, before_records = check_database_before()
    
    # 2. 创建任务
    task_id, container_number = create_web_task()
    
    # 3. 检查创建后状态
    check_database_after(container_number)
    
    print(f"\n=== 测试总结 ===")
    print(f"容器号: {container_number}")
    print(f"任务ID: {task_id}")

if __name__ == "__main__":
    main()

// 在浏览器Console中执行此脚本来自动登录
localStorage.setItem('auth_data', '{"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiVEVTVDAwMSIsIm5hbWUiOiJcdTZkNGJcdThiZDVcdTc1MjhcdTYyMzciLCJyb2xlIjoiXHU2NjZlXHU5MDFhXHU3NTI4XHU2MjM3IiwiZXhwIjoxNzU3MTczOTQ2LCJpYXQiOjE3NTY1NjkxNDYsInR5cGUiOiJhY2Nlc3NfdG9rZW4ifQ.PcoGnBAPfWF1hyd81wP-AauISRlRVMtTwXA9Tnol5Wo", "user": {"user_id": "TEST001", "name": "测试用户", "role": "普通用户", "avatar": "T", "status": "active", "permissions": ["query:create", "query:view", "batch:create"], "created_at": "2025-01-20T00:00:00Z", "last_login": "2025-08-30T15:52:26.543950", "query_count": 5, "success_rate": 80.0}}');
console.log('✅ 登录信息已保存');
console.log('🔄 正在刷新页面...');
location.reload();
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务创建和查询的一致性
"""

import requests
import json
from datetime import datetime

def test_task_consistency():
    """测试任务创建和查询的一致性"""
    print('🧪 测试任务创建和查询的一致性')
    print('=' * 80)
    
    # 1. 登录
    print('步骤 1: 登录')
    login_url = 'http://127.0.0.1:8080/api/v1/auth/login'
    login_data = {'invite_code': 'TEST001'}
    
    response = requests.post(login_url, json=login_data)
    if response.status_code != 200:
        print(f'❌ 登录失败: {response.status_code}')
        return
    
    result = response.json()
    if not result.get('success'):
        print(f'❌ 登录失败: {result.get("message")}')
        return
    
    token = result['access_token']
    user_info = result['user_info']
    print(f'✅ 登录成功，用户ID: {user_info["user_id"]}，用户名: {user_info["name"]}')
    
    # 2. 获取创建前的任务数量
    print('\n步骤 2: 获取创建前的任务数量')
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    tasks_url = 'http://127.0.0.1:8080/api/v1/tasks/my-tasks'
    
    response = requests.get(tasks_url, headers=headers)
    if response.status_code == 200:
        before_tasks = response.json()
        print(f'✅ 创建前任务数量: {len(before_tasks)}')
    else:
        print(f'❌ 获取任务列表失败: {response.status_code}')
        return
    
    # 3. 创建新任务
    print('\n步骤 3: 创建新任务')
    test_container = f'FIX{datetime.now().strftime("%m%d%H%M%S")}'
    create_url = 'http://127.0.0.1:8080/api/v1/tasks/quick-create'
    create_data = {'container_number': test_container}
    
    response = requests.post(create_url, json=create_data, headers=headers)
    if response.status_code == 200:
        task_result = response.json()
        created_task_id = task_result['task_id']
        print(f'✅ 任务创建成功，ID: {created_task_id}，号码: {test_container}')
    else:
        print(f'❌ 任务创建失败: {response.status_code} - {response.text}')
        return
    
    # 4. 获取创建后的任务数量
    print('\n步骤 4: 获取创建后的任务数量')
    response = requests.get(tasks_url, headers=headers)
    if response.status_code == 200:
        after_tasks = response.json()
        print(f'✅ 创建后任务数量: {len(after_tasks)}')
        
        # 5. 检查新任务是否在列表中
        print('\n步骤 5: 检查新任务是否在列表中')
        found_task = None
        for task in after_tasks:
            if task['task_id'] == created_task_id:
                found_task = task
                break
        
        if found_task:
            print(f'✅ 在任务列表中找到新创建的任务!')
            print(f'   任务ID: {found_task["task_id"]}')
            print(f'   容器号: {found_task["container_number"]}')
            print(f'   状态: {found_task["status"]}')
            print(f'   用户ID: {found_task["user_id"]}')
            print(f'   创建时间: {found_task["created_at"]}')
        else:
            print(f'❌ 在任务列表中未找到新创建的任务: {created_task_id}')
            print(f'任务列表中的任务ID:')
            for i, task in enumerate(after_tasks[:5]):
                print(f'  {i+1}. {task["task_id"]} - {task["container_number"]} - {task["user_id"]}')
        
        # 6. 比较任务数量变化
        print(f'\n步骤 6: 比较任务数量变化')
        if len(after_tasks) == len(before_tasks) + 1:
            print(f'✅ 任务数量正确增加了1个')
        else:
            print(f'❌ 任务数量变化异常: 预期+1，实际{len(after_tasks) - len(before_tasks)}')
            
    else:
        print(f'❌ 获取创建后任务列表失败: {response.status_code}')

if __name__ == "__main__":
    test_task_consistency()

#!/usr/bin/env python3
import sys
import os

print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.path[:3]}")

try:
    from scheduled_task_processor import ScheduledTaskProcessor
    print("✓ 成功导入 ScheduledTaskProcessor")
    
    # 创建处理器实例
    processor = ScheduledTaskProcessor()
    print("✓ 成功创建处理器实例")
    
    print("测试完成")
    
except Exception as e:
    print(f"✗ 导入失败: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复现有任务的result数据
从shipment_records和shipment_dates表中获取数据，填充到task_queue.result字段
"""

import sqlite3
import json
from datetime import datetime

def fix_existing_task_results():
    """修复现有任务的result数据"""
    print("🔧 修复现有任务的result数据...")
    
    # 连接数据库
    task_conn = sqlite3.connect('db/task_queue.db')
    shipment_conn = sqlite3.connect('db/shipment_records.db')
    
    task_cursor = task_conn.cursor()
    shipment_cursor = shipment_conn.cursor()
    
    try:
        # 查询所有已完成但没有result数据的任务
        task_cursor.execute("""
            SELECT id, tracking_number, task_stage, status
            FROM task_queue 
            WHERE status = 'completed' 
            AND (result IS NULL OR result = '')
            ORDER BY updated_at DESC
        """)
        
        tasks = task_cursor.fetchall()
        print(f"找到 {len(tasks)} 个需要修复的任务")
        
        fixed_count = 0
        
        for task_id, tracking_number, task_stage, status in tasks:
            print(f"\n🔄 处理任务: {tracking_number} ({task_stage})")
            
            # 查询对应的货运记录
            shipment_cursor.execute("""
                SELECT id, estimated_arrival_time, evidence_screenshot, carrier_company
                FROM shipment_records 
                WHERE bill_of_lading = ? OR container_number = ?
                ORDER BY updated_at DESC
                LIMIT 1
            """, (tracking_number, tracking_number))
            
            shipment_record = shipment_cursor.fetchone()
            
            if not shipment_record:
                print(f"   ❌ 未找到货运记录")
                continue
            
            shipment_id, eta, screenshot, carrier = shipment_record
            print(f"   ✅ 找到货运记录 ID: {shipment_id}")
            
            # 查询物流节点
            shipment_cursor.execute("""
                SELECT date, time, event_type, location, description, status
                FROM shipment_dates 
                WHERE shipment_id = ?
                ORDER BY date ASC
            """, (shipment_id,))
            
            dates_records = shipment_cursor.fetchall()
            
            # 构建tracking_points数据
            tracking_points = []
            for date, time, event_type, location, description, status in dates_records:
                tracking_points.append({
                    "date": date,
                    "time": time,
                    "event_type": event_type,
                    "location": location,
                    "description": description,
                    "status": status
                })
            
            print(f"   📍 找到 {len(tracking_points)} 个物流节点")
            
            # 构建result数据
            result_data = {
                "summary": f"{tracking_number} 任务完成",
                "estimated_arrival_time": eta,
                "tracking_points": tracking_points,
                "screenshots": [screenshot] if screenshot else [],
                "carrier_company": carrier,
                "shipment_id": shipment_id,
                "task_stage": task_stage,
                "fixed_at": datetime.now().isoformat()
            }
            
            # 更新task_queue表的result字段
            result_json = json.dumps(result_data, ensure_ascii=False)
            task_cursor.execute("""
                UPDATE task_queue 
                SET result = ?
                WHERE id = ?
            """, (result_json, task_id))
            
            print(f"   ✅ 已更新result数据")
            fixed_count += 1
        
        # 提交更改
        task_conn.commit()
        
        print(f"\n🎉 修复完成！共修复 {fixed_count} 个任务")
        
        # 验证修复结果
        print("\n🔍 验证修复结果...")
        task_cursor.execute("""
            SELECT COUNT(*) 
            FROM task_queue 
            WHERE status = 'completed' 
            AND result IS NOT NULL 
            AND result != ''
        """)
        
        result_count = task_cursor.fetchone()[0]
        print(f"✅ 现在有 {result_count} 个任务包含result数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        task_conn.rollback()
        return False
        
    finally:
        task_conn.close()
        shipment_conn.close()

def test_api_after_fix():
    """测试修复后的API数据"""
    print("\n🧪 测试修复后的API数据...")
    
    try:
        import requests
        
        # 使用最新的token
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiVEVTVDAwMSIsIm5hbWUiOiJcdTZkNGJcdThiZDVcdTc1MjhcdTYyMzciLCJyb2xlIjoiXHU2NjZmXHU5MDFhXHU3NTI4XHU2MjM3IiwiZXhwIjoxNzU3MTc0NDQ2LCJpYXQiOjE3NTY1Njk2NDYsInR5cGUiOiJhY2Nlc3NfdG9rZW4ifQ.Ej_Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
        
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get('http://127.0.0.1:8080/api/v1/tasks/my-tasks?limit=3', 
                              headers=headers, timeout=10)
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ API返回 {len(tasks)} 个任务")
            
            for i, task in enumerate(tasks):
                container = task.get('container_number', 'N/A')
                eta = task.get('estimated_arrival_time', 'N/A')
                nodes = len(task.get('shipment_dates', []))
                screenshot = '有' if task.get('evidence_screenshot') else '无'
                
                result = task.get('result', {})
                result_eta = result.get('estimated_arrival_time', 'N/A')
                result_nodes = len(result.get('tracking_points', []))
                result_screenshots = len(result.get('screenshots', []))
                
                print(f"\n  {i+1}. {container}")
                print(f"     直接字段 - ETA: {eta}, 节点: {nodes}, 截图: {screenshot}")
                print(f"     result字段 - ETA: {result_eta}, 节点: {result_nodes}, 截图: {result_screenshots}")
            
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复现有任务的result数据...")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 修复现有任务数据
    if fix_existing_task_results():
        print("\n✨ 数据修复成功！")
        
        # 2. 测试API
        test_api_after_fix()
        
        print("\n📝 下一步:")
        print("1. 刷新浏览器页面（使用 Ctrl+F5 强制刷新）")
        print("2. 查看任务列表中的预计到港时间、物流节点、截图数据")
        print("3. 创建新的查询任务测试完整流程")
        
    else:
        print("\n💥 数据修复失败！")

if __name__ == "__main__":
    main()

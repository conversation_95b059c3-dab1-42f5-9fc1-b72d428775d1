#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API的get_my_tasks功能
"""

import requests
import json

API_BASE = "http://127.0.0.1:8080/api/v1"

def test_api():
    """测试API功能"""
    
    # 1. 登录
    login_data = {"invite_code": "TEST001"}
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return
    
    token = response.json().get('access_token')
    print(f"✅ 登录成功")
    
    # 2. 获取任务列表
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{API_BASE}/tasks/my-tasks", headers=headers)
    
    print(f"\n📋 API响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        tasks = response.json()
        print(f"✅ 获取到 {len(tasks)} 个任务")
        
        for i, task in enumerate(tasks, 1):
            print(f"\n任务 {i}:")
            print(f"  ID: {task.get('task_id', 'N/A')[:8]}...")
            print(f"  集装箱号: {task.get('container_number', 'N/A')}")
            print(f"  承运人: {task.get('carrier_code', 'N/A')}")
            print(f"  承运人名称: {task.get('carrier_name', 'N/A')}")
            print(f"  主状态: {task.get('status', 'N/A')}")
            print(f"  货运状态: {task.get('shipment_status', 'N/A')}")
            print(f"  网页抓取: {task.get('scraping_status', 'N/A')}")
            print(f"  AI分析: {task.get('ai_status', 'N/A')}")
            print(f"  预计到港: {task.get('estimated_arrival_time', 'N/A')}")
            print(f"  截图: {task.get('evidence_screenshot', 'N/A')}")
            print(f"  物流节点: {len(task.get('shipment_dates', []))}个")
            
    else:
        print(f"❌ 获取任务失败: {response.text}")
        print(f"响应内容: {response.content}")

if __name__ == "__main__":
    test_api()

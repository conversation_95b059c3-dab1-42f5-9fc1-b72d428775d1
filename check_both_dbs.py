#!/usr/bin/env python3
"""检查两个位置的数据库"""

import sqlite3
import os

def check_databases():
    """检查根目录和db目录的数据库"""
    
    print("=== 检查根目录的数据库文件 ===")
    if os.path.exists('task_queue.db'):
        print("根目录有 task_queue.db")
        try:
            conn = sqlite3.connect('task_queue.db')
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"表: {tables}")
            
            if tables:
                # 检查是否有数据
                try:
                    cursor.execute("SELECT COUNT(*) FROM task_queue")
                    count = cursor.fetchone()[0]
                    print(f"根目录task_queue记录数: {count}")
                    
                    if count > 0:
                        cursor.execute("SELECT id, tracking_number, created_at FROM task_queue ORDER BY created_at DESC LIMIT 3")
                        rows = cursor.fetchall()
                        print("最新3条记录:")
                        for row in rows:
                            print(f"  {row}")
                except Exception as e:
                    print(f"查询根目录数据出错: {e}")
            
            conn.close()
        except Exception as e:
            print(f"连接根目录数据库出错: {e}")
    else:
        print("根目录没有 task_queue.db")

    print("\n=== 检查db目录的数据库文件 ===")
    if os.path.exists('db/task_queue.db'):
        print("db目录有 task_queue.db")
        try:
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM task_queue")
            count = cursor.fetchone()[0]
            print(f"db目录task_queue记录数: {count}")
            
            cursor.execute("SELECT id, tracking_number, created_at FROM task_queue ORDER BY created_at DESC LIMIT 3")
            rows = cursor.fetchall()
            print("最新3条记录:")
            for row in rows:
                print(f"  {row}")
            
            conn.close()
        except Exception as e:
            print(f"连接db目录数据库出错: {e}")
    else:
        print("db目录没有 task_queue.db")

if __name__ == "__main__":
    check_databases()

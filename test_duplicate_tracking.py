#!/usr/bin/env python3
"""测试重复创建相同跟踪号的任务"""

import requests
import json
import sqlite3
from datetime import datetime

def test_duplicate_tracking_number():
    """测试重复创建MEDUJ0618622任务"""
    
    # 1. 获取token
    response = requests.post(
        'http://127.0.0.1:8080/api/v1/auth/login', 
        json={'invite_code': 'TEST001'}
    )
    
    if response.status_code != 200:
        print(f"登录失败: {response.text}")
        return
    
    token_data = response.json()
    token = token_data.get('access_token')
    
    if not token:
        print("无法获取token")
        return
    
    print(f"获取token成功")
    
    # 2. 使用已存在的跟踪号MEDUJ0618622
    tracking_number = "MEDUJ0618622"
    print(f"使用跟踪号: {tracking_number}")
    
    # 3. 检查创建前的状态
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM task_queue')
    before_tasks = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM task_queue WHERE tracking_number = ?', (tracking_number,))
    before_meduj_tasks = cursor.fetchone()[0]
    conn.close()
    
    conn = sqlite3.connect('db/shipment_records.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM shipment_records')
    before_records = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM shipment_records WHERE bill_of_lading = ? OR container_number = ?', (tracking_number, tracking_number))
    before_meduj_records = cursor.fetchone()[0]
    conn.close()
    
    print(f"创建前:")
    print(f"  总任务: {before_tasks}个")
    print(f"  MEDUJ0618622任务: {before_meduj_tasks}个")
    print(f"  总记录: {before_records}个")
    print(f"  MEDUJ0618622记录: {before_meduj_records}个")
    
    # 4. 创建新任务
    task_data = {
        "container_number": tracking_number,
        "carrier_code": "MSC",
        "priority": "normal"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        "http://127.0.0.1:8080/api/v1/tasks/quick-create",
        json=task_data,
        headers=headers
    )
    
    print(f"\n创建响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        task_id = result.get('task_id')
        task_status = result.get('status')
        print(f"任务创建成功，ID: {task_id}, 状态: {task_status}")
        
        # 5. 检查创建后的状态
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM task_queue')
        after_tasks = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM task_queue WHERE tracking_number = ?', (tracking_number,))
        after_meduj_tasks = cursor.fetchone()[0]
        
        # 查找最新的MEDUJ0618622任务
        cursor.execute('SELECT id, created_at, creator_id, status FROM task_queue WHERE tracking_number = ? ORDER BY created_at DESC LIMIT 1', (tracking_number,))
        latest_task = cursor.fetchone()
        conn.close()
        
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM shipment_records')
        after_records = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM shipment_records WHERE bill_of_lading = ? OR container_number = ?', (tracking_number, tracking_number))
        after_meduj_records = cursor.fetchone()[0]
        
        # 查找最新的MEDUJ0618622记录
        cursor.execute('SELECT id, created_at, created_by FROM shipment_records WHERE bill_of_lading = ? OR container_number = ? ORDER BY created_at DESC LIMIT 1', (tracking_number, tracking_number))
        latest_record = cursor.fetchone()
        conn.close()
        
        print(f"\n创建后:")
        print(f"  总任务: {after_tasks}个 (+{after_tasks - before_tasks})")
        print(f"  MEDUJ0618622任务: {after_meduj_tasks}个 (+{after_meduj_tasks - before_meduj_tasks})")
        print(f"  总记录: {after_records}个 (+{after_records - before_records})")
        print(f"  MEDUJ0618622记录: {after_meduj_records}个 (+{after_meduj_records - before_meduj_records})")
        
        if latest_task:
            print(f"\n✅ 最新MEDUJ0618622任务: {latest_task}")
            if latest_task[0] == task_id:
                print("✅ 任务ID匹配，确实创建了新任务")
            else:
                print("❌ 任务ID不匹配，可能返回了老任务")
        else:
            print("❌ 没有找到MEDUJ0618622任务")
            
        if latest_record:
            print(f"✅ 最新MEDUJ0618622记录: {latest_record}")
        else:
            print("❌ 没有找到MEDUJ0618622记录")
            
        # 检查是否为新创建的任务（状态应该是pending而不是completed）
        if task_status == 'pending':
            print("✅ 任务状态为pending，确实是新创建的任务")
        else:
            print(f"❌ 任务状态为{task_status}，可能是返回的老任务")
    else:
        print(f"任务创建失败: {response.text}")

if __name__ == "__main__":
    test_duplicate_tracking_number()

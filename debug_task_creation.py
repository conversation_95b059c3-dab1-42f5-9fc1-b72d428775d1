#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务创建问题
"""

import sqlite3
import sys
from pathlib import Path
from datetime import datetime

def check_task_queue_table():
    """检查task_queue表结构和数据"""
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(task_queue)")
        columns = cursor.fetchall()
        
        print("📋 task_queue表结构:")
        print("=" * 60)
        for col in columns:
            default_val = f" DEFAULT {col[4]}" if col[4] else ""
            print(f"  {col[1]} ({col[2]}){default_val} - {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        # 检查最近的数据
        print(f"\n📊 最近10条任务记录:")
        print("=" * 60)
        cursor.execute("SELECT id, tracking_number, status, creator_id, created_at FROM task_queue ORDER BY created_at DESC LIMIT 10")
        rows = cursor.fetchall()
        
        if not rows:
            print("  ❌ 没有任务记录")
        else:
            for row in rows:
                print(f"  ID: {row[0]}, 号码: {row[1]}, 状态: {row[2]}, 创建者: {row[3]}, 时间: {row[4]}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 检查task_queue表失败: {e}")
        return False

def check_shipment_records_table():
    """检查shipment_records表结构和数据"""
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        
        print(f"\n📋 shipment_records表结构:")
        print("=" * 60)
        for col in columns:
            default_val = f" DEFAULT {col[4]}" if col[4] else ""
            print(f"  {col[1]} ({col[2]}){default_val} - {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
        
        # 检查最近的数据
        print(f"\n📊 最近10条货运记录:")
        print("=" * 60)
        cursor.execute("SELECT id, bill_of_lading, container_number, status, created_by, created_at FROM shipment_records ORDER BY created_at DESC LIMIT 10")
        rows = cursor.fetchall()
        
        if not rows:
            print("  ❌ 没有货运记录")
        else:
            for row in rows:
                print(f"  ID: {row[0]}, 提单号: {row[1]}, 箱号: {row[2]}, 状态: {row[3]}, 创建者: {row[4]}, 时间: {row[5]}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 检查shipment_records表失败: {e}")
        return False

def simulate_task_creation():
    """模拟任务创建过程"""
    print(f"\n🧪 模拟任务创建过程:")
    print("=" * 60)
    
    try:
        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        from shipment_manager import ShipmentManager
        from task_manager import TaskManager
        
        # 测试数据
        test_container = f"TEST{datetime.now().strftime('%m%d%H%M%S')}"
        
        print(f"  📦 测试箱号: {test_container}")
        
        # 1. 创建货运记录（会自动创建任务）
        shipment_manager = ShipmentManager()
        record_id = shipment_manager.create_shipment_record(
            container_number=test_container,
            carrier_company="TEST",
            created_by="test_user_web"
        )
        
        print(f"  ✅ 货运记录创建成功，ID: {record_id}")
        
        # 2. 检查是否有对应的任务
        task_manager = TaskManager()
        conn = task_manager._get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, status, creator_id FROM task_queue WHERE tracking_number = ? ORDER BY created_at DESC LIMIT 1', (test_container,))
        task_row = cursor.fetchone()
        conn.close()
        
        if task_row:
            print(f"  ✅ 关联任务创建成功，ID: {task_row[0]}, 状态: {task_row[1]}, 创建者: {task_row[2]}")
            return True
        else:
            print(f"  ❌ 没有找到关联的任务")
            return False
        
    except Exception as e:
        print(f"  ❌ 模拟任务创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 调试任务创建问题")
    print("=" * 60)
    
    # 检查数据库表
    task_ok = check_task_queue_table()
    shipment_ok = check_shipment_records_table()
    
    if task_ok and shipment_ok:
        # 模拟任务创建
        simulate_task_creation()
    else:
        print("\n❌ 数据库表检查失败，无法进行模拟测试")

if __name__ == "__main__":
    main()

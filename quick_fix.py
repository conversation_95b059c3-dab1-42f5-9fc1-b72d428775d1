#!/usr/bin/env python3
import sqlite3
import json

# 连接数据库
task_conn = sqlite3.connect('db/task_queue.db')
shipment_conn = sqlite3.connect('db/shipment_records.db')

task_cursor = task_conn.cursor()
shipment_cursor = shipment_conn.cursor()

# 获取物流节点数据
shipment_cursor.execute('SELECT COUNT(*) FROM shipment_dates WHERE shipment_id = 225')
node_count = shipment_cursor.fetchone()[0]

print(f'物流节点数量: {node_count}')

# 构建简单的result数据
result_data = {
    "estimated_arrival_time": "2025-08-31",
    "tracking_points": [{"count": node_count}],
    "screenshots": ["files\\2025-08\\MEDUJ0616089_20250830_232502\\final_result.png"]
}

result_json = json.dumps(result_data, ensure_ascii=False)

# 更新所有MEDUJ0616089任务
task_cursor.execute("""
    UPDATE task_queue 
    SET result = ?
    WHERE tracking_number = 'MEDUJ0616089'
    AND status = 'completed'
""", (result_json,))

affected = task_cursor.rowcount
print(f'更新了 {affected} 个任务')

task_conn.commit()
task_conn.close()
shipment_conn.close()

print('修复完成！')

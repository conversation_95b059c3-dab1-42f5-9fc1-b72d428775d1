#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_meduj_task():
    """专门测试MEDUJ0616089任务的API响应"""
    print("🔍 测试MEDUJ0616089任务的API响应...")
    
    # API配置
    base_url = "http://localhost:8080"
    invite_code = "TEST001"
    
    try:
        # 1. 登录获取token
        login_response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json={"invite_code": invite_code}
        )
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
            
        token = login_response.json()["access_token"]
        print("✅ 登录成功")
        
        # 2. 获取用户任务，使用正确的端点
        headers = {"Authorization": f"Bearer {token}"}
        tasks_response = requests.get(
            f"{base_url}/api/v1/tasks/my-tasks?limit=20",  # 增加limit来看更多任务
            headers=headers
        )
        
        print(f"\n📋 API响应状态码: {tasks_response.status_code}")
        
        if tasks_response.status_code == 200:
            # 直接获取任务列表，API返回List[TaskResponse]
            tasks = tasks_response.json()
            print(f"✅ 获取到 {len(tasks)} 个任务")
            
            # 查找MEDUJ0616089任务
            meduj_task = None
            for i, task in enumerate(tasks, 1):
                print(f"\n任务 {i}:")
                # 先打印任务的所有字段
                print(f"  任务数据: {json.dumps(task, indent=4, ensure_ascii=False)}")
                
                # 安全地获取字段，使用正确的字段名
                task_id = task.get('task_id', task.get('id', 'N/A'))
                tracking_number = task.get('container_number', 'N/A')  # 修正字段名
                carrier = task.get('carrier_code', 'N/A')  # 修正字段名
                main_status = task.get('status', task.get('main_status', 'N/A'))  # 修正字段名
                created_at = task.get('created_at', 'N/A')
                updated_at = task.get('updated_at', 'N/A')
                
                print(f"  ID: {str(task_id)[:8]}...")
                print(f"  集装箱号: {tracking_number}")
                print(f"  承运人: {carrier}")
                print(f"  主状态: {main_status}")
                print(f"  创建时间: {created_at}")
                print(f"  更新时间: {updated_at}")
                
                if tracking_number == 'MEDUJ0616089':
                    meduj_task = task
                    print("  🎯 这是MEDUJ0616089任务!")
            
            if meduj_task:
                print(f"\n🎯 找到MEDUJ0616089任务详情:")
                print(f"  承运人名称: {meduj_task.get('carrier_name')}")
                print(f"  货运状态: {meduj_task.get('shipment_status')}")
                print(f"  网页抓取: {meduj_task.get('scraping_status')}")
                print(f"  AI分析: {meduj_task.get('ai_status')}")
                print(f"  预计到港: {meduj_task.get('estimated_arrival')}")
                print(f"  截图: {meduj_task.get('evidence_screenshot')}")
                print(f"  物流节点: {len(meduj_task.get('shipment_dates', []))}个")
            else:
                print("\n❌ 没有找到MEDUJ0616089任务")
                
        else:
            print(f"❌ 获取任务失败: {tasks_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_meduj_task()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的result数据
"""

import sqlite3
import json

def check_result_data():
    """检查数据库中的result数据"""
    print("🔍 检查数据库中的result数据...")
    
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    
    # 查询最近的任务及其result数据
    cursor.execute('''
        SELECT tracking_number, task_stage, status, result, result_summary 
        FROM task_queue 
        WHERE result IS NOT NULL OR result_summary IS NOT NULL
        ORDER BY updated_at DESC 
        LIMIT 3
    ''')
    
    tasks = cursor.fetchall()
    print(f'找到 {len(tasks)} 个有结果数据的任务:')
    
    for i, (tracking_number, task_stage, status, result, result_summary) in enumerate(tasks):
        print(f'\n{i+1}. {tracking_number} ({task_stage}) - {status}')
        
        if result:
            print(f'   result长度: {len(result)}')
            print(f'   result前100字符: {result[:100]}...')
            
            try:
                result_data = json.loads(result)
                print(f'   result解析成功，包含字段: {list(result_data.keys())}')
                
                if 'tracking_points' in result_data:
                    tracking_points = result_data['tracking_points']
                    print(f'   tracking_points类型: {type(tracking_points)}')
                    if isinstance(tracking_points, list):
                        print(f'   tracking_points长度: {len(tracking_points)}')
                        if tracking_points:
                            print(f'   第一个节点: {tracking_points[0]}')
                    else:
                        print(f'   tracking_points值: {tracking_points}')
                
                if 'screenshots' in result_data:
                    screenshots = result_data['screenshots']
                    print(f'   screenshots类型: {type(screenshots)}')
                    print(f'   screenshots值: {screenshots}')
                
                if 'estimated_arrival_time' in result_data:
                    eta = result_data['estimated_arrival_time']
                    print(f'   estimated_arrival_time: {eta}')
                    
            except Exception as e:
                print(f'   result解析失败: {e}')
        else:
            print(f'   result: 无')
        
        if result_summary:
            print(f'   result_summary前100字符: {result_summary[:100]}...')
        else:
            print(f'   result_summary: 无')
    
    conn.close()

if __name__ == "__main__":
    check_result_data()

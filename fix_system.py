#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统修复脚本 - 修复数据获取、截图、物流节点问题
"""

import sqlite3
import os
import sys
from datetime import datetime

def reset_stuck_tasks():
    """重置卡住的任务"""
    print("🔧 重置卡住的任务...")
    
    db_path = 'db/task_queue.db'
    if not os.path.exists(db_path):
        print("❌ 任务队列数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 重置长时间处理中的任务
        cursor.execute('''
            UPDATE task_queue 
            SET status = 'pending', 
                started_at = NULL,
                error_message = '系统重置 - 任务卡住超时'
            WHERE status = 'processing' 
            AND datetime(started_at) < datetime('now', '-30 minutes')
        ''')
        
        reset_count = cursor.rowcount
        
        # 重置失败的AI分析任务
        cursor.execute('''
            UPDATE task_queue 
            SET status = 'pending',
                started_at = NULL,
                completed_at = NULL,
                error_message = NULL
            WHERE status = 'failed' 
            AND task_stage = 'ai_analysis'
            AND datetime(created_at) > datetime('now', '-24 hours')
        ''')
        
        ai_reset_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"✅ 重置了 {reset_count} 个卡住的任务")
        print(f"✅ 重置了 {ai_reset_count} 个失败的AI分析任务")
        
    except Exception as e:
        print(f"❌ 重置任务失败: {e}")

def check_and_fix_ai_config():
    """检查并修复AI配置"""
    print("\n🔧 检查AI配置...")
    
    db_path = 'db/ai_config.db'
    if not os.path.exists(db_path):
        print("❌ AI配置数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查活跃提供商
        cursor.execute('SELECT * FROM ai_providers WHERE is_active = 1')
        providers = cursor.fetchall()
        
        if not providers:
            print("❌ 没有活跃的AI提供商")
            return False
        
        provider = providers[0]
        api_key = provider['api_key']
        
        print(f"当前提供商: {provider['name']}")
        print(f"API密钥: {api_key[:20] if api_key else 'None'}...")
        print(f"基础URL: {provider['base_url']}")
        
        # 检查API密钥格式
        if not api_key or len(api_key) < 20:
            print("⚠️  API密钥可能无效")
            print("请检查API密钥是否正确设置")
            return False
        
        # 检查文本模型是否启用
        cursor.execute('''
            SELECT am.*, ap.name as provider_name 
            FROM ai_models am 
            JOIN ai_providers ap ON am.provider_id = ap.id 
            WHERE am.model_type = 'text' AND am.is_enabled = 1
        ''')
        
        text_models = cursor.fetchall()
        if not text_models:
            print("❌ 文本模型未启用")
            return False
        
        print(f"✅ 文本模型已启用: {text_models[0]['provider_name']}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查AI配置失败: {e}")
        return False

def create_test_task():
    """创建测试任务"""
    print("\n🔧 创建测试任务...")
    
    try:
        sys.path.append('.')
        from task_manager import TaskManager
        
        task_manager = TaskManager()
        
        # 创建一个简单的测试任务
        test_tracking = "TEST123456"
        task_id = task_manager.create_task(
            tracking_number=test_tracking,
            task_type="bill_of_lading",
            creator_id="system",
            creator_name="系统测试",
            carrier="MSC",
            priority=1,
            remarks="系统修复测试任务"
        )
        
        print(f"✅ 创建测试任务: {task_id}")
        return task_id
        
    except Exception as e:
        print(f"❌ 创建测试任务失败: {e}")
        return None

def check_storage_directories():
    """检查并创建存储目录"""
    print("\n🔧 检查存储目录...")
    
    required_dirs = [
        'storage',
        'screenshots', 
        'data',
        'logs',
        'temp'
    ]
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            try:
                os.makedirs(dir_name, exist_ok=True)
                print(f"✅ 创建目录: {dir_name}")
            except Exception as e:
                print(f"❌ 创建目录失败 {dir_name}: {e}")
        else:
            print(f"✅ 目录存在: {dir_name}")

def cleanup_old_files():
    """清理旧文件"""
    print("\n🔧 清理临时文件...")
    
    temp_patterns = [
        'temp_*.png',
        'temp_*.html',
        'error_*.png'
    ]
    
    import glob
    cleaned_count = 0
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                cleaned_count += 1
            except Exception as e:
                print(f"⚠️  无法删除 {file}: {e}")
    
    print(f"✅ 清理了 {cleaned_count} 个临时文件")

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "=" * 50)
    print("📋 修复完成，后续步骤:")
    print("1. 启动应用程序: python app.py")
    print("2. 检查AI配置: 工具 -> AI配置管理")
    print("3. 验证API密钥是否有效")
    print("4. 查看任务状态: 工具 -> 任务状态查看器")
    print("5. 尝试添加新的查询任务")
    print("6. 如果问题持续，检查网络连接和API服务状态")
    print("\n⚠️  重要提醒:")
    print("- 确保API密钥有效且有足够余额")
    print("- 检查网络连接是否正常")
    print("- 如果使用代理，确保代理设置正确")

def quick_fix():
    """快速修复 - 重置任务状态"""
    print("🚀 快速修复：重置任务状态...")

    db_path = 'db/task_queue.db'
    if not os.path.exists(db_path):
        print("❌ 任务队列数据库不存在")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 重置所有processing状态的任务
        cursor.execute('''
            UPDATE task_queue
            SET status = 'pending',
                started_at = NULL,
                error_message = '系统重置'
            WHERE status = 'processing'
        ''')

        processing_reset = cursor.rowcount

        # 重置最近失败的任务
        cursor.execute('''
            UPDATE task_queue
            SET status = 'pending',
                started_at = NULL,
                completed_at = NULL,
                error_message = NULL
            WHERE status = 'failed'
            AND datetime(created_at) > datetime('now', '-6 hours')
        ''')

        failed_reset = cursor.rowcount

        conn.commit()
        conn.close()

        print(f"✅ 重置了 {processing_reset} 个处理中的任务")
        print(f"✅ 重置了 {failed_reset} 个失败的任务")
        print("\n现在请:")
        print("1. 启动应用程序: python app.py")
        print("2. 检查AI配置中的API密钥")
        print("3. 尝试添加新的查询任务")

    except Exception as e:
        print(f"❌ 快速修复失败: {e}")

def main():
    """主修复流程"""
    print("🚀 开始系统修复...")
    print("=" * 50)

    # 1. 重置卡住的任务
    reset_stuck_tasks()

    # 2. 检查AI配置
    ai_ok = check_and_fix_ai_config()

    # 3. 检查存储目录
    check_storage_directories()

    # 4. 清理临时文件
    cleanup_old_files()

    # 5. 创建测试任务（如果AI配置正常）
    if ai_ok:
        create_test_task()

    # 6. 显示后续步骤
    show_next_steps()

    print("\n✅ 系统修复完成!")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_fix()
    else:
        main()

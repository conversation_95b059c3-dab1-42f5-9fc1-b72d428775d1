#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理相关的数据模型
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime

class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "超级管理员"
    VIP = "VIP用户" 
    SENIOR = "高级用户"
    NORMAL = "普通用户"

class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"

class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

# ==================== 请求模型 ====================

class LoginRequest(BaseModel):
    """登录请求"""
    invite_code: str = Field(..., description="邀请码", min_length=1, max_length=20)

class LogoutRequest(BaseModel):
    """登出请求 - 目前为空，将来可扩展"""
    pass

class CarrierValidationRequest(BaseModel):
    """承运人校验请求"""
    tracking_number: str = Field(..., description="提单号/箱号", min_length=1, max_length=50)

class BatchValidationRequest(BaseModel):
    """批量校验请求"""
    tracking_numbers: List[str] = Field(..., description="提单号/箱号列表", min_items=1, max_items=200)
    deduplicate: bool = Field(True, description="是否去重")
    
class TaskCreateRequest(BaseModel):
    """单个任务创建请求"""
    container_number: str = Field(..., description="集装箱号/提单号", min_length=1, max_length=50)
    carrier_code: Optional[str] = Field(None, description="承运人代码")
    priority: TaskPriority = Field(TaskPriority.NORMAL, description="任务优先级")
    callback_url: Optional[str] = Field(None, description="回调URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加元数据")

class BatchTaskCreateRequest(BaseModel):
    """批量任务创建请求"""
    tasks: List[TaskCreateRequest] = Field(..., description="任务列表", min_items=1, max_items=200)
    
# ==================== 响应模型 ====================

class UserInfo(BaseModel):
    """用户信息"""
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="用户姓名")
    role: UserRole = Field(..., description="用户角色")
    avatar: str = Field(..., description="头像字符")
    status: UserStatus = Field(..., description="用户状态")
    permissions: List[str] = Field(default=[], description="用户权限列表")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")
    query_count: int = Field(default=0, description="查询次数")
    success_rate: float = Field(default=0.0, description="成功率")

class LoginResponse(BaseModel):
    """登录响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    access_token: Optional[str] = Field(None, description="访问令牌")
    user_info: Optional[UserInfo] = Field(None, description="用户信息")
    expires_in: Optional[int] = Field(None, description="令牌有效期（秒）")

class CarrierInfo(BaseModel):
    """承运人信息"""
    code: str = Field(..., description="承运人代码")
    name: str = Field(..., description="承运人名称")
    full_name: str = Field(..., description="承运人全称")
    url: str = Field(..., description="官网URL")
    tracking_url: Optional[str] = Field(None, description="跟踪URL")

class ValidationResult(BaseModel):
    """校验结果"""
    tracking_number: str = Field(..., description="提单号/箱号")
    is_valid: bool = Field(..., description="是否有效")
    carrier_info: Optional[CarrierInfo] = Field(None, description="承运人信息")
    error_message: Optional[str] = Field(None, description="错误信息")

class CarrierValidationResponse(BaseModel):
    """承运人校验响应"""
    success: bool = Field(..., description="是否成功")
    result: ValidationResult = Field(..., description="校验结果")

class BatchValidationResponse(BaseModel):
    """批量校验响应"""
    success: bool = Field(..., description="是否成功") 
    results: List[ValidationResult] = Field(..., description="校验结果列表")
    summary: Dict[str, int] = Field(..., description="统计摘要")
    
class ShipmentDates(BaseModel):
    """货运日期信息"""
    date_type: str = Field(..., description="日期类型")
    planned_date: Optional[str] = Field(None, description="计划日期")
    actual_date: Optional[str] = Field(None, description="实际日期")
    status: Optional[str] = Field(None, description="状态")
    location: Optional[str] = Field(None, description="地点")

class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str = Field(..., description="任务ID")
    container_number: str = Field(..., description="集装箱号")
    carrier_code: Optional[str] = Field(None, description="承运人代码")
    carrier_name: Optional[str] = Field(None, description="承运人名称")
    status: str = Field(..., description="任务状态")
    shipment_status: Optional[str] = Field(None, description="货运状态")
    priority: str = Field(..., description="任务优先级")
    user_id: str = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    
    # 货运记录详细信息
    estimated_arrival_time: Optional[str] = Field(None, description="预计到港时间")
    evidence_screenshot: Optional[str] = Field(None, description="佐证截图路径")
    shipment_dates: List[ShipmentDates] = Field(default=[], description="物流节点")
    ai_analysis_result: Optional[Dict[str, Any]] = Field(None, description="AI分析结果")
    
    # 任务执行状态
    scraping_status: Optional[str] = Field(None, description="网页抓取状态")
    ai_status: Optional[str] = Field(None, description="AI分析状态")
    
class BatchTaskCreateResponse(BaseModel):
    """批量任务创建响应"""
    success: bool = Field(..., description="是否成功")
    created_tasks: List[TaskResponse] = Field(..., description="成功创建的任务")
    failed_tasks: List[Dict[str, Any]] = Field(default=[], description="创建失败的任务")
    summary: Dict[str, int] = Field(..., description="统计摘要")

class UserStatsResponse(BaseModel):
    """用户统计响应"""
    total_queries: int = Field(..., description="总查询数")
    successful_queries: int = Field(..., description="成功查询数")
    failed_queries: int = Field(..., description="失败查询数")
    success_rate: float = Field(..., description="成功率")
    today_queries: int = Field(..., description="今日查询数")
    this_week_queries: int = Field(..., description="本周查询数")
    this_month_queries: int = Field(..., description="本月查询数")

class UserPermission(BaseModel):
    """用户权限"""
    permission: str = Field(..., description="权限名称")
    description: str = Field(..., description="权限描述")
    granted: bool = Field(..., description="是否授予")

class UserPermissionsResponse(BaseModel):
    """用户权限响应"""
    user_id: str = Field(..., description="用户ID")
    permissions: List[UserPermission] = Field(..., description="权限列表")

class APIResponse(BaseModel):
    """通用API响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
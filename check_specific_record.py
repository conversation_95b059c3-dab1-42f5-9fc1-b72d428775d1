#!/usr/bin/env python3
import sqlite3

conn = sqlite3.connect('db/shipment_records.db')
cursor = conn.cursor()

# 查看MEDUJ0616089的记录
cursor.execute('SELECT * FROM shipment_records WHERE bill_of_lading = ? ORDER BY updated_at DESC LIMIT 1', ('MEDUJ0616089',))
record = cursor.fetchone()
print('货运记录:', record)

# 查看对应的物流节点
if record:
    shipment_id = record[0]
    cursor.execute('SELECT * FROM shipment_dates WHERE shipment_id = ? LIMIT 3', (shipment_id,))
    dates = cursor.fetchall()
    print(f'物流节点 (前3个): {dates}')

conn.close()

#!/usr/bin/env python3
import sqlite3
from datetime import datetime

try:
    conn = sqlite3.connect('db/task_queue.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, task_name, tracking_number, status, task_stage, created_at, remarks 
        FROM task_queue 
        ORDER BY created_at DESC 
        LIMIT 10
    ''')
    rows = cursor.fetchall()
    
    if rows:
        print('最近的任务记录:')
        print('=' * 120)
        for row in rows:
            task_id = row[0][:8] + '...' if row[0] else 'N/A'
            task_name = row[1] or 'N/A'
            tracking_number = row[2] or 'N/A'
            status = row[3] or 'N/A'
            task_stage = row[4] or 'N/A'
            created_at = row[5] or 'N/A'
            remarks = (row[6][:50] + '...') if row[6] else '无'
            
            print(f'ID: {task_id:12} | 任务名: {task_name:30} | 跟踪号: {tracking_number:15}')
            print(f'状态: {status:10} | 阶段: {task_stage:12} | 创建时间: {created_at}')
            print(f'备注: {remarks}')
            print('-' * 120)
    else:
        print('没有找到任务记录')
    
    # 检查货运记录
    print('\n检查货运记录:')
    try:
        conn2 = sqlite3.connect('db/shipment_records.db')
        cursor2 = conn2.cursor()
        cursor2.execute('''
            SELECT id, container_number, status, estimated_arrival_time, created_at, updated_at
            FROM shipment_records 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        shipment_rows = cursor2.fetchall()
        
        if shipment_rows:
            print('最近的货运记录:')
            print('=' * 120)
            for row in shipment_rows:
                print(f'ID: {row[0]} | 箱号: {row[1]} | 状态: {row[2]} | ETA: {row[3]} | 创建: {row[4]} | 更新: {row[5]}')
        else:
            print('没有找到货运记录')
        
        conn2.close()
    except Exception as e:
        print(f'查询货运记录失败: {e}')
    
    conn.close()
    
except Exception as e:
    print(f'查询失败: {e}')
    import traceback
    traceback.print_exc()

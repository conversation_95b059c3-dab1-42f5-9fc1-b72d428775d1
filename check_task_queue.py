#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查任务数据库中的任务
"""

import sqlite3
from datetime import datetime

def check_task_queue():
    """检查task_queue数据库"""
    print("检查task_queue数据库...")
    
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 查看最近的任务
        cursor.execute("""
            SELECT id, tracking_number, creator_id, status, created_at, task_stage
            FROM task_queue 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        rows = cursor.fetchall()
        print(f"找到 {len(rows)} 个最近任务:")
        
        for row in rows:
            task_id, tracking_number, creator_id, status, created_at, task_stage = row
            print(f"  任务ID: {task_id}")
            print(f"  跟踪号: {tracking_number}")
            print(f"  创建者: {creator_id}")
            print(f"  状态: {status}")
            print(f"  阶段: {task_stage}")
            print(f"  创建时间: {created_at}")
            print(f"  ---")
        
        # 查看TEST001用户的任务
        cursor.execute("""
            SELECT COUNT(*) FROM task_queue WHERE creator_id = 'TEST001'
        """)
        test_count = cursor.fetchone()[0]
        print(f"\nTEST001用户的任务数量: {test_count}")
        
        if test_count > 0:
            cursor.execute("""
                SELECT id, tracking_number, status, created_at, task_stage
                FROM task_queue 
                WHERE creator_id = 'TEST001'
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            rows = cursor.fetchall()
            print(f"TEST001用户的最近任务:")
            for row in rows:
                print(f"  {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_task_queue()

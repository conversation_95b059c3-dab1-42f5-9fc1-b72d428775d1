#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断系统问题 - 数据获取、截图、物流节点问题排查
"""

import sqlite3
import os
import sys
from datetime import datetime

def check_ai_config():
    """检查AI配置"""
    print("🔍 检查AI配置...")
    
    db_path = 'db/ai_config.db'
    if not os.path.exists(db_path):
        print("❌ AI配置数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 检查提供商
        cursor.execute('SELECT * FROM ai_providers WHERE is_active = 1')
        active_providers = cursor.fetchall()
        
        if not active_providers:
            print("❌ 没有活跃的AI提供商")
            return False
        
        provider = active_providers[0]
        api_key = provider['api_key']
        
        if not api_key or api_key == "在此处填入您的真实API密钥" or len(api_key) < 10:
            print(f"❌ API密钥无效: {api_key[:20] if api_key else 'None'}...")
            return False
        
        # 检查模型配置
        cursor.execute('SELECT * FROM ai_models WHERE is_enabled = 1 AND model_type = "text"')
        text_models = cursor.fetchall()
        
        if not text_models:
            print("❌ 文本模型未启用")
            return False
        
        print("✅ AI配置正常")
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查AI配置失败: {e}")
        return False

def check_task_queue():
    """检查任务队列状态"""
    print("\n🔍 检查任务队列...")
    
    db_path = 'db/task_queue.db'
    if not os.path.exists(db_path):
        print("❌ 任务队列数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 统计任务状态
        cursor.execute('SELECT status, COUNT(*) as count FROM task_queue GROUP BY status')
        status_counts = cursor.fetchall()
        
        print("任务状态分布:")
        for row in status_counts:
            print(f"  {row['status']}: {row['count']} 个")
        
        # 检查最近的失败任务
        cursor.execute('''
            SELECT tracking_number, task_stage, status, error_message, created_at
            FROM task_queue 
            WHERE status = 'failed' 
            ORDER BY created_at DESC 
            LIMIT 5
        ''')
        
        failed_tasks = cursor.fetchall()
        if failed_tasks:
            print("\n最近失败的任务:")
            for task in failed_tasks:
                print(f"  {task['tracking_number']} ({task['task_stage']}): {task['error_message'][:100] if task['error_message'] else 'No error message'}...")
        
        # 检查卡住的任务
        cursor.execute('''
            SELECT tracking_number, task_stage, status, created_at, started_at
            FROM task_queue 
            WHERE status = 'processing' 
            AND datetime(started_at) < datetime('now', '-1 hour')
        ''')
        
        stuck_tasks = cursor.fetchall()
        if stuck_tasks:
            print("\n可能卡住的任务:")
            for task in stuck_tasks:
                print(f"  {task['tracking_number']} ({task['task_stage']}): 运行超过1小时")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查任务队列失败: {e}")

def check_file_storage():
    """检查文件存储"""
    print("\n🔍 检查文件存储...")
    
    storage_dirs = ['storage', 'screenshots', 'data']
    for dir_name in storage_dirs:
        if os.path.exists(dir_name):
            file_count = sum(len(files) for _, _, files in os.walk(dir_name))
            print(f"  {dir_name}: {file_count} 个文件")
        else:
            print(f"  {dir_name}: 目录不存在")

def test_ai_client():
    """测试AI客户端"""
    print("\n🔍 测试AI客户端...")
    
    try:
        # 尝试导入和初始化AI客户端
        sys.path.append('.')
        from ai.client import get_ai_client
        
        client = get_ai_client()
        print("✅ AI客户端初始化成功")
        
        # 测试获取模型
        try:
            text_model = client.get_text_model()
            print(f"✅ 文本模型: {text_model}")
        except Exception as e:
            print(f"❌ 获取文本模型失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AI客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖"""
    print("\n🔍 检查依赖...")
    
    required_modules = [
        'sqlite3', 'openai', 'playwright', 'PySide6', 'bs4'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 未安装")

def main():
    """主函数"""
    print("🚀 开始系统诊断...")
    print("=" * 50)
    
    # 检查各个组件
    ai_ok = check_ai_config()
    check_task_queue()
    check_file_storage()
    check_dependencies()
    ai_client_ok = test_ai_client()
    
    print("\n" + "=" * 50)
    print("📋 诊断总结:")
    
    if not ai_ok:
        print("❌ AI配置有问题 - 这是主要问题")
        print("解决方案:")
        print("  1. 打开应用程序")
        print("  2. 进入 工具 -> AI配置管理")
        print("  3. 检查API密钥是否正确设置")
        print("  4. 确保文本模型已启用")
    elif not ai_client_ok:
        print("❌ AI客户端初始化失败")
        print("解决方案:")
        print("  1. 检查网络连接")
        print("  2. 验证API密钥是否有效")
        print("  3. 检查API服务是否可用")
    else:
        print("✅ 基础配置正常")
        print("可能的问题:")
        print("  1. 任务处理器未启动")
        print("  2. 网络连接问题")
        print("  3. 网站结构变化导致抓取失败")
        print("  4. 检查应用程序的任务状态查看器")

if __name__ == "__main__":
    main()

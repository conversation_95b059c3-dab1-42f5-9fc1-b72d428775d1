#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_shipment_structure():
    """检查shipment_records数据库结构"""
    print("🔍 检查shipment_records数据库结构...")
    
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(shipment_records)")
        columns = cursor.fetchall()
        
        print(f"\n📊 shipment_records表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_shipment_structure()

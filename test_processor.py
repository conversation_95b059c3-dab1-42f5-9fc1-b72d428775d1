#!/usr/bin/env python3
"""
测试任务处理器启动
"""

import sys
import time
import traceback

def test_processor():
    try:
        print("=== 测试任务处理器启动 ===")
        
        # 导入处理器
        from scheduled_task_processor import ScheduledTaskProcessor
        print("✓ 成功导入 ScheduledTaskProcessor")
        
        # 创建处理器
        processor = ScheduledTaskProcessor(
            scraping_interval=10,
            ai_interval=5,
            status_update_interval=30,
            max_scraping_tasks=2,
            max_ai_tasks=3
        )
        print("✓ 成功创建处理器实例")
        
        # 启动处理器
        processor.start()
        print("✓ 处理器已启动")
        
        # 等待几秒钟检查状态
        time.sleep(3)
        
        print("✓ 处理器运行正常")
        
        # 停止处理器
        processor.stop()
        print("✓ 处理器已停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_processor()
    print(f"\n测试结果: {'成功' if success else '失败'}")

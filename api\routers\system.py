#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统状态与运行信息API
"""

from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pathlib import Path
import json
import os
import sqlite3
from datetime import datetime, timedelta

router = APIRouter()


@router.get("/system/processor-status")
async def get_processor_status():
    """获取后台处理器（定时任务处理器）的最新心跳/运行状态"""
    hb_path = Path("db") / "processor_heartbeat.json"
    if not hb_path.exists():
        return JSONResponse(
            {
                "running": False,
                "status": "unknown",
                "message": "未找到心跳文件，处理器可能未运行",
            }
        )

    try:
        with hb_path.open("r", encoding="utf-8") as f:
            data = json.load(f)
    except Exception as e:
        return JSONResponse(
            {
                "running": False,
                "status": "error",
                "message": f"读取心跳失败: {e}",
            }
        )

    # 判断心跳是否过期（默认90秒内视为在线）
    try:
        ts = datetime.fromisoformat(data.get("timestamp"))
        delta = datetime.now() - ts
        stale = delta > timedelta(seconds=90)
    except Exception:
        stale = True

    return {
        "running": bool(data.get("running") and not stale),
        "status": "online" if not stale and data.get("running") else "offline",
        "last_heartbeat": data.get("timestamp"),
        "active": data.get("active", {}),
        "intervals": data.get("intervals", {}),
        "stats": data.get("stats", {}),
        "raw": data,
    }


@router.get("/system/task-stats")
async def get_task_stats():
    """获取任务统计信息"""
    try:
        conn = sqlite3.connect('db/task_queue.db')
        cursor = conn.cursor()
        
        # 总任务统计
        cursor.execute("SELECT status, COUNT(*) FROM task_queue GROUP BY status")
        status_stats = dict(cursor.fetchall())
        
        # 任务阶段统计
        cursor.execute("SELECT task_stage, status, COUNT(*) FROM task_queue GROUP BY task_stage, status")
        stage_stats = {}
        for stage, status, count in cursor.fetchall():
            if stage not in stage_stats:
                stage_stats[stage] = {}
            stage_stats[stage][status] = count
        
        # 今日任务统计
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT status, COUNT(*) FROM task_queue 
            WHERE DATE(created_at) = ? 
            GROUP BY status
        """, (today,))
        today_stats = dict(cursor.fetchall())
        
        # 最近24小时的任务
        twenty_four_hours_ago = (datetime.now() - timedelta(hours=24)).isoformat()
        cursor.execute("""
            SELECT status, COUNT(*) FROM task_queue 
            WHERE created_at >= ? 
            GROUP BY status
        """, (twenty_four_hours_ago,))
        recent_stats = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            "total_stats": status_stats,
            "stage_stats": stage_stats,
            "today_stats": today_stats,
            "last_24h_stats": recent_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"获取任务统计失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@router.get("/system/shipment-stats") 
async def get_shipment_stats():
    """获取货运记录统计信息"""
    try:
        conn = sqlite3.connect('db/shipment_records.db')
        cursor = conn.cursor()
        
        # 货运记录状态统计
        cursor.execute("SELECT status, COUNT(*) FROM shipment_records GROUP BY status")
        shipment_status_stats = dict(cursor.fetchall())
        
        # 承运人统计
        cursor.execute("SELECT carrier_company, COUNT(*) FROM shipment_records GROUP BY carrier_company")
        carrier_stats = dict(cursor.fetchall())
        
        # 今日新增
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("SELECT COUNT(*) FROM shipment_records WHERE DATE(created_at) = ?", (today,))
        today_new = cursor.fetchone()[0]
        
        # 有截图的记录数量
        cursor.execute("SELECT COUNT(*) FROM shipment_records WHERE evidence_screenshot IS NOT NULL AND evidence_screenshot != ''")
        with_screenshot = cursor.fetchone()[0]
        
        # 有物流节点的记录数量
        cursor.execute("""
            SELECT COUNT(DISTINCT shipment_id) FROM shipment_dates
        """)
        with_dates = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "status_stats": shipment_status_stats,
            "carrier_stats": carrier_stats,
            "today_new": today_new,
            "with_screenshot": with_screenshot,
            "with_dates": with_dates,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": f"获取货运统计失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )

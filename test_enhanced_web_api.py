#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的Web API - 验证任务列表数据同步
"""

import requests
import json
from datetime import datetime

API_BASE = "http://127.0.0.1:8080/api/v1"

def test_login():
    """测试登录"""
    print("=" * 50)
    print("测试登录...")
    
    login_data = {
        "invite_code": "TEST001"  # 使用有效的测试邀请码
    }
    
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    print(f"登录状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"登录成功: {result.get('message')}")
        if result.get('access_token'):
            print(f"获得令牌: {result['access_token'][:20]}...")
            return result['access_token']
        else:
            print(f"未获得访问令牌，响应: {result}")
    else:
        print(f"登录失败: {response.text}")
    return None

def test_my_tasks(token):
    """测试获取我的任务列表"""
    print("=" * 50)
    print("测试获取任务列表...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE}/tasks/my-tasks", headers=headers)
    print(f"获取任务列表状态码: {response.status_code}")
    
    if response.status_code == 200:
        tasks = response.json()
        print(f"获取到 {len(tasks)} 个任务")
        
        for i, task in enumerate(tasks[:3]):  # 只显示前3个任务
            print(f"\n任务 {i+1}:")
            print(f"  任务ID: {task.get('task_id')}")
            print(f"  跟踪号: {task.get('container_number')}")
            print(f"  承运人: {task.get('carrier_name', task.get('carrier_code'))}")
            print(f"  任务状态: {task.get('status')}")
            print(f"  货运状态: {task.get('shipment_status', '无')}")
            print(f"  网页抓取状态: {task.get('scraping_status', '无')}")
            print(f"  AI分析状态: {task.get('ai_status', '无')}")
            print(f"  预计到港时间: {task.get('estimated_arrival_time', '无')}")
            print(f"  佐证截图: {'有' if task.get('evidence_screenshot') else '无'}")
            print(f"  物流节点数: {len(task.get('shipment_dates', []))}")
            
            # 显示物流节点详情
            if task.get('shipment_dates'):
                print(f"  物流节点详情:")
                for date in task.get('shipment_dates', [])[:3]:  # 最多显示3个节点
                    print(f"    - {date.get('date_type')}: {date.get('actual_date') or date.get('planned_date', '未知')} ({date.get('location', '无地点')})")
            
            print(f"  创建时间: {task.get('created_at')}")
            print(f"  更新时间: {task.get('updated_at')}")
    else:
        print(f"获取任务列表失败: {response.text}")

def test_system_status():
    """测试系统状态"""
    print("=" * 50)
    print("测试系统状态...")
    
    # 测试处理器状态
    response = requests.get(f"{API_BASE}/system/processor-status")
    print(f"处理器状态码: {response.status_code}")
    if response.status_code == 200:
        status = response.json()
        print(f"处理器运行状态: {'运行中' if status.get('running') else '停止'}")
        print(f"处理器状态: {status.get('status')}")
        print(f"最后心跳: {status.get('last_heartbeat', '无')}")
        if status.get('stats'):
            print(f"统计信息: {status.get('stats')}")
    
    print("\n" + "-" * 30)
    
    # 测试任务统计
    response = requests.get(f"{API_BASE}/system/task-stats")
    print(f"任务统计状态码: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"任务总统计: {stats.get('total_stats', {})}")
        print(f"今日任务: {stats.get('today_stats', {})}")
        print(f"最近24小时: {stats.get('last_24h_stats', {})}")
        if stats.get('stage_stats'):
            print("分阶段统计:")
            for stage, stage_data in stats.get('stage_stats', {}).items():
                print(f"  {stage}: {stage_data}")
    
    print("\n" + "-" * 30)
    
    # 测试货运统计
    response = requests.get(f"{API_BASE}/system/shipment-stats")
    print(f"货运统计状态码: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"货运状态统计: {stats.get('status_stats', {})}")
        print(f"今日新增: {stats.get('today_new', 0)}")
        print(f"有截图记录: {stats.get('with_screenshot', 0)}")
        print(f"有物流节点记录: {stats.get('with_dates', 0)}")
        if stats.get('carrier_stats'):
            print("承运人统计:")
            for carrier, count in list(stats.get('carrier_stats', {}).items())[:5]:
                print(f"  {carrier}: {count}")

def main():
    """主函数"""
    print("开始测试增强的Web API...")
    print(f"测试时间: {datetime.now()}")
    
    # 先测试登录
    token = test_login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    # 测试任务列表
    test_my_tasks(token)
    
    # 测试系统状态
    test_system_status()
    
    print("=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
